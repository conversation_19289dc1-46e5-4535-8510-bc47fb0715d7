package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.List;

import org.maalischool.ms.accounting.model.enums.AccountingCategory;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class BalanceSheetSectionDto {
    private AccountingCategory category;
    private List<BalanceSheetAccountDto> accounts;
    private BigDecimal totalDebit;
    private BigDecimal totalCredit;
    private BigDecimal totalBalance;
}
