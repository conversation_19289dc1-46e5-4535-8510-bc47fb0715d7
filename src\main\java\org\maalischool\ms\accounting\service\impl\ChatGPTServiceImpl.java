package org.maalischool.ms.accounting.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.maalischool.ms.accounting.service.ChatGPTService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Implementation of ChatGPT service for generating human-readable reports
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ChatGPTServiceImpl implements ChatGPTService {

    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    private String p = "You are an expert financial analyst and UI designer specializing in creating professional, visually appealing, and culturally relevant reports for non-accounting staff in Saudi Arabia, This Is a School has 3 branches in Saudi Arabia. Your task is to generate comprehensive reports using HTML, and CSS (without external libraries or links) that are consistent in design, structure, and formatting regardless of the type of data or report being generated. All monetary values must be displayed in Saudi Riyal (SAR). Below are detailed instructions to guide you in crafting these reports:\\n" + //
            "\\n" + //
            "Key Objectives\\n" + //
            "Consistency Across Reports: Ensure every report adheres to the same design principles, layout, and branding guidelines.\\n" + //
            "Clarity and Accessibility: Write content in formal Arabic that is easy to understand for non-accounting professionals while maintaining proper grammar and vocabulary.\\n" + //
            "Visual Appeal: Use charts and tables effectively to present data clearly and engagingly.\\n" + //
            "Responsiveness: Optimize the report for readability on all devices, including desktops, tablets, and mobile phones.\\n" + //
            "Template and Design Guidelines\\n" + //
            "1. Language\\n" + //
            "All text, including headings, paragraphs, table content, chart labels, legends, and recommendations, must be written in clear, formal Arabic suitable for non-accounting staff.\\n" + //
            "Avoid overly technical jargon; explain complex terms when necessary.\\n" + //
            "2. Data Presentation\\n" + //
            "Populate tables and charts with accurate and relevant financial data.\\n" + //
            "Display all monetary values in Saudi Riyal (SAR) and assign them the currency class for consistent styling.\\n" + //
            "Use RTL (right-to-left) directionality throughout the report to align with Arabic typography standards.\\n" + //
            "3. Structure and Layout\\n" + //
            "Every report should follow this standardized structure:\\n" + //
            "\\n" + //
            "Executive Summary\\n" + //
            "Provide a brief overview of the key findings, trends, and insights from the report.\\n" + //
            "Highlight critical KPIs and actionable takeaways.\\n" + //
            "Data Sections\\n" + //
            "Divide the report into logical sections based on the dataset provided (e.g., revenue analysis, expense breakdown, profitability).\\n" + //
            "Each section should include:\\n" + //
            "A concise introduction explaining the purpose of the section.\\n" + //
            "Relevant tables displaying raw or summarized data.\\n" + //
            "Charts (bar, line, pie, or area charts as appropriate) to visualize trends and patterns.\\n" + //
            "A detailed analysis explaining the data, identifying trends, and discussing their implications.\\n" + //
            "Recommendations\\n" + //
            "Include a dedicated section with:\\n" + //
            "Key Performance Indicator (KPI) analysis.\\n" + //
            "Risk assessment and mitigation strategies.\\n" + //
            "Actionable recommendations based on the data.\\n" + //
            "Future projections and growth opportunities.\\n" + //
            "Conclusion\\n" + //
            "Summarize the main points discussed in the report.\\n" + //
            "Reinforce actionable insights and next steps.\\n" + //
            "4. Visual Elements\\n" + //
            "Charts:\\n" + //
            "For each major data section, create appropriate visualizations using charts. Follow these requirements:\\n" + //
            "Use consistent color schemes (#B14E00, #1C5954, #D6AA48) for all elements, including chart bars, lines, and legends.\\n" + //
            "Label axes, legends, and data points clearly in Arabic.\\n" + //
            "Add trend lines where applicable to highlight changes over time.\\n" + //
            "Ensure charts are responsive and scale appropriately on smaller screens.\\n" + //
            "Example Chart Types:\\n" + //
            "Bar charts for comparing categories.\\n" + //
            "Line charts for showing trends over time.\\n" + //
            "Pie charts for displaying proportions.\\n" + //
            "Area charts for cumulative data representation.\\n" + //
            "Tables:\\n" + //
            "Use clean, minimalistic table designs with borders styled using brand colors.\\n" + //
            "Highlight important rows or columns to draw attention to key figures.\\n" + //
            "5. Styling and Branding\\n" + //
            "Brand Colors:\\n" + //
            "Use the specified brand colors (#B14E00, #1C5954, #D6AA48) consistently across the report:\\n" + //
            "Headings and subheadings.\\n" + //
            "Chart elements (bars, lines, legends).\\n" + //
            "Table borders and highlights.\\n" + //
            "Important information such as KPIs or warnings.\\n" + //
            "Typography:\\n" + //
            "Choose a modern Arabic font that supports RTL rendering and ensures readability.\\n" + //
            "Maintain uniform font sizes and weights for headings, body text, and captions.\\n" + //
            "6. Responsiveness\\n" + //
            "Ensure the report is fully responsive by using relative units (e.g., percentages, em/rem) for widths, heights, margins, and paddings.\\n" + //
            "Test the layout on various screen sizes to confirm it adapts well to different devices.\\n" + //
            "7. Customization\\n" + //
            "Replace bracketed placeholders with actual content dynamically generated from the input data.\\n" + //
            "Tailor the analysis, recommendations, and visualizations to fit the specific dataset provided.\\n" + //
            "Final Output Requirements\\n" + //
            "Generate the report as an HTML file with embedded CSS and JavaScript for styling and interactivity.\\n" + //
            "Ensure all visualizations (charts and tables) are included directly in the output, not as separate files.\\n" + //
            "Validate the code for cross-browser compatibility and responsiveness.";


    String p2 = """
            You are an expert financial report writer and financial analyst for non-accountant business owners in Saudi Arabia.
            
             Your task is to generate a structured, consistent, and professional **HTML financial report** based on the provided JSON data.
            
             Report Requirements:
             - Language: Modern Standard Arabic (الفصحى).
             - Structure: Follow exactly this sequence in every report:
               1. Executive Summary (ملخص تنفيذي)
               2. Financial Tables (جداول مالية مفصلة)
               3. Key Observations & Financial Analysis (ملاحظات رئيسية و تحليل مالي)
               4. Recommendations with actionable insights (اقتراحات مع معلومات فعالة)
               5. Conclusion (خاتمة)
            
             Formatting Rules:
             - Display all financial amounts in Saudi Riyals (SAR).
             - Build a clean and printable HTML structure using these tags: <h1>, <h2>, <table>, <p>, <div>.
             - Use **TailwindCSS** classes for styling.
             - Apply the following text formatting:
               - Font Family: Tajawal
               - Text Direction: Right-to-Left (RTL)
               - Text Alignment: Right
             - Apply the following color scheme:
               - Primary headings: #B14E00
               - Section headings: #1C5954
               - Table headers and important highlights: #D6AA48
             - Assign the main container the class "financial-report".
             - All tables must be styled for clarity with visible borders, header background, padding, and consistent spacing.
             - Currency format must use commas and the symbol "ر.س" (e.g., 1,500 ر.س).
            
             Charts Instructions:
             - If appropriate, suggest 1–2 simple charts (bar chart or pie chart).
             - Provide either:
               - Full Chart.js configuration JSON, or
               - Structured JSON data for frontend rendering.
             - Focus charts on major figures like Revenues, Expenses, Net Profit, and Assets vs Liabilities.
            
             Tone and Style:
             - Maintain a formal, clear, and respectful tone.
             - Avoid deep technical accounting jargon.
             - Ensure insights are simple, practical, and easy to understand for non-accountants.
             - Always use the same section titles, sequence, and formatting across all reports for consistency.
            
             Important:
             - Financial Analysis Section must include trends, major risks, positive indicators, and attention points based on provided data.
             - Recommendations Section must suggest realistic next steps, such as cost optimization, revenue boosting, or cashflow improvements, based on the data.
            """;
    // @Value("${openai.api.url:https://api.openai.com/v1/chat/completions}")
    // @Value("${openai.api.url:https://api.deepseek.com}")
    @Value("${openai.api.url:https://router.requesty.ai/v1}")
    private String apiUrl;

    // @Value("${openai.api.key:sk-8aca11fd574b4cb7a4e84278a52f8da5}")
    @Value("${openai.api.key:********************************************************************************************************************************************************************}")
    private String apiKey;

    // @Value("${openai.model:gpt-4.1-mini}")
    @Value("${openai.model:gpt-4.1-mini}")
    private String model;

    @Override
    @SuppressWarnings("unchecked")
    public String generateReport(String reportType, Map<String, Object> reportData, String audience) {
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("Authorization", "Bearer " + apiKey);

            // Create the prompt based on report type and data
            String prompt = createPrompt(reportType, reportData, audience);
            log.info("Generated prompt for ChatGPT: {}", prompt);

            // Create the request body
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", model);
            requestBody.put("messages", List.of(
                    Map.of("role", "system", "content", p2),
                    Map.of("role", "user", "content", prompt)
            ));
            requestBody.put("temperature", 0.7);
//            requestBody.put("max_tokens", 50000);

            // Convert the request body to JSON
            String requestJson = objectMapper.writeValueAsString(requestBody);

            // Create the HTTP entity
            HttpEntity<String> entity = new HttpEntity<>(requestJson, headers);

            // Make the API call
            ResponseEntity<Map> response = restTemplate.postForEntity(apiUrl, entity, Map.class);

            // Extract the response content
            Map<String, Object> responseBody = response.getBody();
            if (responseBody != null && responseBody.containsKey("choices")) {
                List<Map<String, Object>> choices = (List<Map<String, Object>>) responseBody.get("choices");
                if (!choices.isEmpty()) {
                    Map<String, Object> choice = choices.get(0);
                    Map<String, Object> message = (Map<String, Object>) choice.get("message");
                    return (String) message.get("content");
                }
            }

            log.error("Failed to parse ChatGPT response: {}", responseBody);
            return "Error generating report. Please try again later.";

        } catch (Exception e) {
            log.error("Error calling ChatGPT API", e);
            return "Error generating report: " + e.getMessage();
        }
    }

    /**
     * Create a prompt for ChatGPT based on the report type and data
     */
    private String createPrompt(String reportType, Map<String, Object> reportData, String audience) {
        StringBuilder prompt = new StringBuilder();

        prompt.append("Please create a clear, concise ").append(reportType.replace("_", " ").toLowerCase())
                .append(" report for ").append(audience).append(".\n\n");

        prompt.append("The report should explain the financial data in simple terms, avoiding technical jargon when possible. ")
                .append("Include key insights and explanations that would be helpful for someone without an accounting background.\n\n");

        prompt.append("Here is the data to base your report on:\n");

        // Format the report data properly
        if (reportData != null && !reportData.isEmpty()) {
            try {
                // Convert the map to a formatted JSON string
                String formattedData = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(reportData);
                prompt.append(formattedData);
            } catch (Exception e) {
                log.warn("Failed to format report data as JSON, using toString() instead", e);
                // Fallback to basic formatting if JSON conversion fails
                reportData.forEach((key, value) -> {
                    prompt.append(key).append(": ");
                    if (value != null) {
                        prompt.append(value.toString());
                    } else {
                        prompt.append("null");
                    }
                    prompt.append("\n");
                });
            }
        } else {
            prompt.append("No data provided for this report. Please generate a generic explanation of what this type of report typically contains.");
        }

        log.debug("Generated prompt for ChatGPT: {}", prompt);
        return prompt.toString();
    }
}
