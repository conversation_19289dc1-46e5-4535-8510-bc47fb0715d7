package org.maalischool.ms.auth.filter;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

@Component
@RequiredArgsConstructor // Creates constructor with final fields
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger log = LoggerFactory.getLogger(JwtAuthenticationFilter.class);
    private final JwtService jwtService;
    private final UserDetailsService userDetailsService; // Provided by ApplicationConfig

    private static final String AUTH_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    @Override
    protected void doFilterInternal(
            @NonNull HttpServletRequest request,
            @NonNull HttpServletResponse response,
            @NonNull FilterChain filterChain
    ) throws ServletException, IOException {

        final String authHeader = request.getHeader(AUTH_HEADER);
        final String jwt;
        final String userEmail;

        // 1. Check if Authorization header exists and starts with Bearer
        if (authHeader == null || !authHeader.startsWith(BEARER_PREFIX)) {
            log.trace("No JWT token found in request header for URI: {}", request.getRequestURI());
            filterChain.doFilter(request, response); // Continue chain
            return;
        }

        // 2. Extract JWT token (remove "Bearer ")
        jwt = authHeader.substring(BEARER_PREFIX.length());

        try {
            // 3. Extract user email (subject) from token
            userEmail = jwtService.extractUsername(jwt);

            // 4. Check if email exists and user is not already authenticated
            if (userEmail != null && SecurityContextHolder.getContext().getAuthentication() == null) {
                // 5. Load UserDetails from database
                UserDetails userDetails = this.userDetailsService.loadUserByUsername(userEmail);

                // 6. Validate token
                if (jwtService.isTokenValid(jwt, userDetails)) {
                    log.debug("JWT token is valid for user: {}", userEmail);
                    // 7. Create authentication token
                    UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                            userDetails,
                            null, // Credentials are null for JWT based auth
                            userDetails.getAuthorities()
                    );
                    // 8. Set details (IP, session ID etc.)
                    authToken.setDetails(
                            new WebAuthenticationDetailsSource().buildDetails(request)
                    );
                    // 9. Update SecurityContextHolder
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                    log.debug("User '{}' authenticated successfully via JWT.", userEmail);
                } else {
                    log.warn("JWT token validation failed for user: {}", userEmail);
                }
            } else {
                if (userEmail == null) {
                    log.warn("Could not extract username from JWT token.");
                } else {
                    log.trace("User '{}' already authenticated, skipping JWT validation.", userEmail);
                }
            }
        } catch (Exception e) {
            // Catch potential exceptions during token parsing/validation (e.g., ExpiredJwtException, MalformedJwtException)
            log.warn("Error processing JWT token for URI {}: {}", request.getRequestURI(), e.getMessage());
            // Optionally: Clear context if an invalid token was somehow processed partially
            // SecurityContextHolder.clearContext();
            // We allow the filter chain to continue, but the SecurityContext remains unauthenticated.
            // The AuthenticationEntryPoint will handle the response later if the endpoint requires authentication.
        }

        // 10. Continue filter chain
        filterChain.doFilter(request, response);
    }
}
