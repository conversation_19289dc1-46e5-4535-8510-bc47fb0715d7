package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.UUID;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
@Builder
public class BalanceSheetAccountDto {
    private UUID accountId;
    private String accountNumber;
    private String accountNameEn;
    private String accountNameAr;
    private BigDecimal debit;
    private BigDecimal credit;
    private BigDecimal balance;
}
