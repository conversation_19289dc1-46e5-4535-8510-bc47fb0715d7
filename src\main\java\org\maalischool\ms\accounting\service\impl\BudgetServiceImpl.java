package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.*;
import org.maalischool.ms.accounting.exception.BudgetNotFoundException;
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException;
import org.maalischool.ms.accounting.model.Budget;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.BudgetRepository;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.BudgetService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BudgetServiceImpl implements BudgetService {

    private static final Logger log = LoggerFactory.getLogger(BudgetServiceImpl.class);
    private final BudgetRepository budgetRepository;
    private final ChartOfAccountRepository chartOfAccountRepository;
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public BudgetDto createBudget(CreateBudgetRequest request) {
        log.info("Creating new Budget: AcademicYear={}, Period={}, AccountID={}, Amount={}", // Updated log
                 request.getAcademicYear(), request.getPeriod(), request.getChartOfAccountId(), request.getBudgetedAmount()); // Use getBudgetedAmount

        ChartOfAccount account = null;
        if (request.getChartOfAccountId() != null) {
             account = chartOfAccountRepository.findById(request.getChartOfAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getChartOfAccountId()));
        }
        // TODO: Add similar check for expenseCategoryId if provided

        // Check if a budget already exists for this account/category, year, and period
        // Need to adjust repository method call based on final BudgetRepository signature
        // budgetRepository.findByChartOfAccountIdAndAcademicYearAndPeriod(...)
        log.warn("Duplicate budget check needs adjustment for academicYear and period.");
        /*
        budgetRepository.findByChartOfAccountIdAndAcademicYearAndPeriod(request.getChartOfAccountId(), request.getAcademicYear(), request.getPeriod())
                .ifPresent(b -> {
                    String accountIdentifier = (account != null) ? account.getName() + " (" + account.getAccountNumber() + ")" : "N/A";
                    // TODO: Add ExpenseCategory identifier if applicable
                    throw new IllegalArgumentException(String.format("Budget already exists for Account '%s', Year '%s', Period '%s'.",
                            accountIdentifier, request.getAcademicYear(), request.getPeriod()));
                });
        */


        Budget budget = Budget.builder()
                .chartOfAccount(account)
                // .expenseCategory(...) // Add if expenseCategoryId is used
                .academicYear(request.getAcademicYear()) // Use academicYear
                .period(request.getPeriod()) // Add period
                .budgetedAmount(request.getBudgetedAmount()) // Use getBudgetedAmount
                .notes(request.getNotes())
                .build();

        Budget savedBudget = budgetRepository.save(budget);
        log.info("Successfully created Budget with ID: {}", savedBudget.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_BUDGET,
                savedBudget.getId(),
                String.format("Created Budget: Year: %s, Period: %s, Account: %s (%s), Amount: %s", // Updated log format
                        savedBudget.getAcademicYear(), savedBudget.getPeriod(),
                        (account != null ? account.getNameEn() : "N/A"), // Use nameEn
                        (account != null ? account.getAccountNumber() : "N/A"),
                        savedBudget.getBudgetedAmount())
        );

        return mapToDto(savedBudget);
    }

    @Override
    @Transactional(readOnly = true)
    public BudgetDto getBudgetById(UUID id) {
        log.debug("Fetching Budget by ID: {}", id);
        Budget budget = findBudgetOrThrow(id);
        return mapToDto(budget);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BudgetDto> getBudgetsByAcademicYear(String academicYear, Pageable pageable) { // Renamed parameter
        log.debug("Fetching Budgets for Academic Year: {}", academicYear); // Update log
        // Adjust repository method name if needed - Use NameEn for sorting
        return budgetRepository.findByAcademicYearOrderByPeriodAscChartOfAccountNameEnAscExpenseCategoryNameEnAsc(academicYear, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public BudgetDto getBudgetByAccountAndPeriod(UUID chartOfAccountId, String academicYear, String period) { // Updated parameters
        log.debug("Fetching Budget for Account ID {}, Academic Year {}, Period {}", chartOfAccountId, academicYear, period); // Update log
        // Adjust repository method name if needed
        return budgetRepository.findByAcademicYearAndPeriodAndChartOfAccountId(academicYear, period, chartOfAccountId)
                .map(this::mapToDto)
                .orElseThrow(() -> new BudgetNotFoundException(String.format("Budget not found for Account ID %s, Year %s, Period %s", chartOfAccountId, academicYear, period)));
    }

    @Override
    @Transactional(readOnly = true)
    public Page<BudgetDto> getAllBudgets(Pageable pageable) {
        log.debug("Fetching Budgets page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return budgetRepository.findAll(pageable).map(this::mapToDto);
    }

    @Override
    @Transactional
    public BudgetDto updateBudget(UUID id, UpdateBudgetRequest request) {
        log.info("Updating Budget with ID: {}", id);
        Budget budget = findBudgetOrThrow(id);

        // Note: Typically, you don't change the Account, Category, Year, or Period of an existing budget entry.
        // If that's needed, it might be better to delete and create a new one.
        // We'll assume only budgetedAmount and notes are updatable here.
        /* // Remove or adjust validation based on business rules
        if ((budget.getChartOfAccount() != null && !budget.getChartOfAccount().getId().equals(request.getChartOfAccountId())) ||
            (budget.getChartOfAccount() == null && request.getChartOfAccountId() != null) ||
            // Add similar checks for expenseCategory if used
            !budget.getAcademicYear().equals(request.getAcademicYear()) ||
            !budget.getPeriod().equals(request.getPeriod())) {
             log.error("Attempted to change immutable fields (Account/Category/Year/Period) for Budget ID {}", id);
             throw new IllegalArgumentException("Cannot change the Account, Category, Academic Year, or Period of an existing budget entry.");
        }
        */

        String oldDetails = String.format("Amount: %s, Notes: %s", budget.getBudgetedAmount(), budget.getNotes()); // Use budgetedAmount

        budget.setBudgetedAmount(request.getBudgetedAmount()); // Use getBudgetedAmount from request
        budget.setNotes(request.getNotes());

        Budget updatedBudget = budgetRepository.save(budget);
        log.info("Successfully updated Budget with ID: {}", updatedBudget.getId());

        String newDetails = String.format("Amount: %s, Notes: %s", updatedBudget.getBudgetedAmount(), updatedBudget.getNotes()); // Use budgetedAmount
        ChartOfAccount account = budget.getChartOfAccount(); // Get account for logging
        // TODO: Get ExpenseCategory if applicable

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_BUDGET,
                updatedBudget.getId(),
                String.format("Updated Budget for %s - %s, Account: %s (%s). Old: [%s], New: [%s]", // Update log format
                        updatedBudget.getAcademicYear(), updatedBudget.getPeriod(),
                        (account != null ? account.getNameEn() : "N/A"), // Use nameEn
                        (account != null ? account.getAccountNumber() : "N/A"),
                        oldDetails, newDetails)
        );

        return mapToDto(updatedBudget);
    }

    @Override
    @Transactional
    public void deleteBudget(UUID id) {
        log.warn("Attempting to delete Budget with ID: {}", id);
        Budget budget = findBudgetOrThrow(id);
        ChartOfAccount account = budget.getChartOfAccount(); // Get details before deleting

        budgetRepository.delete(budget);
        log.warn("Successfully deleted Budget with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_BUDGET,
                id,
                String.format("Deleted Budget: Year: %s, Period: %s, Account: %s (%s), Amount: %s", // Update log format
                        budget.getAcademicYear(), budget.getPeriod(),
                        (account != null ? account.getNameEn() : "N/A"), // Use nameEn
                        (account != null ? account.getAccountNumber() : "N/A"),
                        budget.getBudgetedAmount())
        );
    }

    private Budget findBudgetOrThrow(UUID id) {
        return budgetRepository.findById(id)
                .orElseThrow(() -> new BudgetNotFoundException(id));
    }

    // --- Helper Methods ---

    private BudgetDto mapToDto(Budget budget) {
        if (budget == null) return null;
        // TODO: Map ExpenseCategory to SimpleExpenseCategoryDto if applicable
        return BudgetDto.builder()
                .id(budget.getId())
                .chartOfAccount(mapAccountToSimpleDto(budget.getChartOfAccount()))
                // .expenseCategory(...) // Add if needed
                .academicYear(budget.getAcademicYear()) // Use academicYear
                .period(budget.getPeriod()) // Add period
                .budgetedAmount(budget.getBudgetedAmount()) // Use budgetedAmount
                .notes(budget.getNotes())
                .createdDate(budget.getCreatedDate())
                .lastModifiedDate(budget.getLastModifiedDate())
                .build();
    }

    private SimpleChartOfAccountDto mapAccountToSimpleDto(ChartOfAccount account) {
        if (account == null) return null;
        return SimpleChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn()) // Use English name
                .nameAr(account.getNameAr())   // Use Arabic name
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("********-0000-0000-0000-********0000");
    }
}
