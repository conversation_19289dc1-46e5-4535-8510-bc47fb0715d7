package org.maalischool.ms.accounting.dto;

import java.time.Instant;
import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

import org.maalischool.ms.accounting.model.enums.ReportStatus;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DTO for AI-generated report response
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIReportResponse {

    private UUID id;
    private String reportType;
    private LocalDate startDate;
    private LocalDate endDate;
    private Map<String, Object> filters; // Deserialized from JSON
    private String content;
    private ReportStatus status;
    private boolean fromCache; // Indicates if the report was retrieved from cache
    private Instant createdDate;
    private Instant lastModifiedDate;
}
