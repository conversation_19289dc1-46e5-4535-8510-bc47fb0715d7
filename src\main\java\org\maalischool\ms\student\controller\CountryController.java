package org.maalischool.ms.student.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.exception.ErrorResponse;
import org.maalischool.ms.student.dto.CountryDto;
import org.maalischool.ms.student.service.CountryService;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/countries")
@RequiredArgsConstructor
@Tag(name = "Country Management", description = "Endpoints for managing countries")
@SecurityRequirement(name = "Bearer Authentication")
public class CountryController {

    private final CountryService countryService;
    private final MessageSource messageSource;

    private String getMessage(String code, Object... args) {
        return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
    }

    @Operation(summary = "Get country by ID", description = "Retrieves a specific country by its ID. Accessible to all authenticated users.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Country found", content = @Content(schema = @Schema(implementation = CountryDto.class))),
            @ApiResponse(responseCode = "404", description = "Country not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping("/{countryId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<CountryDto> getCountryById(@PathVariable UUID countryId) {
        return countryService.getCountryById(countryId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "Get country by code", description = "Retrieves a specific country by its code. Accessible to all authenticated users.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Country found", content = @Content(schema = @Schema(implementation = CountryDto.class))),
            @ApiResponse(responseCode = "404", description = "Country not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping("/code/{code}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<CountryDto> getCountryByCode(@PathVariable String code) {
        return countryService.getCountryByCode(code)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @Operation(summary = "Search countries", description = "Search countries with optional keyword filtering. Supports pagination and sorting. Accessible to all authenticated users.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Countries retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<Page<CountryDto>> searchCountries(
            @Parameter(description = "Search keyword to filter by nameAr, nameEn, or code (case-insensitive, partial match)")
            @RequestParam(required = false) String search,
            @ParameterObject @PageableDefault(size = 20, sort = "nameEn") Pageable pageable) {

        return ResponseEntity.ok(countryService.searchCountries(search, pageable));
    }

    @Operation(summary = "Get all countries", description = "Retrieves all countries without pagination. Accessible to all authenticated users.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Countries retrieved successfully"),
            @ApiResponse(responseCode = "401", description = "Unauthorized", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping("/all")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<List<CountryDto>> getAllCountries() {
        return ResponseEntity.ok(countryService.getAllCountries());
    }
}
