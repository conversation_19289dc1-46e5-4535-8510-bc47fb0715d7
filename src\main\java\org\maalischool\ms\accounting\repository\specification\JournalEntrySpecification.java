package org.maalischool.ms.accounting.repository.specification;

import jakarta.persistence.criteria.Predicate;
import org.maalischool.ms.accounting.model.JournalEntry;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils; // Use Spring's StringUtils

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * Utility class for creating JPA Specifications for JournalEntry entities.
 */
public class JournalEntrySpecification {

    private JournalEntrySpecification() {
        // Private constructor to hide the implicit public one
    }

    /**
     * Creates a Specification based on optional filter criteria.
     *
     * @param startDate       Optional start date for filtering.
     * @param endDate         Optional end date for filtering.
     * @param referenceNumber Optional reference number (case-insensitive, partial match).
     * @return A Specification<JournalEntry> combining the provided criteria.
     * @param isPosted        Optional filter for posted status (true=posted, false=unposted, null=both).
     */
    public static Specification<JournalEntry> filterBy(LocalDate startDate, LocalDate endDate, String referenceNumber, Boolean isPosted) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (startDate != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("entryDate"), startDate));
            }
            if (endDate != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("entryDate"), endDate));
            }
            // Use StringUtils.hasText for robust check (null, empty, whitespace)
            if (StringUtils.hasText(referenceNumber)) {
                predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get("referenceNumber")),
                        "%" + referenceNumber.toLowerCase() + "%"));
            }

            // Filter by posted status if isPosted is not null
            if (isPosted != null) {
                if (isPosted) {
                    // Find entries where postedDate is not null
                    predicates.add(criteriaBuilder.isNotNull(root.get("postedDate")));
                } else {
                    // Find entries where postedDate is null
                    predicates.add(criteriaBuilder.isNull(root.get("postedDate")));
                }
            }

            // Add default sorting if needed, e.g., by entry date descending
            // Only apply default sort if no sort is specified in the Pageable
            if (query.getOrderList().isEmpty()) {
                 query.orderBy(criteriaBuilder.desc(root.get("entryDate")));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    // --- Optional: Individual Specification methods if preferred ---

    public static Specification<JournalEntry> entryDateGreaterThanOrEqualTo(LocalDate date) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.greaterThanOrEqualTo(root.get("entryDate"), date);
    }

    public static Specification<JournalEntry> entryDateLessThanOrEqualTo(LocalDate date) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.lessThanOrEqualTo(root.get("entryDate"), date);
    }

    public static Specification<JournalEntry> referenceNumberContainsIgnoreCase(String referenceNumber) {
        return (root, query, criteriaBuilder) ->
                criteriaBuilder.like(criteriaBuilder.lower(root.get("referenceNumber")),
                        "%" + referenceNumber.toLowerCase() + "%");
    }
}
