package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Discount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface DiscountRepository extends JpaRepository<Discount, UUID> {

    /**
     * Finds a discount by its code, ignoring case.
     *
     * @param code The discount code.
     * @return An Optional containing the Discount if found.
     */
    Optional<Discount> findByCodeIgnoreCase(String code); // Add missing method

    Optional<Discount> findByCodeIgnoreCaseAndActiveTrue(String code);

    List<Discount> findByActiveTrueOrderByCodeAsc();

    // Find active discounts valid today
    @Query("SELECT d FROM Discount d WHERE d.active = true " +
           "AND (d.validFrom IS NULL OR d.validFrom <= :today) " +
           "AND (d.validUntil IS NULL OR d.validUntil >= :today) " +
           "ORDER BY d.code")
    List<Discount> findActiveAndValidDiscounts(@Param("today") LocalDate today);

    // Find discounts applicable to a specific fee
    List<Discount> findByApplicableFeeIdAndActiveTrue(UUID feeId);

    // Find discounts applicable to a specific student
    List<Discount> findByApplicableStudentIdAndActiveTrue(UUID studentId);
}
