package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class JournalEntryDto {
    private UUID id;
    private LocalDate entryDate;
    private String description;
    private String referenceNumber;
    private String journalEntryNumber;
    private List<JournalEntryLineDto> lines;
    private Instant postedDate;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
