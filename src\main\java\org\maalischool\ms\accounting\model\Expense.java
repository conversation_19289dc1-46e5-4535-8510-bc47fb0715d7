package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID; // Keep existing UUID import

@Entity
@Table(name = "acc_expenses")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class Expense {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "expense_date", nullable = false)
    private LocalDate expenseDate;

    @Column(nullable = false, length = 500)
    private String descriptionEn;
    @Column(nullable = false, length = 500)
    private String descriptionAr;

    @Column(name = "amount", nullable = false, precision = 19, scale = 4)
    private BigDecimal amount; // Renamed for clarity (amount before tax)

    @ManyToOne(fetch = FetchType.LAZY) // Tax is optional
    @JoinColumn(name = "tax_id")
    private Tax tax;

    @Column(name = "tax_amount", precision = 19, scale = 4) // Tax amount can be null if no tax applied
    private BigDecimal taxAmount;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "category_id", nullable = false)
    private ExpenseCategory category;

    // Remove PaymentMethod link, use paymentAccount instead
    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "payment_method_id")
    // private PaymentMethod paymentMethod;

    // REMOVED: Expense account is now derived from the ExpenseCategory
    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "expense_account_id")
    // private ChartOfAccount expenseAccount;

    // Link to the ChartOfAccount that was credited (Cash/Bank/Payable) - This remains
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "payment_account_id")
    private ChartOfAccount paymentAccount;

    @Column(length = 255)
    private String vendor; // Simple vendor name, or link to a Vendor entity

    @Column(name = "reference_number", length = 100) // e.g., Invoice number
    private String referenceNumber;

    @Column(name = "receipt_attached_path", length = 500) // Path or ID to stored receipt image/doc
    private String receiptAttachedPath;

    // Note: Consider adding a 'totalAmount' field (amount + taxAmount) if frequently needed,
    // or calculate it on the fly in services/DTOs. For now, keeping it separate in the entity.

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID createdBy;

    @LastModifiedBy
    @Column(name = "last_modified_by")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID lastModifiedBy;
}
