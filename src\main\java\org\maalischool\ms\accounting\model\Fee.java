package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
// Import related entities if they exist in other packages
// import org.maalischool.ms.schoolmanagement.model.GradeLevel;
// import org.maalischool.ms.schoolmanagement.model.EducationalStage;
// import org.maalischool.ms.schoolmanagement.model.Branch;
import org.maalischool.ms.schoolmanagement.model.AcademicYear;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate; // Add import for LocalDate
import java.util.UUID;

@Entity
@Table(name = "acc_fees", uniqueConstraints = {
        // Example: Ensure fee name is unique within an academic year and category
        @UniqueConstraint(columnNames = { "name_en", "academic_year_id", "fee_category_id" })
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class Fee {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(nullable = false, length = 255)
    private String nameEn;
    @Column(nullable = false, length = 255)
    private String nameAr;

    @Column(length = 1000)
    private String descriptionEn;
    @Column(length = 1000)
    private String descriptionAr;

    @Column(nullable = false, precision = 19, scale = 4)
    private BigDecimal amount;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "fee_category_id", nullable = false)
    private FeeCategory feeCategory;

    // Consider linking to ChartOfAccount for revenue recognition
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "revenue_account_id")
    private ChartOfAccount revenueAccount;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "academic_year_id", nullable = false)
    private AcademicYear academicYear;

    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate; // Add the dueDate field

    // Applicability - Use specific IDs if Grade/Stage/Branch entities exist
    // Ensure the related entities (GradeLevel, EducationalStage, Branch) are
    // imported if needed
    @Column(name = "applicable_grade_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID applicableGradeId; // Link to GradeLevel entity ID

    @Column(name = "applicable_stage_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID applicableStageId; // Link to EducationalStage entity ID

    @Column(name = "applicable_branch_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID applicableBranchId; // Link to Branch entity ID

    @Column(name = "is_active", nullable = false)
    @Builder.Default
    private boolean active = true;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
