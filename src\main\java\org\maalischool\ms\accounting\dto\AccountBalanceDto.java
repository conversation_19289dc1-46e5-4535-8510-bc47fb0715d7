package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AccountBalanceDto {
    private UUID accountId;
    private String accountNumber;
    private String accountName;
    private BigDecimal balance;
}