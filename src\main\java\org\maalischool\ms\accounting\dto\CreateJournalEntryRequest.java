package org.maalischool.ms.accounting.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
public class CreateJournalEntryRequest {
    @NotNull(message = "Entry date cannot be null")
    private LocalDate entryDate;

    @NotBlank(message = "Description cannot be blank")
    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;

    @Size(max = 100, message = "Reference number must be less than 100 characters")
    private String referenceNumber;

    @NotEmpty(message = "Journal entry must have at least one line")
    @Valid
    private List<CreateJournalEntryLineRequest> lines;
}
