package org.maalischool.ms.accounting.mapper;

import java.util.List;

import org.maalischool.ms.accounting.dto.CreateTaxRequest;
import org.maalischool.ms.accounting.dto.TaxDto;
import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto;
import org.maalischool.ms.accounting.dto.UpdateTaxRequest;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.Tax;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

// Assuming ChartOfAccountMapper exists and maps ChartOfAccount -> SimpleChartOfAccountDto
@Mapper(componentModel = "spring", uses = {ChartOfAccountMapper.class})
public interface TaxMapper {

    // INSTANCE field is generally not needed with componentModel = "spring"
    // TaxMapper INSTANCE = Mappers.getMapper(TaxMapper.class);

    @Mapping(source = "chartOfAccount", target = "chartOfAccount") // MapStruct should use ChartOfAccountMapper here
    TaxDto toTaxDto(Tax tax);

    List<TaxDto> toTaxDtoList(List<Tax> taxes);

    @Mapping(target = "id", ignore = true) // Let DB generate ID
    @Mapping(target = "chartOfAccount", ignore = true) // Service layer will handle fetching/setting this
    Tax toTax(CreateTaxRequest createTaxRequest);

    @Mapping(target = "id", ignore = true) // Don't update ID
    @Mapping(target = "chartOfAccount", ignore = true) // Service layer will handle fetching/setting this
    void updateTaxFromDto(UpdateTaxRequest updateTaxRequest, @MappingTarget Tax tax);
}
