package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateRoleRequest {

    @NotBlank(message = "{validation.roleName.notBlank}") // Assuming you add this key
    @Size(min = 3, max = 50, message = "{validation.roleName.size}")
    private String name;

    @Size(max = 255, message = "Description cannot exceed 255 characters")
    private String description;

    // List of permission names to assign to the new role
    @NotEmpty(message = "{validation.permissionList.notEmpty}")
    private List<@NotBlank(message = "{validation.permissionName.notBlank}") String> permissionNames;
}
