package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.ItemType;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
public class CreateItemRequest {
    @NotBlank(message = "Item code cannot be blank")
    @Size(max = 50, message = "Item code must be less than 50 characters")
    private String itemCode;

    @NotBlank(message = "English item name cannot be blank")
    @Size(max = 255, message = "English item name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic item name cannot be blank")
    @Size(max = 255, message = "Arabic item name must be less than 255 characters")
    private String nameAr;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    @NotNull(message = "Item type cannot be null")
    private ItemType type;

    @NotBlank(message = "Unit of measure cannot be blank")
    @Size(max = 50, message = "Unit of measure must be less than 50 characters") // e.g., "pcs", "kg", "box"
    private String unitOfMeasure;

    @NotNull(message = "Cost price cannot be null")
    @DecimalMin(value = "0.0", message = "Cost price must be non-negative")
    private BigDecimal costPrice;

    @NotNull(message = "Selling price cannot be null")
    @DecimalMin(value = "0.0", message = "Selling price must be non-negative")
    private BigDecimal sellingPrice;

    @NotNull(message = "Inventory account (Chart of Account) ID cannot be null")
    private UUID inventoryAccountId; // Asset account for inventory value

    @NotNull(message = "COGS account (Chart of Account) ID cannot be null")
    private UUID cogsAccountId; // Cost of Goods Sold expense account

    @NotNull(message = "Sales account (Chart of Account) ID cannot be null")
    private UUID salesAccountId; // Revenue account for sales

    @PositiveOrZero(message = "Reorder level must be zero or positive")
    private Integer reorderLevel;

    @Size(max = 255, message = "Supplier info must be less than 255 characters")
    private String supplierInfo;
}
