package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.InventoryTransactionType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * DTO for requesting an inventory adjustment.
 */
@Data
@Builder
public class InventoryAdjustmentRequest {

    @NotNull(message = "Item ID cannot be null")
    private UUID itemId;

    @NotNull(message = "Transaction date cannot be null")
    private LocalDate transactionDate;

    @NotNull(message = "Transaction type cannot be null")
    private InventoryTransactionType transactionType;

    @NotNull(message = "Quantity cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Adjustment quantity must be positive")
    private BigDecimal quantity; // The magnitude of the change (always positive)

    // Optional: Cost per unit at the time of this transaction (e.g., for purchases or cost adjustments)
    @DecimalMin(value = "0.0", message = "Unit cost cannot be negative")
    private BigDecimal unitCost;

    @Size(max = 1000, message = "Notes must be less than 1000 characters")
    private String notes;

    @Size(max = 100, message = "Reference ID must be less than 100 characters")
    private String referenceId; // Optional link to PO, Sale Order, Adjustment Document etc.
}
