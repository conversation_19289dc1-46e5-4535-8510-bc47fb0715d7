package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreateItemRequest;
import org.maalischool.ms.accounting.dto.ItemDto;
import org.maalischool.ms.accounting.dto.SimpleItemDto; // Assuming this exists
import org.maalischool.ms.accounting.dto.UpdateItemRequest;
import org.maalischool.ms.accounting.exception.ItemNotFoundException;
import org.maalischool.ms.accounting.model.Item;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.InventoryTransactionRepository; // To check usage/stock
import org.maalischool.ms.accounting.repository.ItemRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.InventoryService; // Add missing import
import org.maalischool.ms.accounting.service.ItemService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ItemServiceImpl implements ItemService {

    private static final Logger log = LoggerFactory.getLogger(ItemServiceImpl.class);
    private final ItemRepository itemRepository;
    private final InventoryTransactionRepository inventoryTransactionRepository; // To check transactions
    private final InventoryService inventoryService; // Inject InventoryService to get stock
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public ItemDto createItem(CreateItemRequest request) {
        log.info("Creating new Item: NameEn={}, ItemCode={}", request.getNameEn(), request.getItemCode()); // Use nameEn and getItemCode

        // Validate Item Code uniqueness
        itemRepository.findByItemCodeIgnoreCase(request.getItemCode()).ifPresent(i -> { // Use findByItemCodeIgnoreCase and getItemCode
            throw new IllegalArgumentException("Item with code '" + request.getItemCode() + "' already exists."); // Use getItemCode
        });

        Item item = Item.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .itemCode(request.getItemCode()) // Use itemCode field and getItemCode()
                .lastCost(request.getCostPrice()) // Use lastCost field and getCostPrice()
                // Remove setting quantityOnHand as it's not stored directly on Item
                .reorderLevel(request.getReorderLevel())
                // Remove setting category as it doesn't exist on DTO or Model
                .active(true) // Default to active
                .build();

        Item savedItem = itemRepository.save(item);
        log.info("Successfully created Item with ID: {}", savedItem.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_ITEM,
                savedItem.getId(),
                String.format("Created Item: %s (Code: %s), Cost: %s", // Log English name, item code and cost
                        savedItem.getNameEn(), savedItem.getItemCode(), savedItem.getLastCost()) // Use nameEn, itemCode and lastCost
        );

        return mapToDto(savedItem);
    }

    @Override
    @Transactional(readOnly = true)
    public ItemDto getItemById(UUID id) {
        log.debug("Fetching Item by ID: {}", id);
        Item item = findItemOrThrow(id);
        return mapToDto(item);
    }

    @Override
    @Transactional(readOnly = true)
    public ItemDto getItemByItemCode(String itemCode) { // Renamed method parameter
        log.debug("Fetching Item by ItemCode: {}", itemCode); // Update log message
        Item item = itemRepository.findByItemCodeIgnoreCase(itemCode) // Use findByItemCodeIgnoreCase
                .orElseThrow(() -> new ItemNotFoundException(itemCode)); // Use itemCode in exception
        return mapToDto(item);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ItemDto> getAllItems(Pageable pageable) {
        log.debug("Fetching Items page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return itemRepository.findAll(pageable).map(this::mapToDto);
    }

     @Override
    @Transactional(readOnly = true)
    public List<ItemDto> getAllActiveItems() {
        log.debug("Fetching all active Items");
        // Use the existing method that orders by English name
        return itemRepository.findByActiveTrueOrderByNameEnAsc() // Use findByActiveTrueOrderByNameEnAsc
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public ItemDto updateItem(UUID id, UpdateItemRequest request) {
        log.info("Updating Item with ID: {}", id);
        Item item = findItemOrThrow(id);

        // Item code is generally not updatable after creation.
        // If it needs to be updatable, add itemCode to UpdateItemRequest and uncomment validation below.
        /*
        if (!item.getItemCode().equalsIgnoreCase(request.getItemCode())) {
            itemRepository.findByItemCodeIgnoreCase(request.getItemCode()).ifPresent(i -> {
                if (!i.getId().equals(id)) {
                    throw new IllegalArgumentException("Another Item with code '" + request.getItemCode() + "' already exists.");
                }
            });
        }
        */

        String oldDetails = String.format("NameEn: %s, Code: %s, Cost: %s, Active: %s", // Use English Name, Code and Cost in log
                                          item.getNameEn(), item.getItemCode(), item.getLastCost(), item.isActive());

        item.setNameEn(request.getNameEn());
        item.setNameAr(request.getNameAr());
        item.setDescriptionEn(request.getDescriptionEn());
        item.setDescriptionAr(request.getDescriptionAr());
        // item.setItemCode(request.getItemCode()); // Only if updatable
        item.setLastCost(request.getCostPrice()); // Update lastCost from costPrice
        // QuantityOnHand is managed by InventoryService, not updated directly here
        item.setReorderLevel(request.getReorderLevel());
        // Remove setting category as it doesn't exist on DTO or Model
        item.setActive(request.getActive()); // Allow updating active status

        Item updatedItem = itemRepository.save(item);
        log.info("Successfully updated Item with ID: {}", updatedItem.getId());

        String newDetails = String.format("NameEn: %s, Code: %s, Cost: %s, Active: %s", // Use English Name, Code and Cost in log
                                          updatedItem.getNameEn(), updatedItem.getItemCode(), updatedItem.getLastCost(), updatedItem.isActive());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_ITEM,
                updatedItem.getId(),
                "Updated Item. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedItem);
    }

    @Override
    @Transactional
    public void deleteItem(UUID id) {
        log.warn("Attempting to delete Item with ID: {}", id);
        Item item = findItemOrThrow(id);

        // CRITICAL CHECK: Prevent deletion if item has quantity on hand or transactions
        BigDecimal currentStock = inventoryService.getQuantityOnHand(id); // Get stock from InventoryService
        if (currentStock.compareTo(BigDecimal.ZERO) != 0) {
             log.error("Cannot delete Item {} because it has quantity on hand ({}).", id, currentStock);
             throw new IllegalStateException("Cannot delete an item with quantity on hand. Deactivate it instead or adjust stock to zero first.");
        }
        // boolean hasTransactions = inventoryTransactionRepository.existsByItemId(id); // Assuming method exists
        // if (hasTransactions) {
        //     log.error("Cannot delete Item {} because it has associated inventory transactions.", id);
        //     throw new IllegalStateException("Cannot delete an item with inventory transaction history. Deactivate it instead.");
        // }
        log.warn("Deletion check for associated InventoryTransactions for Item {} is not fully implemented.", id);


        itemRepository.delete(item);
        log.warn("Successfully deleted Item with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_ITEM,
                id,
                String.format("Deleted Item: %s (Code: %s)", item.getNameEn(), item.getItemCode()) // Use English Name and Code in log
        );
    }

    @Override
    @Transactional
    public ItemDto activateItem(UUID id) {
        log.info("Activating Item with ID: {}", id);
        return updateItemStatus(id, true);
    }

    @Override
    @Transactional
    public ItemDto deactivateItem(UUID id) {
        log.info("Deactivating Item with ID: {}", id);
        return updateItemStatus(id, false);
    }

    private ItemDto updateItemStatus(UUID id, boolean active) {
        Item item = findItemOrThrow(id);

        if (item.isActive() == active) {
            log.warn("Item {} already has active status {}", id, active);
            return mapToDto(item);
        }

        // If deactivating, consider checking stock level? Business rule.
        BigDecimal currentStock = inventoryService.getQuantityOnHand(id); // Get stock from InventoryService
        if (!active && currentStock.compareTo(BigDecimal.ZERO) != 0) {
             log.warn("Deactivating Item {} which still has quantity on hand ({}).", id, currentStock);
        }

        boolean oldStatus = item.isActive();
        item.setActive(active);
        Item updatedItem = itemRepository.save(item);
        log.info("Successfully set active status to {} for Item ID: {}", active, id);

        auditLogService.logAction(
                getCurrentUserId(),
                active ? AccountingActionType.ACTIVATE : AccountingActionType.DEACTIVATE,
                AccountingConstants.ENTITY_ITEM,
                updatedItem.getId(),
                String.format("Changed active status from %s to %s for Item %s (Code: %s)", // Use English Name and Code in log
                              oldStatus, active, updatedItem.getNameEn(), updatedItem.getItemCode())
        );

        return mapToDto(updatedItem);
    }


    private Item findItemOrThrow(UUID id) {
        return itemRepository.findById(id)
                .orElseThrow(() -> new ItemNotFoundException(id));
    }

    // --- Helper Methods ---

    private ItemDto mapToDto(Item item) {
        if (item == null) return null;
        return ItemDto.builder()
                .id(item.getId())
                .nameEn(item.getNameEn())
                .nameAr(item.getNameAr())
                .descriptionEn(item.getDescriptionEn())
                .descriptionAr(item.getDescriptionAr())
                .itemCode(item.getItemCode()) // Use itemCode
                .costPrice(item.getLastCost()) // Map lastCost to costPrice in DTO
                // .sellingPrice(item.getSellingPrice()) // Map sellingPrice if added to Item model
                // Remove quantityOnHand mapping - it's not directly on Item model
                .reorderLevel(item.getReorderLevel())
                // Remove category as it doesn't exist on Model
                .active(item.isActive())
                .createdDate(item.getCreatedDate())
                .lastModifiedDate(item.getLastModifiedDate())
                .build();
    }

    private SimpleItemDto mapToSimpleDto(Item item) {
        if (item == null) return null;
        // Assuming SimpleItemDto needs cost/price info
        return SimpleItemDto.builder()
                .id(item.getId())
                .nameEn(item.getNameEn())
                .nameAr(item.getNameAr())
                .itemCode(item.getItemCode()) // Use itemCode
                // Remove costPrice as it's not part of SimpleItemDto
                // .sellingPrice(item.getSellingPrice()) // Map sellingPrice if added to Item model
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("00000000-0000-0000-0000-000000000000");
    }
}
