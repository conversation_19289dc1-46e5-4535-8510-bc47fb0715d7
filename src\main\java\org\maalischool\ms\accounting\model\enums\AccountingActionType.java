package org.maalischool.ms.accounting.model.enums;

/**
 * Defines the types of actions that can be logged for accounting entities.
 */
public enum AccountingActionType {
    // General CRUD
    CREATE,
    UPDATE,
    DELETE,
    ACTIVATE,   // Added
    DEACTIVATE, // Added

    // Specific Accounting Actions
    POST_JOURNAL_ENTRY,
    UNPOST_JOURNAL_ENTRY,
    RECORD_PAYMENT,
    ALLOCATE_PAYMENT,
    UNALLOCATE_PAYMENT,
    ASSIGN_FEE,
    UPDATE_FEE_STATUS, // e.g., WAIVE, CANCEL
    APPLY_DISCOUNT,
    REMOVE_DISCOUNT,
    RECORD_EXPENSE,
    UPDATE_BUDGET,
    CREATE_FIXED_ASSET,
    UPDATE_FIXED_ASSET_STATUS,
    DEPRECIATE_ASSET,
    RECORD_INVENTORY_TRANSACTION,
    ADJUST_INVENTORY,

    // Other potential actions
    GENERATE_REPORT,
    EXPORT_DATA,
    IMPORT_DATA,
    SYSTEM_PROCESS, // For automated actions


    DISPOSE_ASSET
}
