package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateExpenseCategoryRequest;
import org.maalischool.ms.accounting.dto.ExpenseCategoryDto;
import org.maalischool.ms.accounting.dto.UpdateExpenseCategoryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing Expense Categories.
 */
public interface ExpenseCategoryService {

    /**
     * Creates a new expense category.
     *
     * @param request the request object containing category details.
     * @return the created expense category DTO.
     */
    ExpenseCategoryDto createExpenseCategory(CreateExpenseCategoryRequest request);

    /**
     * Retrieves an expense category by its ID.
     *
     * @param id the UUID of the expense category.
     * @return the expense category DTO.
     * @throws org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException if the category is not found.
     */
    ExpenseCategoryDto getExpenseCategoryById(UUID id);

    /**
     * Retrieves an expense category by its name (case-insensitive).
     *
     * @param name the name of the expense category.
     * @return the expense category DTO.
     * @throws org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException if the category is not found.
     */
    ExpenseCategoryDto getExpenseCategoryByName(String name);

    /**
     * Retrieves all expense categories, ordered by name.
     *
     * @return a list of all expense category DTOs.
     */
    List<ExpenseCategoryDto> getAllExpenseCategories();

    /**
     * Retrieves all expense categories with pagination.
     *
     * @param searchTerm Optional search term to filter by nameEn or nameAr.
     * @param pageable   pagination information.
     * @return a page of expense category DTOs.
     */
    Page<ExpenseCategoryDto> getAllExpenseCategories(String searchTerm, Pageable pageable);

    /**
     * Updates an existing expense category.
     *
     * @param id      the UUID of the expense category to update.
     * @param request the request object containing updated category details.
     * @return the updated expense category DTO.
     * @throws org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException if the category is not found.
     */
    ExpenseCategoryDto updateExpenseCategory(UUID id, UpdateExpenseCategoryRequest request);

    /**
     * Deletes an expense category by its ID.
     * Consider implications if expenses exist in this category.
     *
     * @param id the UUID of the expense category to delete.
     * @throws org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException if the category is not found.
     */
    void deleteExpenseCategory(UUID id);
}
