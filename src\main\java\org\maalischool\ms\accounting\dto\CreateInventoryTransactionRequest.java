package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.DecimalMin;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.InventoryTransactionType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class CreateInventoryTransactionRequest {
    @NotNull(message = "Item ID cannot be null")
    private UUID itemId;

    @NotNull(message = "Transaction date cannot be null")
    private LocalDate transactionDate;

    @NotNull(message = "Transaction type cannot be null")
    private InventoryTransactionType type; // e.g., PURCHASE, SALE, ADJUSTMENT_IN, ADJUSTMENT_OUT

    @NotNull(message = "Quantity cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Quantity must be positive (use transaction type for direction)")
    private BigDecimal quantity; // Changed to BigDecimal

    @Size(max = 100, message = "Reference number must be less than 100 characters")
    private String referenceNumber; // e.g., PO number, SO number, Adjustment ID

    @NotBlank(message = "Reason cannot be blank")
    @Size(max = 500, message = "Reason must be less than 500 characters")
    private String reason;
}
