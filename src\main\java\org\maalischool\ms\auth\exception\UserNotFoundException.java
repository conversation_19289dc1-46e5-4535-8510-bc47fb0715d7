package org.maalischool.ms.auth.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND) // 404 Not Found
public class UserNotFoundException extends RuntimeException {
    public UserNotFoundException(String identifier) {
        super("User not found with identifier: " + identifier);
    }
     public UserNotFoundException(UUID id) {
        super("User not found with ID: " + id);
    }
}
