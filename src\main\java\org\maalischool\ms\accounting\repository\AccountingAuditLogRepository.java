package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.AccountingAuditLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor; // Import JpaSpecificationExecutor
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Repository
public interface AccountingAuditLogRepository extends JpaRepository<AccountingAuditLog, UUID>, JpaSpecificationExecutor<AccountingAuditLog> { // Extend JpaSpecificationExecutor

    // Keep specific finders if they are used elsewhere or provide specific optimizations
    Page<AccountingAuditLog> findByUserId(UUID userId, Pageable pageable);

    Page<AccountingAuditLog> findByEntityTypeAndEntityId(String entityType, UUID entityId, Pageable pageable);

    Page<AccountingAuditLog> findByActionTimestampBetween(Instant start, Instant end, Pageable pageable);

    List<AccountingAuditLog> findTop50ByOrderByActionTimestampDesc(); // Example query

    /**
     * Finds all audit logs for a specific entity type and ID, ordered by timestamp descending.
     *
     * @param entityType The entity type.
     * @param entityId   The entity ID.
     * @return A list of matching audit logs.
     */
    List<AccountingAuditLog> findAllByEntityTypeAndEntityIdOrderByActionTimestampDesc(String entityType, UUID entityId);
}
