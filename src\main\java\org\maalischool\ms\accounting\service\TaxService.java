package org.maalischool.ms.accounting.service;

import java.util.UUID;

import org.maalischool.ms.accounting.dto.CreateTaxRequest;
import org.maalischool.ms.accounting.dto.TaxDto;
import org.maalischool.ms.accounting.dto.UpdateTaxRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface TaxService {

    /**
     * Creates a new tax.
     *
     * @param createTaxRequest DTO containing tax creation data.
     * @return The created TaxDto.
     */
    TaxDto createTax(CreateTaxRequest createTaxRequest);

    /**
     * Retrieves a tax by its ID.
     *
     * @param id The UUID of the tax.
     * @return The TaxDto.
     * @throws org.maalischool.ms.accounting.exception.TaxNotFoundException if tax not found.
     */
    TaxDto getTaxById(UUID id);

    /**
     * Retrieves all taxes with pagination.
     *
     * @param pageable Pagination information.
     * @return A Page of TaxDto.
     */
    Page<TaxDto> getAllTaxes(Pageable pageable);

    /**
     * Updates an existing tax.
     *
     * @param id               The UUID of the tax to update.
     * @param updateTaxRequest DTO containing tax update data.
     * @return The updated TaxDto.
     * @throws org.maalischool.ms.accounting.exception.TaxNotFoundException if tax not found.
     */
    TaxDto updateTax(UUID id, UpdateTaxRequest updateTaxRequest);

    /**
     * Deletes a tax by its ID.
     *
     * @param id The UUID of the tax to delete.
     * @throws org.maalischool.ms.accounting.exception.TaxNotFoundException if tax not found.
     */
    void deleteTax(UUID id);
}