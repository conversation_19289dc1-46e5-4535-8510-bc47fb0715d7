package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

/**
 * Represents the many-to-many relationship between StudentFee and Discount.
 * Tracks which discounts are applied to which student fees.
 */
@Entity
@Table(name = "acc_student_fee_discounts", indexes = {
        @Index(name = "idx_studentfeediscount_studentfee", columnList = "student_fee_id"),
        @Index(name = "idx_studentfeediscount_discount", columnList = "discount_id")
}, uniqueConstraints = {
        // Ensure a specific discount can only be applied once to a specific student fee
        @UniqueConstraint(columnNames = {"student_fee_id", "discount_id"})
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"studentFee", "discount"}) // Equality based on the linked entities
@EntityListeners(AuditingEntityListener.class)
public class StudentFeeDiscount {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "student_fee_id", nullable = false)
    private StudentFee studentFee;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "discount_id", nullable = false)
    private Discount discount;

    @CreatedDate
    @Column(nullable = false, updatable = false)
    private Instant appliedDate;

    // Note: No amount stored here; the discount calculation logic resides within the StudentFee entity
    // based on the linked Discount's type and value.
}
