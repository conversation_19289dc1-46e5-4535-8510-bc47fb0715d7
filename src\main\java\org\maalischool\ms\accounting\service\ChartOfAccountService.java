package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.ChartOfAccountDto;
import org.maalischool.ms.accounting.dto.CreateChartOfAccountRequest;
import org.maalischool.ms.accounting.dto.UpdateChartOfAccountRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.Optional;
import java.util.UUID;


import org.maalischool.ms.accounting.model.enums.AccountingCategory; // Import AccountingCategory

public interface ChartOfAccountService {

    ChartOfAccountDto createChartOfAccount(CreateChartOfAccountRequest request);

    Optional<ChartOfAccountDto> getChartOfAccountById(UUID id);

    Optional<ChartOfAccountDto> getChartOfAccountByNumber(String accountNumber);

    Page<ChartOfAccountDto> getAllActiveChartOfAccounts(String accountNumber, String nameEn, String nameAr, AccountingCategory category, Pageable pageable); // Added category filter

    Page<ChartOfAccountDto> getAllChartOfAccounts(String accountNumber, String nameEn, String nameAr, AccountingCategory category, Pageable pageable); // Added category filter

    ChartOfAccountDto updateChartOfAccount(UUID id, UpdateChartOfAccountRequest request);

    void deleteChartOfAccount(UUID id); // Consider soft delete (setting active=false) via update instead
}
