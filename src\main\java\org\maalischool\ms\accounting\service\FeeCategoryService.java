package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateFeeCategoryRequest;
import org.maalischool.ms.accounting.dto.FeeCategoryDto;
import org.maalischool.ms.accounting.dto.SimpleFeeCategoryDto; // Added import
import org.maalischool.ms.accounting.dto.UpdateFeeCategoryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List; // Added import

import java.util.List;
import java.util.UUID;

public interface FeeCategoryService {

    FeeCategoryDto createFeeCategory(CreateFeeCategoryRequest request);

    FeeCategoryDto getFeeCategoryById(UUID id);

    // Removed getFeeCategoryByName and getAllFeeCategories as they are replaced by searchFeeCategories

    /**
     * Searches for Fee Categories based on a search term (matching nameEn or nameAr)
     * or retrieves all categories if the search term is blank or null. Supports pagination.
     *
     * @param searchTerm The term to search for (can be null or blank to retrieve all).
     * @param pageable   Pagination and sorting information.
     * @return A Page of FeeCategoryDto objects.
     */
    Page<FeeCategoryDto> searchFeeCategories(String searchTerm, Pageable pageable);

    /**
     * Retrieves all Fee Categories as a simplified list (ID, names).
     *
     * @return A List of SimpleFeeCategoryDto objects.
     */
    List<SimpleFeeCategoryDto> getAllFeeCategoriesSimple(); // Added method

    FeeCategoryDto updateFeeCategory(UUID id, UpdateFeeCategoryRequest request);

    void deleteFeeCategory(UUID id); // Consider implications if fees exist in this category
}
