package org.maalischool.ms.accounting.util;

public final class AccountingConstants {

    private AccountingConstants() {
        // Prevent instantiation
    }

    // Entity Type Names for Auditing
    public static final String ENTITY_CHART_OF_ACCOUNT = "ChartOfAccount";
    public static final String ENTITY_JOURNAL_ENTRY = "JournalEntry";
    public static final String ENTITY_JOURNAL_ENTRY_LINE = "JournalEntryLine";
    public static final String ENTITY_FEE_CATEGORY = "FeeCategory";
    public static final String ENTITY_FEE = "Fee";
    public static final String ENTITY_STUDENT_FEE = "StudentFee";
    public static final String ENTITY_PAYMENT_METHOD = "PaymentMethod";
    public static final String ENTITY_RECEIPT = "Receipt";
    public static final String ENTITY_PAYMENT = "Payment";
    public static final String ENTITY_DISCOUNT = "Discount";
    public static final String ENTITY_EXPENSE_CATEGORY = "ExpenseCategory";
    public static final String ENTITY_EXPENSE = "Expense";
    public static final String ENTITY_BUDGET = "Budget";
    public static final String ENTITY_FIXED_ASSET = "FixedAsset";
    public static final String ENTITY_ITEM = "Item";
    public static final String ENTITY_INVENTORY_TRANSACTION = "InventoryTransaction";

    // Add other constants as needed
}
