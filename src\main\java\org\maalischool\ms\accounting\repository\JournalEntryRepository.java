package org.maalischool.ms.accounting.repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import org.maalischool.ms.accounting.model.JournalEntry;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor; // Import JpaSpecificationExecutor
import org.springframework.stereotype.Repository;

@Repository
// Extend JpaSpecificationExecutor
public interface JournalEntryRepository extends JpaRepository<JournalEntry, UUID>, JpaSpecificationExecutor<JournalEntry> {

    // Keep non-paginated version if needed elsewhere, otherwise remove
    List<JournalEntry> findByEntryDateBetweenOrderByEntryDateDesc(LocalDate startDate, LocalDate endDate);

    // Remove the specific paginated method, findAll(Specification, Pageable) will replace it
    // Page<JournalEntry> findByEntryDateBetweenOrderByEntryDateDesc(LocalDate startDate, LocalDate endDate, Pageable pageable);

    // Keep this if needed for other specific use cases, otherwise remove
    List<JournalEntry> findByReferenceNumberContainingIgnoreCase(String referenceNumber);

    List<JournalEntry> findByPostedDateIsNullOrderByEntryDateDesc();

    Optional<JournalEntry> findTopByJournalEntryNumberStartingWithOrderByJournalEntryNumberDesc(String yearPrefix);
}
