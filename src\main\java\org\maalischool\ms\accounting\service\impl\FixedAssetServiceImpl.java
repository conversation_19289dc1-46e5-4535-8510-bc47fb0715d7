package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.*;
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException;
import org.maalischool.ms.accounting.exception.FixedAssetNotFoundException;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.FixedAsset;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.FixedAssetRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.FixedAssetService;
import org.maalischool.ms.accounting.model.enums.DepreciationMethod; // Add missing import
// Import JournalEntryService if disposal creates journal entries
// import org.maalischool.ms.accounting.service.JournalEntryService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FixedAssetServiceImpl implements FixedAssetService {

    private static final Logger log = LoggerFactory.getLogger(FixedAssetServiceImpl.class);
    private final FixedAssetRepository fixedAssetRepository;
    private final ChartOfAccountRepository chartOfAccountRepository;
    private final AccountingAuditLogService auditLogService;
    // private final JournalEntryService journalEntryService; // Inject if needed for disposal

    @Override
    @Transactional
    public FixedAssetDto createFixedAsset(CreateFixedAssetRequest request) {
        log.info("Creating new Fixed Asset: NameEn={}, AcquisitionDate={}", request.getNameEn(), request.getAcquisitionDate()); // Log English name

        ChartOfAccount assetAccount = chartOfAccountRepository.findById(request.getAssetAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getAssetAccountId())); // Use UUID constructor
        ChartOfAccount accumulatedDepreciationAccount = chartOfAccountRepository.findById(request.getAccumulatedDepreciationAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getAccumulatedDepreciationAccountId())); // Use UUID constructor
        ChartOfAccount depreciationExpenseAccount = chartOfAccountRepository.findById(request.getDepreciationExpenseAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getDepreciationExpenseAccountId())); // Use UUID constructor

        FixedAsset fixedAsset = FixedAsset.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .assetCode(request.getAssetCode())
//                .serialNumber(request.getSerialNumber())
                .purchaseDate(request.getAcquisitionDate())
                .purchaseCost(request.getAcquisitionCost())
                .depreciationMethod(request.getDepreciationMethod().name()) // Convert enum to String
                .usefulLifeYears(request.getUsefulLifeYears())
                .salvageValue(request.getSalvageValue())
                .assetAccount(assetAccount)
                .accumulatedDepreciationAccount(accumulatedDepreciationAccount)
                .depreciationExpenseAccount(depreciationExpenseAccount)
                .location(request.getLocation())
                .status(AssetStatus.ACTIVE) // Set the initial status to ACTIVE
//                .disposed(false) // Initially not disposed
                .build();

        FixedAsset savedAsset = fixedAssetRepository.save(fixedAsset);
        log.info("Successfully created Fixed Asset with ID: {}", savedAsset.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_FIXED_ASSET,
                savedAsset.getId(),
                String.format("Created Fixed Asset: %s (Tag: %s), Cost: %s, Date: %s",
                        savedAsset.getNameEn(), savedAsset.getAssetCode(), // Log English name
                        savedAsset.getPurchaseCost(), savedAsset.getPurchaseDate())
        );

        // TODO: Consider creating initial acquisition Journal Entry if needed
        // journalEntryService.createJournalEntry(...)

        return mapToDto(savedAsset);
    }

    @Override
    @Transactional(readOnly = true)
    public FixedAssetDto getFixedAssetById(UUID id) {
        log.debug("Fetching Fixed Asset by ID: {}", id);
        FixedAsset asset = findAssetOrThrow(id);
        return mapToDto(asset);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<FixedAssetDto> getAllFixedAssets(Pageable pageable) {
        log.debug("Fetching Fixed Assets page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        // Optionally filter out disposed assets: findByDisposedFalse(pageable)
        return fixedAssetRepository.findAll(pageable).map(this::mapToDto);
    }

    @Override
    @Transactional
    public FixedAssetDto updateFixedAsset(UUID id, UpdateFixedAssetRequest request) {
        log.info("Updating Fixed Asset with ID: {}", id);
        FixedAsset asset = findAssetOrThrow(id);

        if (asset.getStatus() == AssetStatus.DISPOSED) {
            log.error("Cannot update a disposed Fixed Asset (ID: {})", id);
            throw new IllegalStateException("Cannot update a fixed asset that has already been disposed.");
        }

        // Validate accounts if they are being changed (though changing accounts might be complex)
        ChartOfAccount assetAccount = chartOfAccountRepository.findById(request.getAssetAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getAssetAccountId())); // Use UUID constructor
        ChartOfAccount accumulatedDepreciationAccount = chartOfAccountRepository.findById(request.getAccumulatedDepreciationAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getAccumulatedDepreciationAccountId())); // Use UUID constructor
        ChartOfAccount depreciationExpenseAccount = chartOfAccountRepository.findById(request.getDepreciationExpenseAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getDepreciationExpenseAccountId())); // Use UUID constructor


        String oldDetails = String.format("NameEn: %s, Tag: %s, Cost: %s", // Log English name
                asset.getNameEn(), asset.getAssetCode(),
                asset.getPurchaseCost());

        // Update mutable fields
        asset.setNameEn(request.getNameEn());
        asset.setNameAr(request.getNameAr());
        asset.setDescriptionEn(request.getDescriptionEn());
        asset.setDescriptionAr(request.getDescriptionAr());
        asset.setAssetCode(request.getAssetCode());
//        asset.setSerialNumber(request.getSerialNumber());
//        asset.setPurchaseDate(request.getAcquisitionDate());
//        asset.setPurchaseCost(request.getAcquisitionCost());
        asset.setDepreciationMethod(request.getDepreciationMethod().name()); // Convert enum to String
        asset.setUsefulLifeYears(request.getUsefulLifeYears());
        asset.setSalvageValue(request.getSalvageValue());
        asset.setAssetAccount(assetAccount); // Allow changing accounts if business logic permits
        asset.setAccumulatedDepreciationAccount(accumulatedDepreciationAccount);
        asset.setDepreciationExpenseAccount(depreciationExpenseAccount);
        asset.setLocation(request.getLocation());
        // Cannot update 'disposed' status via this method

        FixedAsset updatedAsset = fixedAssetRepository.save(asset);
        log.info("Successfully updated Fixed Asset with ID: {}", updatedAsset.getId());

        String newDetails = String.format("NameEn: %s, Tag: %s", // Log English name
                updatedAsset.getNameEn(), updatedAsset.getAssetCode());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_FIXED_ASSET,
                updatedAsset.getId(),
                "Updated Fixed Asset. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedAsset);
    }

    @Override
    @Transactional
    public void disposeFixedAsset(UUID id, String disposalNotes /*, LocalDate disposalDate, BigDecimal proceeds */) {
        log.warn("Attempting to dispose Fixed Asset with ID: {}", id);
        FixedAsset asset = findAssetOrThrow(id);

        if (asset.getStatus() == AssetStatus.DISPOSED) {
            log.warn("Fixed Asset {} is already disposed.", id);
            throw new IllegalStateException("Fixed asset has already been disposed.");
        }

        // --- Disposal Logic ---
        // 1. Mark the asset as disposed
        asset.setStatus(AssetStatus.DISPOSED);
//        asset.setDisposalDate(LocalDate.now()); // Or use disposalDate parameter
//        asset.setDisposalNotes(disposalNotes);
        // asset.setDisposalProceeds(proceeds); // If tracking proceeds

        FixedAsset disposedAsset = fixedAssetRepository.save(asset);
        log.warn("Successfully marked Fixed Asset {} as disposed.", id);

        // 2. TODO: Create Journal Entry for Disposal
        //    - Debit Accumulated Depreciation (for total depreciation taken)
        //    - Debit Cash/Bank (for proceeds, if any)
        //    - Debit Loss on Disposal (if applicable)
        //    - Credit Fixed Asset account (for original cost)
        //    - Credit Gain on Disposal (if applicable)
        //    This requires calculating total depreciation up to disposal date.
        //    journalEntryService.createJournalEntry(buildDisposalJournalEntryRequest(disposedAsset));
        log.warn("Journal entry creation for fixed asset disposal (ID: {}) is not implemented.", id);


        // --- Audit Log ---
        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DISPOSE_ASSET, // Use a specific action type
                AccountingConstants.ENTITY_FIXED_ASSET,
                id,
                String.format("Disposed Fixed Asset: %s (Tag: %s). Notes: %s",
                        disposedAsset.getNameEn(), disposedAsset.getAssetCode(), // Log English name
                        disposalNotes)
        );
    }

    private FixedAsset findAssetOrThrow(UUID id) {
        return fixedAssetRepository.findById(id)
                .orElseThrow(() -> new FixedAssetNotFoundException(id));
    }

    // --- Helper Methods ---

    private FixedAssetDto mapToDto(FixedAsset asset) {
        if (asset == null) return null;
        return FixedAssetDto.builder()
                .id(asset.getId())
                .nameEn(asset.getNameEn())
                .nameAr(asset.getNameAr())
                .descriptionEn(asset.getDescriptionEn())
                .descriptionAr(asset.getDescriptionAr())
                .assetCode(asset.getAssetCode())
//                .serialNumber(asset.getSerialNumber())
                .acquisitionDate(asset.getPurchaseDate())
                .acquisitionCost(asset.getPurchaseCost())
                // Convert String from DB back to Enum for DTO
                .depreciationMethod(asset.getDepreciationMethod() != null ? DepreciationMethod.valueOf(asset.getDepreciationMethod()) : null)
                .usefulLifeYears(asset.getUsefulLifeYears())
                .salvageValue(asset.getSalvageValue())
                .assetAccount(mapAccountToSimpleDto(asset.getAssetAccount()))
                .accumulatedDepreciationAccount(mapAccountToSimpleDto(asset.getAccumulatedDepreciationAccount()))
                .depreciationExpenseAccount(mapAccountToSimpleDto(asset.getDepreciationExpenseAccount()))
//                .location(asset.getLocation())
                .status(asset.getStatus())
//                .disposalDate(asset.getDisposalDate())
//                .disposalNotes(asset.getDisposalNotes())
                // Add calculated fields if needed (e.g., current book value)
                .createdDate(asset.getCreatedDate())
                .lastModifiedDate(asset.getLastModifiedDate())
                .build();
    }

    private SimpleChartOfAccountDto mapAccountToSimpleDto(ChartOfAccount account) {
        if (account == null) return null;
        return SimpleChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn()) // Use English name
                .nameAr(account.getNameAr())   // Use Arabic name
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("********-0000-0000-0000-********0000");
    }

    // Placeholder for building disposal JE request
    /*
    private CreateJournalEntryRequest buildDisposalJournalEntryRequest(FixedAsset asset) {
        // Complex logic to calculate depreciation, gain/loss and build lines
        log.error("buildDisposalJournalEntryRequest is not implemented!");
        throw new UnsupportedOperationException("Journal entry creation for disposal not implemented.");
    }
    */
}
