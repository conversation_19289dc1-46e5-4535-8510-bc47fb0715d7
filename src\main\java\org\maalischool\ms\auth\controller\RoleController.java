package org.maalischool.ms.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.*;
import org.maalischool.ms.auth.exception.RoleNotFoundException;
import org.maalischool.ms.auth.service.RoleService;
import org.maalischool.ms.auth.service.UserService; // Import UserService
import org.maalischool.ms.exception.ErrorResponse;
import org.springframework.context.MessageSource; // Import MessageSource
import org.springframework.context.i18n.LocaleContextHolder; // Import LocaleContextHolder
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/admin/roles") // Admin endpoint prefix
@RequiredArgsConstructor
@Tag(name = "Role Management", description = "Endpoints for managing roles and their permissions (Requires ADMIN role)")
@SecurityRequirement(name = "Bearer Authentication") // Indicate endpoints require Bearer token
@PreAuthorize("hasRole('ADMIN')") // Secure all methods in this controller
public class RoleController {

    private final RoleService roleService;
    private final UserService userService; // Inject UserService for assigning roles to users
    private final MessageSource messageSource; // Inject MessageSource

    // Helper to get localized message
    private String getMessage(String code, Object... args) {
        return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
    }

    @Operation(summary = "Get all roles")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of roles retrieved"),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping
    public ResponseEntity<List<RoleDto>> getAllRoles() {
        return ResponseEntity.ok(roleService.findAllRoles());
    }

    @Operation(summary = "Get role by ID")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role found"),
            @ApiResponse(responseCode = "404", description = "Role not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping("/{id}")
    public ResponseEntity<RoleDto> getRoleById(@PathVariable UUID id) {
        return roleService.findRoleById(id)
                .map(ResponseEntity::ok)
                .orElseThrow(() -> new RoleNotFoundException(getMessage("error.role.notfound.id", id))); // Use localized message
    }

    @Operation(summary = "Create a new role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Role created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data or permission not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "409", description = "Role name already exists",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping
    public ResponseEntity<RoleDto> createRole(@Valid @RequestBody CreateRoleRequest request) {
        RoleDto createdRole = roleService.createRole(request);
        return ResponseEntity.status(HttpStatus.CREATED).body(createdRole);
    }

    @Operation(summary = "Update an existing role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role updated successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data or permission not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "404", description = "Role not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "409", description = "Updated role name conflicts with an existing one",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PutMapping("/{id}")
    public ResponseEntity<RoleDto> updateRole(@PathVariable UUID id, @Valid @RequestBody UpdateRoleRequest request) {
        RoleDto updatedRole = roleService.updateRole(id, request);
        return ResponseEntity.ok(updatedRole);
    }

    @Operation(summary = "Delete a role")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Role deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Role not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "409", description = "Conflict - Role might be in use",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRole(@PathVariable UUID id) {
        roleService.deleteRole(id);
        return ResponseEntity.noContent().build();
    }

    // --- Role-Permission Assignment ---

    @Operation(summary = "Assign permissions to a role", description = "Replaces all existing permissions for the role with the provided list.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Permissions assigned successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input data (e.g., empty permission list)",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "404", description = "Role or one of the Permissions not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/{roleId}/permissions")
    public ResponseEntity<RoleDto> assignPermissionsToRole(
            @PathVariable UUID roleId,
            @RequestBody List<String> permissionNames // Simple list of names in body
    ) {
        // Basic validation: ensure list is not null, though @RequestBody handles empty body
        if (permissionNames == null) {
             return ResponseEntity.badRequest().build(); // Or throw validation exception
        }
        RoleDto updatedRole = roleService.assignPermissionsToRole(roleId, permissionNames);
        return ResponseEntity.ok(updatedRole);
    }

     // --- User-Role Assignment ---
     // Consider moving this to a dedicated AdminUserController if managing users becomes complex

    @Operation(summary = "Assign a role to a user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role assigned successfully"),
            @ApiResponse(responseCode = "404", description = "User or Role not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/users/{userId}/assign")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> assignRoleToUser(
            @PathVariable UUID userId,
            @RequestParam String roleName // Assign role by name via query parameter
    ) {
        userService.assignRoleToUser(userId, roleName);
        return ResponseEntity.ok(org.maalischool.ms.auth.dto.ApiResponse.builder()
                .success(true)
                .message(getMessage("response.role.assigned", roleName, userId)) // Use localized message
                .build());
    }

    @Operation(summary = "Remove a role from a user")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Role removed successfully"),
            @ApiResponse(responseCode = "404", description = "User or Role not found",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "403", description = "Forbidden",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/users/{userId}/remove")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> removeRoleFromUser(
            @PathVariable UUID userId,
            @RequestParam String roleName // Remove role by name via query parameter
    ) {
        userService.removeRoleFromUser(userId, roleName);
         return ResponseEntity.ok(org.maalischool.ms.auth.dto.ApiResponse.builder()
                 .success(true)
                 .message(getMessage("response.role.removed", roleName, userId)) // Use localized message
                 .build());
    }
}
