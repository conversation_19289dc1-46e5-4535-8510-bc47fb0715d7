package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class FixedAssetNotFoundException extends RuntimeException {

    public FixedAssetNotFoundException(UUID id) {
        super("Fixed Asset not found with ID: " + id);
    }

    public FixedAssetNotFoundException(String assetTag) {
        super("Fixed Asset not found with Asset Tag: " + assetTag);
    }

     public FixedAssetNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
