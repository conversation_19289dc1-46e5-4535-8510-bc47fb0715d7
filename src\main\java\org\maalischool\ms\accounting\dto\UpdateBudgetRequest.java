package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;

@Data // Ensures getters/setters/etc.
@Builder
public class UpdateBudgetRequest {
    // Usually only amount and notes are updatable for a specific budget entry
    @NotNull(message = "Budgeted amount cannot be null")
    @DecimalMin(value = "0.0", message = "Budgeted amount must be non-negative")
    private BigDecimal budgetedAmount;

    @Size(max = 500, message = "Notes must be less than 500 characters")
    private String notes;
}
