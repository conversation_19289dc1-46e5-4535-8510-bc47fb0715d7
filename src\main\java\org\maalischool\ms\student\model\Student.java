package org.maalischool.ms.student.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.maalischool.ms.auth.model.User; // Import User
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "student", uniqueConstraints = {
        @UniqueConstraint(columnNames = "admissionNumber"),
        @UniqueConstraint(columnNames = { "id_type", "national_id" }) // Assuming ID type + number is unique
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = { "userAccount", "guardian", "nationality" })
@EntityListeners(AuditingEntityListener.class)
public class Student {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    // Link to the User account for login credentials etc.
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", unique = true)
    private User userAccount;

    @NotBlank(message = "{validation.student.admissionNumber.notBlank}")
    @Size(max = 50, message = "{validation.student.admissionNumber.size}")
    @Column(nullable = false, unique = true, length = 50)
    private String admissionNumber;

    @NotNull(message = "{validation.student.dateOfBirth.notNull}")
    @PastOrPresent(message = "{validation.student.dateOfBirth.pastOrPresent}")
    @Column(nullable = false)
    private LocalDate dateOfBirth;

    @NotNull(message = "{validation.student.gender.notNull}")
    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10)
    private Gender gender;

    @Size(max = 255, message = "{validation.student.address.size}")
    private String address;

    @NotBlank(message = "{validation.student.nationalId.notBlank}") // New field validation
    @Size(max = 50, message = "{validation.student.nationalId.size}") // New field validation
    @Column(name = "national_id", nullable = false, length = 50)
    private String nationalId;

    @NotNull(message = "{validation.student.idType.notNull}") // New field validation
    @Enumerated(EnumType.STRING)
    @Column(name = "id_type", nullable = false, length = 20)
    private IdType idType;

    // Many Students belong to one Guardian
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "guardian_id") // Renamed column
    private Guardian guardian; // Renamed relationship

    @NotNull(message = "{validation.student.guardianType.notNull}") // New field validation
    @Enumerated(EnumType.STRING)
    @Column(name = "guardian_type", nullable = false, length = 20)
    private GuardianType guardianType; // New field

    // Nationality relationship
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nationality_id")
    private Country nationality;

    // TODO: Add relationships for Grades and Files later
    // @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, orphanRemoval =
    // true)
    // private List<StudentGrade> grades = new ArrayList<>();
    //
    // @OneToMany(mappedBy = "student", cascade = CascadeType.ALL, orphanRemoval =
    // true)
    // private List<StudentFile> files = new ArrayList<>();

    // TODO: Add relationship to Section (current enrollment) later
    // @ManyToOne(fetch = FetchType.LAZY)
    // @JoinColumn(name = "current_section_id")
    // private Section currentSection;

    // --- Auditing Fields ---
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;
}
