package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.AIReportMessage;

/**
 * Service interface for sending AI report messages to RabbitMQ
 */
public interface AIReportMessageProducer {
    
    /**
     * Send an AI report message to the queue for processing
     * 
     * @param message The message containing report details
     */
    void sendReportMessage(AIReportMessage message);
}
