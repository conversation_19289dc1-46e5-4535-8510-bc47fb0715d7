package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.ExpenseCategory;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor; // Import JpaSpecificationExecutor
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ExpenseCategoryRepository extends JpaRepository<ExpenseCategory, UUID>, JpaSpecificationExecutor<ExpenseCategory> { // Extend JpaSpecificationExecutor

    Optional<ExpenseCategory> findByNameEnIgnoreCase(String nameEn); // Find by English name

    List<ExpenseCategory> findAllByOrderByNameEnAsc(); // Order by English name

    // Find top-level categories
    List<ExpenseCategory> findByParentCategoryIsNullOrderByNameEnAsc(); // Order by English name

    // Find direct children of a category
    List<ExpenseCategory> findByParentCategoryIdOrderByNameEnAsc(UUID parentCategoryId); // Order by English name
}
