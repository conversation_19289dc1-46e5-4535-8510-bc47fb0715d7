package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.CONFLICT) // 409 Conflict
public class JournalEntryAlreadyPostedException extends RuntimeException {

    public JournalEntryAlreadyPostedException(UUID journalEntryId) {
        super("Journal Entry with ID " + journalEntryId + " is already posted and cannot be modified or deleted.");
    }

    public JournalEntryAlreadyPostedException(UUID journalEntryId, String message) {
        super(message + " (ID: " + journalEntryId + ")");
    }
}
