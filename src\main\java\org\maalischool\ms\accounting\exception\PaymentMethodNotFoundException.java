package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class PaymentMethodNotFoundException extends RuntimeException {

    public PaymentMethodNotFoundException(UUID id) {
        super("Payment Method not found with ID: " + id);
    }

    public PaymentMethodNotFoundException(String name) {
        super("Payment Method not found with name: " + name);
    }

     public PaymentMethodNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
