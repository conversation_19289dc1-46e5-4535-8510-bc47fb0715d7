package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Payment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Repository
public interface PaymentRepository extends JpaRepository<Payment, UUID> {

    List<Payment> findByReceiptIdOrderByPaymentDateAsc(UUID receiptId);

    List<Payment> findByStudentFeeIdOrderByPaymentDateAsc(UUID studentFeeId);

    // Calculate total payments made for a specific student fee
    @Query("SELECT COALESCE(SUM(p.amount), 0) FROM Payment p WHERE p.studentFee.id = :studentFeeId")
    BigDecimal calculateTotalPaidForStudentFee(@Param("studentFeeId") UUID studentFeeId);
}
