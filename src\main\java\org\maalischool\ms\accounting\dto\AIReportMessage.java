package org.maalischool.ms.accounting.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.Map;
import java.util.UUID;

/**
 * DTO for AI report message to be sent to RabbitMQ
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIReportMessage {
    
    private UUID reportId;
    private String reportType;
    private LocalDate startDate;
    private LocalDate endDate;
    private Map<String, Object> filters; // Serialized to JSON
    private String audience; // Target audience for the report
}
