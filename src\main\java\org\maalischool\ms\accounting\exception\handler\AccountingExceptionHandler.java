package org.maalischool.ms.accounting.exception.handler;

import jakarta.validation.ConstraintViolationException;
import org.maalischool.ms.accounting.exception.*;
import org.maalischool.ms.exception.ErrorResponse; // Assuming this is the shared ErrorResponse DTO
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import java.time.Instant;
import java.time.LocalDateTime; // Add this import
import java.util.HashMap;
import java.util.List; // Add this import (needed for Map.of usage below)
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Global Exception Handler for the Accounting module.
 * Catches specific accounting exceptions and common Spring exceptions,
 * returning a standardized ErrorResponse.
 */
@RestControllerAdvice(basePackages = "org.maalischool.ms.accounting") // Target only accounting controllers
public class AccountingExceptionHandler extends ResponseEntityExceptionHandler {

    private static final Logger log = LoggerFactory.getLogger(AccountingExceptionHandler.class);

    @Autowired
    private MessageSource messageSource; // For localized error messages

    private String getMessage(String code, Object... args) {
        // Attempt to resolve the message, default to the code itself if not found
        return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
    }

    // --- Custom Accounting Exception Handlers ---

    @ExceptionHandler(ChartOfAccountNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleChartOfAccountNotFound(ChartOfAccountNotFoundException ex, WebRequest request) {
        log.warn("ChartOfAccountNotFoundException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.NOT_FOUND.value(),
                getMessage("error.chartOfAccount.notFound"), // Example message code
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(DuplicateChartOfAccountNumberException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ResponseEntity<ErrorResponse> handleDuplicateChartOfAccountNumber(DuplicateChartOfAccountNumberException ex, WebRequest request) {
        log.warn("DuplicateChartOfAccountNumberException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.CONFLICT.value(),
                getMessage("error.chartOfAccount.duplicateNumber"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

    @ExceptionHandler(FeeCategoryNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleFeeCategoryNotFound(FeeCategoryNotFoundException ex, WebRequest request) {
        log.warn("FeeCategoryNotFoundException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.NOT_FOUND.value(),
                getMessage("error.feeCategory.notFound"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

     @ExceptionHandler(FeeNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleFeeNotFound(FeeNotFoundException ex, WebRequest request) {
        log.warn("FeeNotFoundException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.NOT_FOUND.value(),
                getMessage("error.fee.notFound"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(JournalEntryNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ResponseEntity<ErrorResponse> handleJournalEntryNotFound(JournalEntryNotFoundException ex, WebRequest request) {
        log.warn("JournalEntryNotFoundException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.NOT_FOUND.value(),
                getMessage("error.journalEntry.notFound"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.NOT_FOUND);
    }

    @ExceptionHandler(JournalEntryAlreadyPostedException.class)
    @ResponseStatus(HttpStatus.CONFLICT) // Or BAD_REQUEST depending on semantics
    public ResponseEntity<ErrorResponse> handleJournalEntryAlreadyPosted(JournalEntryAlreadyPostedException ex, WebRequest request) {
        log.warn("JournalEntryAlreadyPostedException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.CONFLICT.value(),
                getMessage("error.journalEntry.alreadyPosted"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }

     @ExceptionHandler(JournalEntryUnbalancedException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleJournalEntryUnbalanced(JournalEntryUnbalancedException ex, WebRequest request) {
        log.warn("JournalEntryUnbalancedException: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                getMessage("error.journalEntry.unbalanced"),
                ex.getMessage(),
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    // Add handlers for other custom exceptions (Discount, Expense, Budget, Asset, Item etc.) here...
    // Example:
    // @ExceptionHandler(DiscountNotFoundException.class) ...
    // @ExceptionHandler(InvalidPaymentAmountException.class) ...


    // --- Standard Spring Boot Exception Handlers ---

    /**
     * Handles validation errors for request bodies (@RequestBody).
     */
    @Override
    protected ResponseEntity<Object> handleMethodArgumentNotValid(
            MethodArgumentNotValidException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.warn("Validation Error: {}", ex.getMessage());
        Map<String, String> errors = new HashMap<>();
        ex.getBindingResult().getAllErrors().forEach((error) -> {
            String fieldName = ((FieldError) error).getField();
            String errorMessage = error.getDefaultMessage();
            // Use messageSource to potentially translate default messages
            String resolvedMessage = messageSource.getMessage(errorMessage, null, errorMessage, LocaleContextHolder.getLocale());
            errors.put(fieldName, resolvedMessage);
        });

        // Use the 4-argument constructor (status, error, message, path)
        String detailedErrorMessage = errors.entrySet().stream()
                .map(entry -> entry.getKey() + ": " + entry.getValue())
                .collect(Collectors.joining(", "));

        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),               // status
                getMessage("error.validation.failed"),        // error (title/code)
                detailedErrorMessage,                         // message (details)
                request.getDescription(false)                 // path
        );
        log.debug("Validation errors: {}", errors); // Log detailed errors separately
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles validation errors for request parameters (@RequestParam, @PathVariable).
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleConstraintViolation(ConstraintViolationException ex, WebRequest request) {
        log.warn("Constraint Violation: {}", ex.getMessage());
        String message = ex.getConstraintViolations().stream()
                .map(cv -> cv.getPropertyPath() + ": " + cv.getMessage())
                .collect(Collectors.joining(", "));

        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                getMessage("error.validation.failed"),
                message,
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    /**
     * Handles cases where the request body is missing or malformed.
     */
    @Override
    protected ResponseEntity<Object> handleHttpMessageNotReadable(
            HttpMessageNotReadableException ex, HttpHeaders headers, HttpStatusCode status, WebRequest request) {
        log.warn("HTTP Message Not Readable: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                getMessage("error.request.malformed"),
                "Request body is missing or malformed.", // Provide a generic message
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    // --- Generic Exception Handler ---

    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ResponseEntity<ErrorResponse> handleAllUncaughtException(Exception ex, WebRequest request) {
        log.error("Unhandled Exception occurred", ex); // Log full stack trace for unexpected errors
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.INTERNAL_SERVER_ERROR.value(),
                getMessage("error.internal.server.title"), // Use title from messages.properties
                getMessage("error.internal.server"), // User-friendly message from messages.properties
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.INTERNAL_SERVER_ERROR);
    }

    // --- Utility Exception Handlers (Optional but good practice) ---

    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ResponseEntity<ErrorResponse> handleIllegalArgumentException(IllegalArgumentException ex, WebRequest request) {
        log.warn("Illegal Argument: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.BAD_REQUEST.value(),
                getMessage("error.illegalArgument"),
                ex.getMessage(), // Often contains useful info
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.BAD_REQUEST);
    }

    @ExceptionHandler(IllegalStateException.class)
    @ResponseStatus(HttpStatus.CONFLICT) // Often indicates a state conflict
    public ResponseEntity<ErrorResponse> handleIllegalStateException(IllegalStateException ex, WebRequest request) {
        log.warn("Illegal State: {}", ex.getMessage());
        ErrorResponse errorResponse = new ErrorResponse(
                HttpStatus.CONFLICT.value(),
                getMessage("error.illegalState"),
                ex.getMessage(), // Often contains useful info
                request.getDescription(false)
        );
        return new ResponseEntity<>(errorResponse, HttpStatus.CONFLICT);
    }
}
