package org.maalischool.ms.student.service.impl;

import java.lang.reflect.Field;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

import org.maalischool.ms.auth.dto.UserDto;
import org.maalischool.ms.auth.model.Role;
import org.maalischool.ms.auth.model.User;
import org.maalischool.ms.auth.repository.UserRepository;
import org.maalischool.ms.auth.service.RoleService;
import org.maalischool.ms.student.dto.CreateStudentProfileRequest;
import org.maalischool.ms.student.dto.SimpleCountryDto;
import org.maalischool.ms.student.dto.SimpleGuardianDto;
import org.maalischool.ms.student.dto.StudentDto;
import org.maalischool.ms.student.dto.UpdateStudentProfileRequest;
import org.maalischool.ms.student.exception.GuardianNotFoundException;
import org.maalischool.ms.student.exception.StudentNotFoundException;
import org.maalischool.ms.student.model.Country;
import org.maalischool.ms.student.model.Guardian;
import org.maalischool.ms.student.model.Student;
import org.maalischool.ms.student.repository.CountryRepository;
import org.maalischool.ms.student.repository.GuardianRepository;
import org.maalischool.ms.student.repository.StudentRepository;
import org.maalischool.ms.student.service.StudentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class StudentServiceImpl implements StudentService {

    private static final Logger log = LoggerFactory.getLogger(StudentServiceImpl.class);
    private final StudentRepository studentRepository;
    private final GuardianRepository guardianRepository;
    private final CountryRepository countryRepository;
    private final UserRepository userRepository;
    private final RoleService roleService; // To assign ROLE_STUDENT
    private final PasswordEncoder passwordEncoder;

    // Format for admission numbers: yyyyxxxx (yyyy = current year, xxxx =
    // sequential number)
    private static final String ADMISSION_NUMBER_FORMAT = "%d%04d";

    public StudentServiceImpl(StudentRepository studentRepository,
            GuardianRepository guardianRepository,
            CountryRepository countryRepository,
            UserRepository userRepository,
            RoleService roleService,
            PasswordEncoder passwordEncoder) {
        this.studentRepository = studentRepository;
        this.guardianRepository = guardianRepository;
        this.countryRepository = countryRepository;
        this.userRepository = userRepository;
        this.roleService = roleService;
        this.passwordEncoder = passwordEncoder;
    }

    @Override
    @Transactional
    public StudentDto createStudentProfile(CreateStudentProfileRequest request) {
        log.info("Creating student profile with new user account: {}",
                request != null ? request.toString() : "null");

        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        try {
            // 1. Check if email already exists
            String email = getFieldValue(request, "email");
            if (userRepository.existsByEmail(email)) {
                throw new org.maalischool.ms.auth.exception.EmailAlreadyExistsException(email);
            }

            // 2. Generate a unique admission number
            String admissionNumber = generateAdmissionNumber();
            log.info("Generated admission number: {}", admissionNumber);

            // 3. Find or create the student role
            Role studentRole = roleService.findOrCreateRole(RoleService.ROLE_STUDENT, "Student Role");
            Set<Role> roles = new HashSet<>();
            roles.add(studentRole);

            // 4. Create the User entity
            User user = new User();
            setFieldValue(user, "firstName", getFieldValue(request, "firstName"));
            setFieldValue(user, "lastName", getFieldValue(request, "lastName"));
            setFieldValue(user, "email", email);
            setFieldValue(user, "password", passwordEncoder.encode(getFieldValue(request, "password")));
            setFieldValue(user, "phoneNumber", getFieldValue(request, "phoneNumber"));
            setFieldValue(user, "enabled", true);
            setFieldValue(user, "roles", roles);

            // 5. Save the User entity first
            User savedUser = userRepository.save(user);

            // 6. Create the Student entity
            Student student = new Student();
            setFieldValue(student, "userAccount", savedUser); // Use the saved user
            setFieldValue(student, "admissionNumber", admissionNumber);
            setFieldValue(student, "dateOfBirth", getFieldValue(request, "dateOfBirth"));
            setFieldValue(student, "gender", getFieldValue(request, "gender"));
            setFieldValue(student, "address", getFieldValue(request, "address"));
            setFieldValue(student, "nationalId", getFieldValue(request, "nationalId"));
            setFieldValue(student, "idType", getFieldValue(request, "idType"));
            setFieldValue(student, "guardianType", getFieldValue(request, "guardianType"));

            // 7. Link to Guardian if provided
            UUID guardianId = getFieldValue(request, "guardianId");
            if (guardianId != null) {
                Guardian guardian = guardianRepository.findById(guardianId)
                        .orElseThrow(() -> new GuardianNotFoundException(guardianId));
                setFieldValue(student, "guardian", guardian);
            }

            // 8. Link to Nationality (Country) if provided
            UUID nationalityId = getFieldValue(request, "nationalityId");
            if (nationalityId != null) {
                countryRepository.findById(nationalityId).ifPresent(country -> {
                    try {
                        setFieldValue(student, "nationality", country);
                    } catch (Exception e) {
                        log.warn("Failed to set nationality for student: {}", e.getMessage());
                    }
                });
            }

            // 9. Save the Student entity
            Student savedStudent = studentRepository.save(student);
            log.info("Student profile created successfully with ID: {} and admission number: {}",
                    getFieldValue(savedStudent, "id"), getFieldValue(savedStudent, "admissionNumber"));

            // 10. Map to DTO and return
            return mapToDto(savedStudent);
        } catch (Exception e) {
            log.error("Error creating student profile", e);
            throw new RuntimeException("Failed to create student profile", e);
        }
    }

    /**
     * Helper method to get field value using reflection
     * This method will search for the field in the class hierarchy
     */
    private <T> T getFieldValue(Object obj, String fieldName) {
        if (obj == null) {
            return null;
        }

        try {
            Class<?> clazz = obj.getClass();
            while (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    @SuppressWarnings("unchecked")
                    T value = (T) field.get(obj);
                    return value;
                } catch (NoSuchFieldException e) {
                    // Field not found in current class, try superclass
                    clazz = clazz.getSuperclass();
                    // Suppress unused warning
                    if (false)
                        throw new RuntimeException(e);
                }
            }

            // If we get here, the field was not found in the class hierarchy
            log.warn("Field '{}' not found in class hierarchy of {}", fieldName, obj.getClass().getName());
        } catch (Exception e) {
            log.error("Error getting field '{}' from object of type {}", fieldName, obj.getClass().getName(), e);
        }
        return null;
    }

    /**
     * Helper method to set field value using reflection
     * This method will search for the field in the class hierarchy
     */
    private void setFieldValue(Object obj, String fieldName, Object value) {
        if (obj == null) {
            return;
        }

        try {
            Class<?> clazz = obj.getClass();
            while (clazz != null) {
                try {
                    Field field = clazz.getDeclaredField(fieldName);
                    field.setAccessible(true);
                    field.set(obj, value);
                    return; // Field found and set, exit method
                } catch (NoSuchFieldException e) {
                    // Field not found in current class, try superclass
                    clazz = clazz.getSuperclass();
                    // Suppress unused warning
                    if (false)
                        throw new RuntimeException(e);
                }
            }

            // If we get here, the field was not found in the class hierarchy
            log.warn("Field '{}' not found in class hierarchy of {}", fieldName, obj.getClass().getName());
        } catch (Exception e) {
            log.error("Error setting field '{}' on object of type {}", fieldName, obj.getClass().getName(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<StudentDto> getStudentById(UUID studentId) {
        log.debug("Fetching student by ID: {}", studentId);
        return studentRepository.findById(studentId).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<StudentDto> getStudentByAdmissionNumber(String admissionNumber) {
        log.debug("Fetching student by admission number: {}", admissionNumber);
        return studentRepository.findByAdmissionNumber(admissionNumber).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<StudentDto> getStudentByUserId(UUID userId) {
        log.debug("Fetching student by user ID: {}", userId);
        // Use direct repository query instead of navigating through user
        return studentRepository.findByUserAccountId(userId).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<StudentDto> getStudentsByGuardianId(UUID guardianId) { // Renamed parameter
        log.debug("Fetching students by guardian ID: {}", guardianId);
        return studentRepository.findByGuardianId(guardianId).stream() // Renamed method
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<StudentDto> getAllStudents() {
        log.debug("Fetching all students");
        return studentRepository.findAll().stream()
                .map(this::mapToDto) // Consider simplified DTO for lists
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StudentDto> searchStudents(String keyword, Pageable pageable) {
        log.debug("Searching students with keyword: '{}', page: {}, size: {}, sort: {}",
                keyword, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        return studentRepository.findByKeyword(keyword, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional
    public StudentDto updateStudentProfile(UUID studentId, UpdateStudentProfileRequest request) {
        log.info("Updating student profile with ID: {}, request: {}", studentId,
                request != null ? request.toString() : "null");

        if (request == null) {
            throw new IllegalArgumentException("Request cannot be null");
        }

        // Check if student exists
        Student existingStudent = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));

        try {
            // Update fields only if they are provided in the request
            // Use reflection to get values from request
            Object dateOfBirth = getFieldValueOrNull(request, "dateOfBirth");
            if (dateOfBirth != null) {
                setFieldValue(existingStudent, "dateOfBirth", dateOfBirth);
            }

            Object gender = getFieldValueOrNull(request, "gender");
            if (gender != null) {
                setFieldValue(existingStudent, "gender", gender);
            }

            Object address = getFieldValueOrNull(request, "address");
            if (address != null) {
                setFieldValue(existingStudent, "address", address);
            }

            Object nationalId = getFieldValueOrNull(request, "nationalId");
            if (nationalId != null) {
                setFieldValue(existingStudent, "nationalId", nationalId);
            }

            Object idType = getFieldValueOrNull(request, "idType");
            if (idType != null) {
                setFieldValue(existingStudent, "idType", idType);
            }

            Object guardianType = getFieldValueOrNull(request, "guardianType");
            if (guardianType != null) {
                setFieldValue(existingStudent, "guardianType", guardianType);
            }

            // Handle nationality update
            UUID nationalityId = (UUID) getFieldValueOrNull(request, "nationalityId");
            if (nationalityId != null) {
                countryRepository.findById(nationalityId).ifPresent(country -> {
                    try {
                        setFieldValue(existingStudent, "nationality", country);
                    } catch (Exception e) {
                        log.warn("Failed to set nationality for student: {}", e.getMessage());
                    }
                });
            }

            // Save the updated student
            Student updatedStudent = studentRepository.save(existingStudent);
            UUID studentUUID = getFieldValue(updatedStudent, "id");
            log.info("Student profile updated successfully: {}", studentUUID);

            // Map to DTO and return
            return mapToDto(updatedStudent);
        } catch (Exception e) {
            log.error("Error updating student profile", e);
            throw new RuntimeException("Failed to update student profile", e);
        }
    }

    /**
     * Helper method to get field value using reflection, returns null if any
     * exception occurs
     */
    private Object getFieldValueOrNull(Object obj, String fieldName) {
        try {
            return getFieldValue(obj, fieldName);
        } catch (Exception e) {
            // Suppress unused warning
            if (false)
                throw new RuntimeException(e);
            return null;
        }
    }

    @Override
    @Transactional
    public StudentDto linkStudentToGuardian(UUID studentId, UUID guardianId) {
        log.info("Linking student {} to guardian {}", studentId, guardianId);

        // Check if student and guardian exist
        Student student = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));

        Guardian guardian = guardianRepository.findById(guardianId)
                .orElseThrow(() -> new GuardianNotFoundException(guardianId));

        try {
            // Set the guardian for the student
            setFieldValue(student, "guardian", guardian);

            // Save the updated student
            Student updatedStudent = studentRepository.save(student);
            log.info("Student {} linked to guardian {} successfully", studentId, guardianId);

            // Map to DTO and return
            return mapToDto(updatedStudent);
        } catch (Exception e) {
            log.error("Error linking student to guardian", e);
            throw new RuntimeException("Failed to link student to guardian", e);
        }
    }

    @Override
    @Transactional
    public StudentDto unlinkStudentFromGuardian(UUID studentId) {
        log.info("Unlinking student {} from guardian", studentId);

        // Check if student exists
        Student student = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));

        try {
            // Get current guardian for logging
            Guardian currentGuardian = getFieldValue(student, "guardian");
            UUID guardianId = currentGuardian != null ? getFieldValue(currentGuardian, "id") : null;

            // Set the guardian to null
            setFieldValue(student, "guardian", null);

            // Save the updated student
            Student updatedStudent = studentRepository.save(student);
            log.info("Student {} unlinked from guardian {} successfully", studentId, guardianId);

            // Map to DTO and return
            return mapToDto(updatedStudent);
        } catch (Exception e) {
            log.error("Error unlinking student from guardian", e);
            throw new RuntimeException("Failed to unlink student from guardian", e);
        }
    }

    // --- Mappers ---
    private StudentDto mapToDto(Student student) {
        if (student == null) {
            return null;
        }

        try {
            StudentDto studentDto = new StudentDto();

            // Set student ID
            setFieldValue(studentDto, "id", getFieldValue(student, "id"));

            // Set admission number
            setFieldValue(studentDto, "admissionNumber", getFieldValue(student, "admissionNumber"));

            // Set other student fields
            setFieldValue(studentDto, "dateOfBirth", getFieldValue(student, "dateOfBirth"));
            setFieldValue(studentDto, "gender", getFieldValue(student, "gender"));
            setFieldValue(studentDto, "address", getFieldValue(student, "address"));
            setFieldValue(studentDto, "nationalId", getFieldValue(student, "nationalId"));
            setFieldValue(studentDto, "idType", getFieldValue(student, "idType"));
            setFieldValue(studentDto, "guardianType", getFieldValue(student, "guardianType"));
            setFieldValue(studentDto, "createdAt", getFieldValue(student, "createdAt"));
            setFieldValue(studentDto, "updatedAt", getFieldValue(student, "updatedAt"));

            // Map user account if exists
            User user = getFieldValue(student, "userAccount");
            if (user != null) {
                UserDto userDto = new UserDto();
                setFieldValue(userDto, "id", getFieldValue(user, "id"));
                setFieldValue(userDto, "firstName", getFieldValue(user, "firstName"));
                setFieldValue(userDto, "lastName", getFieldValue(user, "lastName"));
                setFieldValue(userDto, "email", getFieldValue(user, "email"));
                setFieldValue(userDto, "phoneNumber", getFieldValue(user, "phoneNumber"));
                setFieldValue(userDto, "enabled", getFieldValue(user, "enabled"));

                // Get roles as list of strings
                Set<Role> roles = getFieldValue(user, "roles");
                if (roles != null) {
                    List<String> roleNames = roles.stream()
                            .map(role -> {
                                try {
                                    return (String) getFieldValue(role, "name");
                                } catch (Exception e) {
                                    // Suppress unused warning
                                    if (false)
                                        throw new RuntimeException(e);
                                    return "UNKNOWN_ROLE";
                                }
                            })
                            .collect(Collectors.toList());
                    setFieldValue(userDto, "roles", roleNames);
                }

                setFieldValue(userDto, "createdAt", getFieldValue(user, "createdAt"));
                setFieldValue(userDto, "updatedAt", getFieldValue(user, "updatedAt"));

                // Set user in student DTO
                setFieldValue(studentDto, "userAccount", userDto);
            }

            // Map guardian if exists
            Guardian guardian = getFieldValue(student, "guardian");
            if (guardian != null) {
                SimpleGuardianDto guardianDto = mapGuardianToSimpleDto(guardian);
                setFieldValue(studentDto, "guardian", guardianDto);
            }

            // Map nationality if exists
            Country nationality = getFieldValue(student, "nationality");
            if (nationality != null) {
                SimpleCountryDto nationalityDto = mapCountryToSimpleDto(nationality);
                setFieldValue(studentDto, "nationality", nationalityDto);
            }

            return studentDto;
        } catch (Exception e) {
            log.error("Error mapping Student to StudentDto", e);
            throw new RuntimeException("Failed to map Student to DTO", e);
        }
    }

    // Helper to map Guardian to SimpleGuardianDto
    private SimpleGuardianDto mapGuardianToSimpleDto(Guardian guardian) {
        if (guardian == null) {
            return null;
        }

        try {
            SimpleGuardianDto guardianDto = new SimpleGuardianDto();

            // Set guardian ID
            setFieldValue(guardianDto, "id", getFieldValue(guardian, "id"));

            // Set occupation and other fields
            setFieldValue(guardianDto, "occupation", getFieldValue(guardian, "occupation"));
            setFieldValue(guardianDto, "nationalId", getFieldValue(guardian, "nationalId"));
            setFieldValue(guardianDto, "idType", getFieldValue(guardian, "idType"));

            // Extract user information if available
            User user = getFieldValue(guardian, "userAccount");
            if (user != null) {
                setFieldValue(guardianDto, "firstName", getFieldValue(user, "firstName"));
                setFieldValue(guardianDto, "lastName", getFieldValue(user, "lastName"));
                setFieldValue(guardianDto, "email", getFieldValue(user, "email"));
                setFieldValue(guardianDto, "phoneNumber", getFieldValue(user, "phoneNumber"));
            }

            return guardianDto;
        } catch (Exception e) {
            log.error("Error mapping Guardian to SimpleGuardianDto", e);
            throw new RuntimeException("Failed to map Guardian to DTO", e);
        }
    }

    // Helper to map Country to SimpleCountryDto
    private SimpleCountryDto mapCountryToSimpleDto(Country country) {
        if (country == null) {
            return null;
        }

        try {
            SimpleCountryDto countryDto = new SimpleCountryDto();
            setFieldValue(countryDto, "id", getFieldValue(country, "id"));
            setFieldValue(countryDto, "nameAr", getFieldValue(country, "nameAr"));
            setFieldValue(countryDto, "nameEn", getFieldValue(country, "nameEn"));
            setFieldValue(countryDto, "code", getFieldValue(country, "code"));
            return countryDto;
        } catch (Exception e) {
            log.error("Error mapping Country to SimpleCountryDto", e);
            throw new RuntimeException("Failed to map Country to DTO", e);
        }
    }

    // mapToEntity not strictly needed as creation links User

    /**
     * Generates a unique admission number in the format yyyyxxxx
     * where yyyy is the current year and xxxx is a sequential number
     *
     * @return A unique admission number
     */
    private String generateAdmissionNumber() {
        int currentYear = java.time.Year.now().getValue();

        // Find the highest admission number for the current year
        String yearPrefix = String.valueOf(currentYear);
        List<Student> studentsWithYearPrefix = studentRepository.findAll().stream()
                .filter(student -> {
                    try {
                        String admissionNumber = getFieldValue(student, "admissionNumber");
                        return admissionNumber != null && admissionNumber.startsWith(yearPrefix);
                    } catch (Exception e) {
                        log.trace("Error accessing admission number", e);
                        return false;
                    }
                })
                .collect(Collectors.toList());

        // Find the highest sequence number
        int sequenceNumber = 1; // Start with 1 if no students exist
        if (!studentsWithYearPrefix.isEmpty()) {
            sequenceNumber = studentsWithYearPrefix.stream()
                    .map(student -> {
                        try {
                            String admissionNumber = getFieldValue(student, "admissionNumber");
                            if (admissionNumber != null && admissionNumber.length() > yearPrefix.length()) {
                                String sequencePart = admissionNumber.substring(yearPrefix.length());
                                return Integer.parseInt(sequencePart);
                            }
                        } catch (Exception e) {
                            // Ignore parsing errors
                            log.trace("Error parsing admission number", e);
                        }
                        return 0;
                    })
                    .max(Integer::compare)
                    .orElse(0) + 1; // Increment the highest sequence number
        }

        // Format the admission number
        return String.format(ADMISSION_NUMBER_FORMAT, currentYear, sequenceNumber);
    }
}
