package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

// Allow partial updates
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateRoleRequest {

    @Size(min = 3, max = 50, message = "{validation.roleName.size}")
    private String name; // Optional

    @Size(max = 255, message = "Description cannot exceed 255 characters")
    private String description; // Optional

    // Optional: List of permission names to SET for the role (replaces existing)
    // Add @NotEmpty if required when present? Depends on desired update logic.
    private List<@NotBlank(message = "{validation.permissionName.notBlank}") String> permissionNames;
}
