package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.*;
import org.maalischool.ms.accounting.exception.DiscountNotFoundException; // Keep this one
import org.maalischool.ms.accounting.exception.FeeNotFoundException;
import org.maalischool.ms.accounting.exception.StudentFeeNotFoundException;
// Removed duplicate import
import org.maalischool.ms.accounting.model.*;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.maalischool.ms.accounting.repository.DiscountRepository;
import org.maalischool.ms.accounting.repository.FeeRepository;
import org.maalischool.ms.accounting.repository.StudentFeeDiscountRepository;
import org.maalischool.ms.accounting.repository.StudentFeeRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.FeeService;
import org.maalischool.ms.accounting.service.StudentFeeService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.maalischool.ms.student.exception.StudentNotFoundException; // Assuming this exists
import org.maalischool.ms.student.model.Student; // Correct model
import org.maalischool.ms.student.repository.StudentRepository; // Correct repository
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.maalischool.ms.student.dto.SimpleStudentDto;
import org.maalischool.ms.schoolmanagement.dto.SimpleAcademicYearDto;
import org.maalischool.ms.schoolmanagement.model.AcademicYear;

import java.math.BigDecimal;
import java.time.LocalDate; // Add missing import
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class StudentFeeServiceImpl implements StudentFeeService {

    private static final Logger log = LoggerFactory.getLogger(StudentFeeServiceImpl.class);

    private final StudentFeeRepository studentFeeRepository;
    private final FeeRepository feeRepository;
    private final StudentRepository studentRepository; // Correct repository
    private final DiscountRepository discountRepository;
    private final StudentFeeDiscountRepository studentFeeDiscountRepository;
    private final FeeService feeService; // To find applicable fees
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public StudentFeeDto assignFeeToStudent(AssignFeeRequest request) {
        log.info("Assigning Fee ID {} to Student ID {}", request.getFeeId(), request.getStudentId());

        Student student = studentRepository.findById(request.getStudentId())
                .orElseThrow(() -> new StudentNotFoundException(request.getStudentId()));
        Fee fee = feeRepository.findById(request.getFeeId())
                .orElseThrow(() -> new FeeNotFoundException(request.getFeeId()));

        // Check if this exact fee is already assigned to this student for the same
        // academic year using the correct repository method
        boolean alreadyAssigned = studentFeeRepository.existsByStudentIdAndFee_IdAndFee_AcademicYear_Id(
                request.getStudentId(), request.getFeeId(), fee.getAcademicYear().getId());
        if (alreadyAssigned) {
            log.warn("Fee ID {} is already assigned to Student ID {} for academic year {}",
                    request.getFeeId(), request.getStudentId(), fee.getAcademicYear());
            // Depending on requirements, either throw an exception or return the existing
            // assignment
            // Assuming Student has a userAccount with firstName - adjust if needed
            String studentName = student.getUserAccount() != null ? student.getUserAccount().getFirstName()
                    : "ID: " + student.getId();
            throw new IllegalArgumentException(
                    String.format("Fee '%s' is already assigned to student '%s' for year '%s'.",
                            fee.getNameEn(), studentName, fee.getAcademicYear())); // Use nameEn
            // Or find and return existing:
            // return studentFeeRepository.findByStudentIdAndFeeIdAndAcademicYear(...)
            // .map(this::mapToDto)
            // .orElseThrow(() -> new IllegalStateException("Assignment exists but could not
            // be retrieved"));
        }

        StudentFee studentFee = StudentFee.builder()
                .student(student)
                .fee(fee)
                .originalAmount(fee.getAmount())
                .amountPaid(BigDecimal.ZERO)
                // amountDue and status will be calculated by @PrePersist/@PreUpdate
                .notesEn(request.getNotesEn())
                .notesAr(request.getNotesAr())
                .build();

        // Handle initial discounts if provided in the request
        Set<StudentFeeDiscount> appliedDiscounts = processDiscounts(
                request.getDiscountId() != null ? List.of(request.getDiscountId()) : List.of(),
                studentFee);
        studentFee.setAppliedDiscounts(appliedDiscounts);

        StudentFee savedStudentFee = studentFeeRepository.save(studentFee);
        log.info("Successfully assigned Fee ID {} to Student ID {}. StudentFee ID: {}",
                savedStudentFee.getFee().getId(), student, savedStudentFee.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.ASSIGN_FEE,
                AccountingConstants.ENTITY_STUDENT_FEE,
                savedStudentFee.getId(),
                String.format("Assigned Fee '%s' (%s) to Student ID %s. Amount: %s",
                        fee.getNameEn(), fee.getAcademicYear(), student.getId(), fee.getAmount()) // Use nameEn,
                                                                                                  // student.getId()
        );

        return mapToDto(savedStudentFee);
    }

    @Override
    @Transactional
    public List<StudentFeeDto> assignApplicableFeesToStudent(UUID studentId, UUID academicYearId) {
        log.info("Assigning applicable fees for Academic Year ID {} to Student ID {}", academicYearId, studentId);

        Student student = studentRepository.findById(studentId)
                .orElseThrow(() -> new StudentNotFoundException(studentId));

        // Fetch student context (assuming these fields exist on Student or related
        // entities)
        // TODO: These fields (currentGradeId, currentStageId, currentBranchId) are NOT
        // in the provided Student model.
        // TODO: Add these fields to Student model or adjust this logic based on actual
        // enrollment structure.
        UUID gradeId = null; // student.getCurrentGradeId(); // Placeholder - Needs implementation
        UUID stageId = null; // student.getCurrentStageId(); // Placeholder - Needs implementation
        UUID branchId = null; // student.getCurrentBranchId(); // Placeholder - Needs implementation

        if (gradeId == null || stageId == null || branchId == null) {
            log.error(
                    "Cannot assign applicable fees. Student {} context (grade, stage, branch) is incomplete or not implemented.",
                    studentId);
            throw new IllegalStateException(
                    "Student context (grade, stage, branch) is required and needs to be implemented on the Student model or fetched differently.");
        }

        // 1. Find all potentially applicable fees using FeeService
        List<FeeDto> applicableFees = feeService.findApplicableFeesForStudentContext(academicYearId, gradeId, stageId,
                branchId);

        if (CollectionUtils.isEmpty(applicableFees)) {
            log.info("No applicable fees found for Student ID {} in Year ID {}", studentId, academicYearId);
            return List.of();
        }

        // 2. Find fees already assigned to this student for this academic year
        Set<UUID> assignedFeeIds = studentFeeRepository.findFeeIdsByStudentIdAndAcademicYear(studentId, academicYearId);
        log.debug("Student {} already has {} fees assigned for year ID {}", studentId, assignedFeeIds.size(),
                academicYearId);

        // 3. Filter out already assigned fees and create new assignments
        List<StudentFee> newAssignments = new ArrayList<>();
        for (FeeDto feeDto : applicableFees) {
            if (!assignedFeeIds.contains(feeDto.getId())) {
                Fee fee = feeRepository.findById(feeDto.getId()).orElse(null); // Fetch the entity
                if (fee == null) {
                    log.error(
                            "Fee with ID {} found by applicable search but not found in repository. Skipping assignment.",
                            feeDto.getId());
                    continue; // Should not happen ideally
                }
                StudentFee studentFee = StudentFee.builder()
                        .student(student)
                        .fee(fee)
                        // .academicYear(academicYear)
                        .originalAmount(fee.getAmount())
                        .amountPaid(BigDecimal.ZERO)
                        // status and amountDue calculated by @PrePersist
                        .build();
                newAssignments.add(studentFee);
                log.debug("Prepared new assignment for Fee ID {} to Student ID {}", fee.getId(), studentId);
            } else {
                log.debug("Skipping already assigned Fee ID {} for Student ID {}", feeDto.getId(), studentId);
            }
        }

        if (newAssignments.isEmpty()) {
            log.info("No *new* applicable fees to assign for Student ID {} in Year ID {}", studentId, academicYearId);
            return List.of();
        }

        // 4. Save new assignments
        List<StudentFee> savedAssignments = studentFeeRepository.saveAll(newAssignments);
        log.info("Successfully assigned {} new fees to Student ID {} for Academic Year ID {}", savedAssignments.size(),
                studentId, academicYearId);

        // 5. Audit log (consider logging each assignment or a summary)
        savedAssignments.forEach(sf -> auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.ASSIGN_FEE, // Or a specific BULK_ASSIGN type
                AccountingConstants.ENTITY_STUDENT_FEE,
                sf.getId(),
                String.format("Auto-assigned Fee '%s' (%s) to Student ID %s. Amount: %s",
                        sf.getFee().getNameEn(), sf.getFee().getAcademicYear(), studentId, sf.getOriginalAmount()) // Use
                                                                                                                   // nameEn
        ));

        return savedAssignments.stream().map(this::mapToDto).collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public StudentFeeDto getStudentFeeById(UUID studentFeeId) {
        log.debug("Fetching StudentFee by ID: {}", studentFeeId);
        StudentFee studentFee = findStudentFeeOrThrow(studentFeeId);
        return mapToDto(studentFee);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StudentFeeDto> getFeesForStudent(UUID studentId, UUID academicYearId, Pageable pageable) {
        log.debug("Fetching fees for Student ID {} and Academic Year ID {}", studentId, academicYearId);
        // Optional: Check if student exists
        if (!studentRepository.existsById(studentId)) {
            throw new StudentNotFoundException(studentId);
        }
        return studentFeeRepository
                .findByStudentIdAndAcademicYearOrderByFeeDueDateAsc(studentId, academicYearId, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StudentFeeDto> getStudentFeesByStatus(UUID academicYearId, FeeStatus status, Pageable pageable) {
        log.debug("Fetching StudentFees for Academic Year ID {} with Status {}", academicYearId, status);
        return studentFeeRepository.findByAcademicYearAndStatusOrderByFeeDueDateAsc(academicYearId, status, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<StudentFeeDto> getStudentFeesByFeeId(UUID feeId, Pageable pageable) {
        log.debug("Fetching StudentFees for Fee ID {}", feeId);
        // Optional: Check if fee exists
        if (!feeRepository.existsById(feeId)) {
            throw new FeeNotFoundException(feeId);
        }
        return studentFeeRepository.findByFeeIdOrderByStudentIdAsc(feeId, pageable) // Order might need adjustment
                .map(this::mapToDto);
    }

    @Override
    @Transactional
    public StudentFeeDto updateStudentFee(UUID studentFeeId, UpdateStudentFeeRequest request) {
        log.info("Updating StudentFee with ID: {}", studentFeeId);
        StudentFee studentFee = findStudentFeeOrThrow(studentFeeId);

        // Prevent updates if fully paid? Or allow notes/discount changes? Business rule
        // needed.
        if (studentFee.getStatus() == FeeStatus.PAID) {
            log.warn("Attempting to update an already PAID StudentFee (ID: {}). Allowing notes/discount update.",
                    studentFeeId);
            // Consider throwing exception if no updates should be allowed after payment:
            // throw new IllegalStateException("Cannot update a fully paid student fee.");
        }

        String oldDetails = String.format("Notes: %s, Discounts: %s",
                studentFee.getNotesEn(), getDiscountCodes(studentFee.getAppliedDiscounts())); // Use notesEn for logging

        studentFee.setNotesEn(request.getNotesEn()); // Set English notes
        studentFee.setNotesAr(request.getNotesAr()); // Set Arabic notes

        // --- Discount Handling ---
        // 1. Remove existing discounts managed by this relationship
        studentFeeDiscountRepository.deleteAll(studentFee.getAppliedDiscounts()); // Clear existing links
        studentFee.getAppliedDiscounts().clear(); // Clear the collection in the entity

        // 2. Process and add new discounts
        // Use getDiscountIds() which returns List<UUID>
        Set<StudentFeeDiscount> newAppliedDiscounts = processDiscounts(request.getDiscountIds(), studentFee); // Use
                                                                                                              // getDiscountIds()
        studentFee.setAppliedDiscounts(newAppliedDiscounts);
        // Note: The StudentFee entity's @PreUpdate method should recalculate amountDue

        StudentFee updatedStudentFee = studentFeeRepository.save(studentFee); // Save triggers @PreUpdate
        log.info("Successfully updated StudentFee with ID: {}", updatedStudentFee.getId());

        String newDetails = String.format("NotesEn: %s, Discounts: %s", // Use notesEn for logging
                updatedStudentFee.getNotesEn(), getDiscountCodes(updatedStudentFee.getAppliedDiscounts()));

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_STUDENT_FEE,
                updatedStudentFee.getId(),
                "Updated StudentFee Assignment. Old: [" + oldDetails + "], New: [" + newDetails + "]");

        return mapToDto(updatedStudentFee);
    }

    @Override
    @Transactional
    public void deleteStudentFeeAssignment(UUID studentFeeId) {
        log.warn("Attempting to delete StudentFee assignment with ID: {}", studentFeeId);
        StudentFee studentFee = findStudentFeeOrThrow(studentFeeId);

        // Critical Check: Prevent deletion if any payments have been made.
        if (studentFee.getAmountPaid().compareTo(BigDecimal.ZERO) > 0) {
            log.error("Cannot delete StudentFee assignment {} because payments have been made (Amount Paid: {}).",
                    studentFeeId, studentFee.getAmountPaid());
            throw new IllegalStateException(
                    "Cannot delete a student fee assignment that has payments recorded against it. Consider waiving the remaining amount instead.");
        }

        // Also delete associated StudentFeeDiscount entries
        studentFeeDiscountRepository.deleteAll(studentFee.getAppliedDiscounts());

        studentFeeRepository.delete(studentFee);
        log.warn("Successfully deleted StudentFee assignment with ID: {}", studentFeeId);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE, // Or a more specific UNASSIGN_FEE type
                AccountingConstants.ENTITY_STUDENT_FEE,
                studentFeeId,
                String.format("Deleted Fee Assignment: Fee '%s' (%s) for Student ID %s",
                        studentFee.getFee().getNameEn(), studentFee.getFee().getAcademicYear(),
                        studentFee.getStudent().getId()) // Use nameEn, Get student ID from associated Student entity
        );
    }

    @Override
    @Transactional(readOnly = true)
    // Use simple names matching the updated interface
    public Page<StudentFeeDto> findStudentFees(
            UUID studentId,
            UUID feeId,
            FeeStatus status,
            LocalDate dueDateStart,
            LocalDate dueDateEnd,
            Pageable pageable) {
        log.debug("Finding student fees with filters - Student: {}, Fee: {}, Status: {}, DueStart: {}, DueEnd: {}",
                studentId, feeId, status, dueDateStart, dueDateEnd);

        // TODO: Implement the actual filtering logic using JpaSpecificationExecutor or
        // Querydsl
        // Example using findAll (replace with specific query):
        // Specification<StudentFee> spec = Specification.where(null);
        // if (studentId != null) spec = spec.and((root, query, cb) ->
        // cb.equal(root.get("studentId"), studentId));
        // if (feeId != null) spec = spec.and((root, query, cb) ->
        // cb.equal(root.get("fee").get("id"), feeId));
        // if (status != null) spec = spec.and((root, query, cb) ->
        // cb.equal(root.get("status"), status));
        // if (dueDateStart != null) spec = spec.and((root, query, cb) ->
        // cb.greaterThanOrEqualTo(root.get("fee").get("dueDate"), dueDateStart));
        // if (dueDateEnd != null) spec = spec.and((root, query, cb) ->
        // cb.lessThanOrEqualTo(root.get("fee").get("dueDate"), dueDateEnd));
        // return studentFeeRepository.findAll(spec, pageable).map(this::mapToDto);

        log.warn("findStudentFees filtering logic is not fully implemented. Returning all student fees for now.");
        // Placeholder implementation:
        return studentFeeRepository.findAll(pageable).map(this::mapToDto);
    }

    // --- Helper Methods ---

    private StudentFee findStudentFeeOrThrow(UUID id) {
        return studentFeeRepository.findById(id)
                .orElseThrow(() -> new StudentFeeNotFoundException(id));
    }

    /**
     * Processes a list of discount IDs, validates them, and creates
     * StudentFeeDiscount objects.
     *
     * @param discountIds The list of discount UUIDs to apply.
     * @param studentFee  The StudentFee entity to associate with.
     * @return A set of validated and created StudentFeeDiscount objects.
     */
    private Set<StudentFeeDiscount> processDiscounts(List<UUID> discountIds, StudentFee studentFee) {
        Set<StudentFeeDiscount> appliedDiscounts = new HashSet<>();
        if (!CollectionUtils.isEmpty(discountIds)) {
            for (UUID discountId : discountIds) {
                Discount discount = discountRepository.findById(discountId)
                        .orElseThrow(() -> new DiscountNotFoundException(discountId));
                // TODO: Add validation logic here if needed (e.g., is discount active?
                // applicable?)
                StudentFeeDiscount studentFeeDiscount = StudentFeeDiscount.builder()
                        .studentFee(studentFee)
                        .discount(discount)
                        .build();
                appliedDiscounts.add(studentFeeDiscount);
            }
        }
        return appliedDiscounts;
    }

    /**
     * Helper to get a comma-separated string of discount codes for logging.
     */
    private String getDiscountCodes(Set<StudentFeeDiscount> discounts) {
        if (CollectionUtils.isEmpty(discounts)) {
            return "None";
        }
        return discounts.stream()
                .map(sfd -> sfd.getDiscount().getCode())
                .collect(Collectors.joining(", "));
    }

    private SimpleStudentDto mapStudentToSimpleDto(Student student) {
        if (student == null)
            return null;

        String firstName = "Unknown";
        String lastName = "Student";
        if (student.getUserAccount() != null) {
            firstName = student.getUserAccount().getFirstName();
            lastName = student.getUserAccount().getLastName();
        }

        return SimpleStudentDto.builder()
                .id(student.getId())
                .firstName(firstName) // Set firstName from UserAccount
                .lastName(lastName) // Set lastName from UserAccount
                .admissionNumber(student.getAdmissionNumber()) // Map other fields from SimpleStudentDto
                .dateOfBirth(student.getDateOfBirth())
                .gender(student.getGender())
                .build();
    }

    private StudentFeeDto mapToDto(StudentFee studentFee) {
        if (studentFee == null)
            return null;

        List<SimpleDiscountDto> discountDtos = studentFee.getAppliedDiscounts().stream()
                .map(sfd -> mapDiscountToSimpleDto(sfd.getDiscount()))
                .collect(Collectors.toList());

        return StudentFeeDto.builder()
                .id(studentFee.getId())
                .student(mapStudentToSimpleDto(studentFee.getStudent())) // Use the corrected helper method
                .fee(mapFeeToSimpleDto(studentFee.getFee()))
                // .academicYear(studentFee.getAcademicYear())
                .amount(studentFee.getOriginalAmount())
                .discountAmount(studentFee.getDiscountAmount()) // Calculated by entity
                .amountDue(studentFee.getAmountDue()) // Calculated by entity
                .amountPaid(studentFee.getAmountPaid())
                .status(studentFee.getStatus()) // Calculated by entity
                .dueDate(studentFee.getFee().getDueDate()) // Get from associated Fee
                .notesEn(studentFee.getNotesEn()) // Map English notes
                .notesAr(studentFee.getNotesAr()) // Map Arabic notes
                .appliedDiscounts(discountDtos)
                .createdDate(studentFee.getCreatedDate())
                .lastModifiedDate(studentFee.getLastModifiedDate())
                .build();
    }

    private SimpleFeeDto mapFeeToSimpleDto(Fee fee) {
        if (fee == null)
            return null;
        return SimpleFeeDto.builder()
                .id(fee.getId())
                .nameEn(fee.getNameEn()) // Use English name
                .nameAr(fee.getNameAr()) // Use Arabic name
                .amount(fee.getAmount())
                .academicYear(mapAcademicYearToSimpleDto(fee.getAcademicYear())) // Add academic year
                .dueDate(fee.getDueDate())
                .build();
    }

    private SimpleDiscountDto mapDiscountToSimpleDto(Discount discount) {
        if (discount == null)
            return null;
        return SimpleDiscountDto.builder()
                .id(discount.getId())
                .code(discount.getCode())
                .type(discount.getType())
                .value(discount.getFixedAmount())
                .build();
    }

    private SimpleAcademicYearDto mapAcademicYearToSimpleDto(AcademicYear academicYear) {
        if (academicYear == null)
            return null;
        return SimpleAcademicYearDto.builder()
                .id(academicYear.getId())
                .name(academicYear.getName())
                .startDate(academicYear.getStartDate())
                .endDate(academicYear.getEndDate())
                .active(academicYear.isActive())
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("00000000-0000-0000-0000-000000000000");
    }
}
