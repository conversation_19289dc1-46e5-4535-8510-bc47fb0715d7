package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * DTO representing the details of a payment applied to a specific student fee
 * within a CreateReceiptRequest.
 */
@Data
@Builder
public class CreatePaymentDetailRequest {

    @NotNull(message = "Student Fee ID cannot be null for payment detail")
    private UUID studentFeeId;

    @NotNull(message = "Allocated amount cannot be null for payment detail")
    @DecimalMin(value = "0.0", inclusive = false, message = "Allocated amount must be positive")
    private BigDecimal allocatedAmount;

    // Add any other relevant fields if needed per payment line, e.g., notes specific to this payment line
    // private String lineNotes;
}
