package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class CreateFeeRequest {
    @NotBlank(message = "English fee name cannot be blank")
    @Size(max = 255, message = "English fee name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic fee name cannot be blank")
    @Size(max = 255, message = "Arabic fee name must be less than 255 characters")
    private String nameAr;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    @NotNull(message = "Amount cannot be null")
    @DecimalMin(value = "0.0", message = "Amount must be non-negative")
    private BigDecimal amount;

    @NotNull(message = "Academic year ID cannot be null")
    private UUID academicYearId;

    @NotNull(message = "Due date cannot be null")
    private LocalDate dueDate;

    @NotNull(message = "Fee category ID cannot be null")
    private UUID feeCategoryId;

    // Optional applicability constraints
    private UUID applicableGradeId;
    private UUID applicableStageId;
    private UUID applicableBranchId;
}
