package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateFeeRequest; // Ensure this import exists
import org.maalischool.ms.accounting.dto.FeeDto;
import org.maalischool.ms.accounting.dto.UpdateFeeRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

public interface FeeService {

    FeeDto createFee(CreateFeeRequest request);

    FeeDto getFeeById(UUID id);

    Page<FeeDto> getAllFees(Pageable pageable);

    List<FeeDto> getActiveFeesByAcademicYear(UUID academicYearId);

    List<FeeDto> getFeesByCategory(UUID categoryId);

    /**
     * Finds fees applicable to a student based on their academic year, grade,
     * stage, and branch.
     * Considers fees applicable to specific levels and those applicable universally
     * (null applicability).
     *
     * @param academicYearId The academic year ID.
     * @param gradeId        The student's grade ID.
     * @param stageId        The student's educational stage ID.
     * @param branchId       The student's branch ID.
     * @return List of applicable FeeDto.
     */
    List<FeeDto> findApplicableFeesForStudentContext(UUID academicYearId, UUID gradeId, UUID stageId, UUID branchId);

    FeeDto updateFee(UUID id, UpdateFeeRequest request);

    void deleteFee(UUID id); // Consider implications if assigned to students

    FeeDto activateFee(UUID id);

    FeeDto deactivateFee(UUID id);
}
