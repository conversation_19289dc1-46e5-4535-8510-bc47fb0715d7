package org.maalischool.ms.auth.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.CreateRoleRequest;
import org.maalischool.ms.auth.dto.PermissionDto;
import org.maalischool.ms.auth.dto.RoleDto;
import org.maalischool.ms.auth.dto.UpdateRoleRequest;
import org.maalischool.ms.auth.exception.PermissionNotFoundException;
import org.maalischool.ms.auth.exception.RoleNotFoundException;
import org.maalischool.ms.auth.model.Permission;
import org.maalischool.ms.auth.model.Role;
import org.maalischool.ms.auth.repository.PermissionRepository;
import org.maalischool.ms.auth.repository.RoleRepository;
import org.maalischool.ms.auth.service.RoleService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;


import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class RoleServiceImpl implements RoleService {

    private static final Logger log = LoggerFactory.getLogger(RoleServiceImpl.class);
    private final RoleRepository roleRepository;
    private final PermissionRepository permissionRepository; // Inject PermissionRepository

    @Override
    public Optional<Role> findByName(String name) {
        return roleRepository.findByName(name);
    }

    @Override
    @Transactional // Ensure atomicity if creation happens
    public Role findOrCreateRole(String name, String description) {
        return roleRepository.findByName(name)
                .orElseGet(() -> {
                    log.info("Role '{}' not found, creating new one.", name);
                    Role newRole = new Role();
                    newRole.setName(name);
                    newRole.setDescription(description);
                    return roleRepository.save(newRole);
                });
    }

    @Override
    @Transactional
    public RoleDto createRole(CreateRoleRequest request) {
         if (roleRepository.findByName(request.getName()).isPresent()) {
             log.warn("Attempted to create role with existing name: {}", request.getName());
             // Let DataIntegrityViolationException handler catch this
         }

        Role role = new Role();
        role.setName(request.getName());
        role.setDescription(request.getDescription());

        // Find and assign permissions
        Set<Permission> permissions = findPermissionsByNames(request.getPermissionNames());
        role.setPermissions(permissions);

        Role savedRole = roleRepository.save(role);
        log.info("Created role '{}' with ID {}", savedRole.getName(), savedRole.getId());
        return mapToDto(savedRole);
    }

    @Override
    @Transactional(readOnly = true)
    public List<RoleDto> findAllRoles() {
        return roleRepository.findAll().stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<RoleDto> findRoleById(UUID id) {
        return roleRepository.findById(id).map(this::mapToDto);
    }

    @Override
    @Transactional
    public RoleDto updateRole(UUID id, UpdateRoleRequest request) {
        Role role = roleRepository.findById(id)
                .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + id));

        boolean updated = false;
        if (StringUtils.hasText(request.getName()) && !request.getName().equals(role.getName())) {
             if (roleRepository.findByName(request.getName()).isPresent()) {
                 log.warn("Attempted to update role {} to existing name: {}", id, request.getName());
                 // Let DataIntegrityViolationException handler catch this
             }
            role.setName(request.getName());
            updated = true;
        }
        if (request.getDescription() != null && !request.getDescription().equals(role.getDescription())) {
            role.setDescription(request.getDescription());
            updated = true;
        }
        // If permissionNames list is provided in the request, update the role's permissions
        if (request.getPermissionNames() != null) {
            Set<Permission> newPermissions = findPermissionsByNames(request.getPermissionNames());
            // Check if permissions actually changed before marking as updated
            if (!role.getPermissions().equals(newPermissions)) {
                 role.setPermissions(newPermissions);
                 updated = true;
                 log.info("Updating permissions for role ID {}", id);
            }
        }


        if (updated) {
            Role updatedRole = roleRepository.save(role);
            log.info("Updated role '{}' with ID {}", updatedRole.getName(), updatedRole.getId());
            return mapToDto(updatedRole);
        } else {
            log.info("No changes detected for role with ID {}", id);
            return mapToDto(role); // Return current state if no changes
        }
    }

    @Override
    @Transactional
    public void deleteRole(UUID id) {
         Role role = roleRepository.findById(id)
                .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + id));

        // Important Consideration: Deleting a role might leave users without roles or break FK constraints
        // depending on cascade settings. A safer approach might be to disallow deletion if users are assigned,
        // or reassign users to a default role. For now, we just delete.

        // Optional: Check if role is assigned to any users before deleting
        // if (!role.getUsers().isEmpty()) {
        //     throw new DataIntegrityViolationException("Cannot delete role '" + role.getName() + "' as it is assigned to users.");
        // }

        log.warn("Deleting role '{}' with ID: {}", role.getName(), id);
        roleRepository.delete(role);
    }

     @Override
     @Transactional
     public RoleDto assignPermissionsToRole(UUID roleId, List<String> permissionNames) {
         Role role = roleRepository.findById(roleId)
                 .orElseThrow(() -> new RoleNotFoundException("Role not found with ID: " + roleId));

         Set<Permission> permissions = findPermissionsByNames(permissionNames);
         role.setPermissions(permissions); // Replace existing permissions

         Role updatedRole = roleRepository.save(role);
         log.info("Assigned permissions {} to role '{}'", permissionNames, updatedRole.getName());
         return mapToDto(updatedRole);
     }

    // --- Helper Methods ---

    /**
     * Finds a set of permissions by their names. Throws exception if any name is not found.
     */
    private Set<Permission> findPermissionsByNames(List<String> names) {
        if (CollectionUtils.isEmpty(names)) {
            return new HashSet<>(); // Return empty set if no names provided
        }
        Set<Permission> permissions = new HashSet<>();
        for (String name : names) {
            Permission permission = permissionRepository.findByName(name)
                    .orElseThrow(() -> new PermissionNotFoundException(name));
            permissions.add(permission);
        }
        return permissions;
    }


    // --- Mapper ---
    private RoleDto mapToDto(Role role) {
        List<PermissionDto> permissionDtos = role.getPermissions().stream()
                .map(p -> PermissionDto.builder()
                        .id(p.getId())
                        .name(p.getName())
                        .description(p.getDescription())
                        .createdAt(p.getCreatedAt())
                        .updatedAt(p.getUpdatedAt())
                        .build())
                .collect(Collectors.toList());

        return RoleDto.builder()
                .id(role.getId())
                .name(role.getName())
                .description(role.getDescription())
                .permissions(permissionDtos)
                .createdAt(role.getCreatedAt())
                .updatedAt(role.getUpdatedAt())
                .build();
    }
}
