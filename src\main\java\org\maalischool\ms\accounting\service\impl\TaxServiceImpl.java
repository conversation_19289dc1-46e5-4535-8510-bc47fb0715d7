package org.maalischool.ms.accounting.service.impl;

import java.util.UUID;

import org.maalischool.ms.accounting.dto.CreateTaxRequest;
import org.maalischool.ms.accounting.dto.TaxDto;
import org.maalischool.ms.accounting.dto.UpdateTaxRequest;
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException; // Import COA exception
import org.maalischool.ms.accounting.exception.TaxNotFoundException;
import org.maalischool.ms.accounting.mapper.TaxMapper;
import org.maalischool.ms.accounting.model.ChartOfAccount; // Import COA model
import org.maalischool.ms.accounting.model.Tax;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository; // Import COA repo
import org.maalischool.ms.accounting.repository.TaxRepository;
import org.maalischool.ms.accounting.service.TaxService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor // Lombok for constructor injection
@Transactional // Default transaction behavior for all public methods
public class TaxServiceImpl implements TaxService {

    private static final Logger log = LoggerFactory.getLogger(TaxServiceImpl.class);

    private final TaxRepository taxRepository;
    private final ChartOfAccountRepository chartOfAccountRepository; // Inject COA repo
    private final TaxMapper taxMapper;

    @Override
    public TaxDto createTax(CreateTaxRequest createTaxRequest) {
        log.info("Attempting to create new tax with nameEn: {}", createTaxRequest.getNameEn());

        // 1. Fetch the associated ChartOfAccount
        ChartOfAccount chartOfAccount = chartOfAccountRepository.findById(createTaxRequest.getChartOfAccountId())
                .orElseThrow(() -> {
                    log.warn("ChartOfAccount not found with id: {}", createTaxRequest.getChartOfAccountId());
                    // Use a specific or generic exception based on project standards
                    return new ChartOfAccountNotFoundException(createTaxRequest.getChartOfAccountId());
                    // Or return new ResourceNotFoundException("ChartOfAccount", "id", createTaxRequest.getChartOfAccountId());
                });
        log.debug("Found ChartOfAccount: {}", chartOfAccount.getId());

        // 2. Map DTO to entity (excluding chartOfAccount initially)
        Tax tax = taxMapper.toTax(createTaxRequest);

        // 3. Set the fetched ChartOfAccount
        tax.setChartOfAccount(chartOfAccount);

        // 4. Save the Tax entity
        Tax savedTax = taxRepository.save(tax);
        log.info("Successfully created tax with id: {} linked to ChartOfAccount id: {}",
                 savedTax.getId(), chartOfAccount.getId());

        // 5. Map saved entity back to DTO
        return taxMapper.toTaxDto(savedTax);
    }

    @Override
    @Transactional(readOnly = true) // Optimize for read operations
    public TaxDto getTaxById(UUID id) {
        log.debug("Fetching tax with id: {}", id);
        Tax tax = taxRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Tax not found with id: {}", id);
                    return new TaxNotFoundException(id);
                });
        return taxMapper.toTaxDto(tax);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TaxDto> getAllTaxes(Pageable pageable) {
        log.debug("Fetching all taxes with pagination: {}", pageable);
        Page<Tax> taxPage = taxRepository.findAll(pageable);
        return taxPage.map(taxMapper::toTaxDto);
    }

    @Override
    public TaxDto updateTax(UUID id, UpdateTaxRequest updateTaxRequest) {
        log.info("Attempting to update tax with id: {}", id);

        // 1. Fetch the existing Tax entity
        Tax existingTax = taxRepository.findById(id)
                .orElseThrow(() -> {
                    log.warn("Tax not found for update with id: {}", id);
                    return new TaxNotFoundException(id);
                });
        log.debug("Found existing tax: {}", existingTax.getId());

        // 2. Fetch the potentially updated ChartOfAccount
        ChartOfAccount chartOfAccount = chartOfAccountRepository.findById(updateTaxRequest.getChartOfAccountId())
                .orElseThrow(() -> {
                    log.warn("ChartOfAccount not found with id: {}", updateTaxRequest.getChartOfAccountId());
                    return new ChartOfAccountNotFoundException(updateTaxRequest.getChartOfAccountId());
                    // Or return new ResourceNotFoundException("ChartOfAccount", "id", updateTaxRequest.getChartOfAccountId());
                });
        log.debug("Found ChartOfAccount for update: {}", chartOfAccount.getId());

        // 3. Update fields from DTO (excluding chartOfAccount initially)
        taxMapper.updateTaxFromDto(updateTaxRequest, existingTax);

        // 4. Set the (potentially new) ChartOfAccount
        existingTax.setChartOfAccount(chartOfAccount);

        // 5. Save the updated Tax entity
        Tax updatedTax = taxRepository.save(existingTax);
        log.info("Successfully updated tax with id: {} now linked to ChartOfAccount id: {}",
                 updatedTax.getId(), chartOfAccount.getId());

        // 6. Map updated entity back to DTO
        return taxMapper.toTaxDto(updatedTax);
    }

    @Override
    public void deleteTax(UUID id) {
        log.info("Deleting tax with id: {}", id);
        if (!taxRepository.existsById(id)) {
            log.warn("Tax not found for deletion with id: {}", id);
            throw new TaxNotFoundException(id);
        }
        taxRepository.deleteById(id);
        log.info("Successfully deleted tax with id: {}", id);
    }
}
