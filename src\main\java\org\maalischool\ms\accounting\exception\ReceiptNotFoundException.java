package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ReceiptNotFoundException extends RuntimeException {

    public ReceiptNotFoundException(UUID id) {
        super("Receipt not found with ID: " + id);
    }

    public ReceiptNotFoundException(String receiptNumber) {
        super("Receipt not found with number: " + receiptNumber);
    }

     public ReceiptNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
