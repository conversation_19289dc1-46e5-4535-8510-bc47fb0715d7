package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.Map;

/**
 * DTO for requesting an AI-generated report
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AIReportRequest {
    
    @NotBlank(message = "Report type is required")
    private String reportType; // "GENERAL_LEDGER", "EXPENSES", "INCOME_STATEMENT", "TRIAL_BALANCE", "BALANCE_SHEET"
    
    @NotNull(message = "Start date is required")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;
    
    @NotNull(message = "End date is required")
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;
    
    private Map<String, Object> filters; // Additional filters as key-value pairs
}
