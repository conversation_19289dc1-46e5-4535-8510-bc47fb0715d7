package org.maalischool.ms.accounting.mapper;

import java.math.BigDecimal;

import org.maalischool.ms.accounting.dto.GeneralLedgerEntryDto;
import org.maalischool.ms.accounting.model.GeneralLedgerView;
import org.maalischool.ms.accounting.model.JournalEntryLine;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface GeneralLedgerMapper {

    @Mapping(source = "journalEntry.entryDate", target = "date")
    @Mapping(source = "journalEntry.id", target = "journalEntryId")
    @Mapping(source = "chartOfAccount.accountNumber", target = "accountCode")
    @Mapping(source = "chartOfAccount.nameEn", target = "accountName")
    @Mapping(source = "journalEntry.description", target = "description")
    @Mapping(target = "debit", expression = "java(journalEntryLine.getType() == org.maalischool.ms.accounting.model.enums.TransactionType.DEBIT ? journalEntryLine.getAmount() : zero())")
    @Mapping(target = "credit", expression = "java(journalEntryLine.getType() == org.maalischool.ms.accounting.model.enums.TransactionType.CREDIT ? journalEntryLine.getAmount() : zero())")
    @Mapping(source = "student.id", target = "studentId")
    @Mapping(source = "student.admissionNumber", target = "studentAdmissionNumber")
    @Mapping(source = "student.userAccount.firstName", target = "studentFirstName")
    @Mapping(source = "student.userAccount.lastName", target = "studentLastName")
    @Mapping(target = "runningBalance", ignore = true)
    @Mapping(target = "cumulativeBalance", ignore = true)
    GeneralLedgerEntryDto toDto(JournalEntryLine journalEntryLine);

    @Mapping(source = "entryDate", target = "date")
    @Mapping(source = "journalEntryId", target = "journalEntryId")
    @Mapping(source = "accountNumber", target = "accountCode")
    @Mapping(source = "accountNameEn", target = "accountName")
    @Mapping(source = "journalDescription", target = "description")
    @Mapping(target = "debit", expression = "java(generalLedgerView.getDebitAmount())")
    @Mapping(target = "credit", expression = "java(generalLedgerView.getCreditAmount())")
    @Mapping(target = "runningBalance", expression = "java(generalLedgerView.getRunningBalance())")
    @Mapping(source = "studentId", target = "studentId")
    @Mapping(source = "studentAdmissionNumber", target = "studentAdmissionNumber")
    @Mapping(source = "studentFirstName", target = "studentFirstName")
    @Mapping(source = "studentLastName", target = "studentLastName")
    @Mapping(target = "cumulativeBalance", ignore = true)
    GeneralLedgerEntryDto toDto(GeneralLedgerView generalLedgerView);

    default BigDecimal zero() {
        return BigDecimal.ZERO;
    }
}
