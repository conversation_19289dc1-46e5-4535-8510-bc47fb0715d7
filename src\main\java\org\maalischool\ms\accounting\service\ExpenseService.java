package org.maalischool.ms.accounting.service;

import java.time.LocalDate;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.CreateExpenseRequest;
import org.maalischool.ms.accounting.dto.ExpenseDto;
import org.maalischool.ms.accounting.dto.UpdateExpenseRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for managing Expenses.
 */
public interface ExpenseService {

    /**
     * Creates a new expense record.
     *
     * @param request the request object containing expense details.
     * @return the created expense DTO.
     */
    ExpenseDto createExpense(CreateExpenseRequest request);

    /**
     * Retrieves an expense by its ID.
     *
     * @param id the UUID of the expense.
     * @return the expense DTO.
     * @throws org.maalischool.ms.accounting.exception.ExpenseNotFoundException if the expense is not found.
     */
    ExpenseDto getExpenseById(UUID id);

    /**
     * Retrieves all expenses with pagination.
     *
     * @param pageable pagination information.
     * @return a page of expense DTOs.
     */
    Page<ExpenseDto> getAllExpenses(Pageable pageable);

    /**
     * Finds expenses within a specific date range.
     *
     * @param startDate the start date (inclusive).
     * @param endDate   the end date (inclusive).
     * @param pageable  pagination information.
     * @return a page of expense DTOs within the date range.
     */
    Page<ExpenseDto> findExpensesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Finds expenses belonging to a specific category.
     *
     * @param categoryId the UUID of the expense category.
     * @param pageable   pagination information.
     * @return a page of expense DTOs for the category.
     */
    Page<ExpenseDto> findExpensesByCategory(UUID categoryId, Pageable pageable);

    /**
     * Updates an existing expense record.
     *
     * @param id      the UUID of the expense to update.
     * @param request the request object containing updated expense details.
     * @return the updated expense DTO.
     * @throws org.maalischool.ms.accounting.exception.ExpenseNotFoundException if the expense is not found.
     * @throws org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException if the category in the request is not found.
     */
    ExpenseDto updateExpense(UUID id, UpdateExpenseRequest request);

    /**
     * Deletes an expense record by its ID.
     *
     * @param id the UUID of the expense to delete.
     * @throws org.maalischool.ms.accounting.exception.ExpenseNotFoundException if the expense is not found.
     */
    void deleteExpense(UUID id);
}
