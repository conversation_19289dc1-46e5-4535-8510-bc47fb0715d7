package org.maalischool.ms.auth.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.maalischool.ms.student.model.Guardian; // Import Guardian
import org.maalischool.ms.student.model.Student; // Import Student
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Entity
// "User" is often a reserved keyword in SQL, hence "app_user"
@Table(name = "app_user", uniqueConstraints = {
        @UniqueConstraint(columnNames = "email")
})
@Data // Includes @Getter, @Setter, @ToString, @EqualsAndHashCode, @RequiredArgsConstructor
@NoArgsConstructor
@AllArgsConstructor
@Builder // Builder pattern for object creation
@EqualsAndHashCode(exclude = {"roles", "studentProfile", "guardianProfile"})
@EntityListeners(AuditingEntityListener.class) // Enable JPA Auditing
public class User implements UserDetails {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @NotBlank(message = "{validation.firstName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.firstName.size}")
    @Column(nullable = false, length = 50)
    private String firstName;

    @NotBlank(message = "{validation.lastName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.lastName.size}")
    @Column(nullable = false, length = 50)
    private String lastName;

    @NotBlank(message = "{validation.email.notBlank}")
    @Email(message = "{validation.email.invalid}")
    @Size(max = 100, message = "{validation.email.size}")
    @Column(nullable = false, unique = true, length = 100)
    private String email;

    @NotBlank(message = "{validation.password.notBlank}")
    // @Size validation on raw password happens in DTO/Service layer before hashing
    @Column(nullable = false, length = 100) // Length depends on hashing algorithm (BCrypt ~60)
    private String password; // Store hashed password

    @Size(max = 20, message = "{validation.phoneNumber.size}")
    @Column(length = 20)
    private String phoneNumber;

    @ManyToMany(fetch = FetchType.EAGER) // Load roles eagerly with the user
    @JoinTable(
            name = "user_role",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    @Builder.Default // Initialize with default value for builder
    private Set<Role> roles = new HashSet<>();

    // --- UserDetails Fields ---
    @Column(nullable = false)
    @Builder.Default
    private boolean enabled = false; // Default to false, require activation

    @Column(nullable = false)
    @Builder.Default
    private boolean accountLocked = false;

    @Column(nullable = false)
    @Builder.Default
    private boolean accountExpired = false; // Set based on policy if needed

    @Column(nullable = false)
    @Builder.Default
    private boolean credentialsExpired = false; // Set based on policy if needed

    // --- Account Management Fields ---
    @Column(length = 36) // UUID length
    private String activationToken;
    private LocalDateTime activationTokenExpiry;

    @Column(length = 36) // UUID length
    private String resetToken;
    private LocalDateTime resetTokenExpiry;

    // --- Profile Links ---
    // A User can be linked to EITHER a Student OR a Guardian profile (or neither)
    @OneToOne(mappedBy = "userAccount", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Student studentProfile;

    @OneToOne(mappedBy = "userAccount", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Guardian guardianProfile; // Renamed field

    // --- Auditing Fields ---
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;

    // --- UserDetails Implementation ---

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // Collect authorities from roles and their permissions
        Set<GrantedAuthority> authorities = roles.stream()
                .map(role -> (GrantedAuthority) new SimpleGrantedAuthority(role.getName()))
                .collect(Collectors.toSet());

        // If permissions are directly assigned or needed, add them here too
        // authorities.addAll(roles.stream()
        //         .flatMap(role -> role.getPermissions().stream())
        //         .map(permission -> (GrantedAuthority) new SimpleGrantedAuthority(permission.getName()))
        //         .collect(Collectors.toSet()));

        return authorities;
    }

    @Override
    public String getUsername() {
        // Use email as the username for authentication
        return email;
    }

    @Override
    public boolean isAccountNonExpired() {
        return !accountExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return !accountLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return !credentialsExpired;
    }

    @Override
    public boolean isEnabled() {
        return enabled;
    }

    // Explicitly implement getPassword() required by UserDetails
    @Override
    public String getPassword() {
        return this.password;
    }

    // --- Helper methods ---
    public void addRole(Role role) {
        this.roles.add(role);
        role.getUsers().add(this);
    }

    public void removeRole(Role role) {
        this.roles.remove(role);
        role.getUsers().remove(this);
    }

    // --- Convenience methods for profile linking ---
    public void setStudentProfile(Student student) {
        if (student == null) {
            if (this.studentProfile != null) {
                this.studentProfile.setUserAccount(null);
            }
        } else {
            student.setUserAccount(this);
        }
        this.studentProfile = student;
    }

    public void setGuardianProfile(Guardian guardian) { // Renamed method and parameter type
        if (guardian == null) {
            if (this.guardianProfile != null) {
                this.guardianProfile.setUserAccount(null);
            }
        } else {
            guardian.setUserAccount(this);
        }
        this.guardianProfile = guardian;
    }
}
