package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.*;
import org.maalischool.ms.accounting.exception.FeeCategoryNotFoundException;
import org.maalischool.ms.accounting.exception.FeeNotFoundException;
import org.maalischool.ms.accounting.model.Fee;
import org.maalischool.ms.accounting.model.FeeCategory;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.FeeCategoryRepository;
import org.maalischool.ms.accounting.repository.FeeRepository;
import org.maalischool.ms.accounting.repository.StudentFeeRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.FeeService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.maalischool.ms.schoolmanagement.model.AcademicYear;
import org.maalischool.ms.schoolmanagement.service.AcademicYearService;
import org.maalischool.ms.schoolmanagement.dto.SimpleAcademicYearDto;
// --- Added Imports ---
import org.maalischool.ms.auth.model.User; // Import your User entity
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
// --- End Added Imports ---
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FeeServiceImpl implements FeeService {

    private static final Logger log = LoggerFactory.getLogger(FeeServiceImpl.class);
    private final FeeRepository feeRepository;
    private final FeeCategoryRepository feeCategoryRepository;
    private final StudentFeeRepository studentFeeRepository; // To check assignments before deletion
    private final AccountingAuditLogService auditLogService;
    private final AcademicYearService academicYearService;

    @Override
    @Transactional
    public FeeDto createFee(CreateFeeRequest request) {
        log.info("Creating new Fee: NameEn={}, AcademicYearId={}, CategoryID={}", request.getNameEn(),
                request.getAcademicYearId(),
                request.getFeeCategoryId());

        FeeCategory feeCategory = feeCategoryRepository.findById(request.getFeeCategoryId())
                .orElseThrow(() -> new FeeCategoryNotFoundException(request.getFeeCategoryId()));

        AcademicYear academicYear = academicYearService.getAcademicYearById(request.getAcademicYearId())
                .map(dto -> {
                    // Convert DTO back to entity - this is a temporary solution
                    // In a real scenario, you might want to add a method to get the entity directly
                    AcademicYear entity = new AcademicYear();
                    entity.setId(dto.getId());
                    entity.setName(dto.getName());
                    entity.setStartDate(dto.getStartDate());
                    entity.setEndDate(dto.getEndDate());
                    entity.setActive(dto.isActive());
                    return entity;
                })
                .orElseThrow(() -> new IllegalArgumentException(
                        "Academic Year not found with ID: " + request.getAcademicYearId()));

        // Check for duplicates based on English name, year, and category
        feeRepository
                .findByNameEnAndAcademicYearIdAndFeeCategoryId(request.getNameEn(), request.getAcademicYearId(),
                        request.getFeeCategoryId()) // Use findByNameEn...
                .ifPresent(f -> {
                    throw new IllegalArgumentException(
                            String.format("Fee with English Name '%s', Year '%s', Category '%s' already exists.",
                                    request.getNameEn(), academicYear.getName(), feeCategory.getNameEn())); // Use
                                                                                                            // nameEn
                });

        Fee fee = Fee.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .amount(request.getAmount())
                .academicYear(academicYear)
                .dueDate(request.getDueDate())
                .feeCategory(feeCategory)
                .applicableGradeId(request.getApplicableGradeId())
                .applicableStageId(request.getApplicableStageId())
                .applicableBranchId(request.getApplicableBranchId())
                .active(true) // Default to active
                .build();

        Fee savedFee = feeRepository.save(fee);
        log.info("Successfully created Fee with ID: {}", savedFee.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_FEE,
                savedFee.getId(),
                String.format("Created Fee: %s (%s) - Amount: %s, Due: %s",
                        savedFee.getNameEn(), savedFee.getAcademicYear(), savedFee.getAmount(), savedFee.getDueDate()) // Log
                                                                                                                       // English
                                                                                                                       // name
        );

        return mapToDto(savedFee);
    }

    @Override
    @Transactional(readOnly = true)
    public FeeDto getFeeById(UUID id) {
        log.debug("Fetching Fee by ID: {}", id);
        Fee fee = feeRepository.findById(id)
                .orElseThrow(() -> new FeeNotFoundException(id));
        return mapToDto(fee);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<FeeDto> getAllFees(Pageable pageable) {
        log.debug("Fetching Fees page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return feeRepository.findAll(pageable).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<FeeDto> getActiveFeesByAcademicYear(UUID academicYearId) {
        log.debug("Fetching active Fees for academic year ID: {}", academicYearId);
        return feeRepository.findByAcademicYearIdAndActiveTrueOrderByFeeCategoryNameEnAscNameEnAsc(academicYearId) // Order
                // by
                // English
                // names
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FeeDto> getFeesByCategory(UUID categoryId) {
        log.debug("Fetching active Fees for category ID: {}", categoryId);
        // Ensure category exists (optional, depends on desired behavior)
        if (!feeCategoryRepository.existsById(categoryId)) {
            throw new FeeCategoryNotFoundException(categoryId);
        }
        return feeRepository.findByFeeCategoryIdAndActiveTrueOrderByNameEnAsc(categoryId) // Order by English name
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<FeeDto> findApplicableFeesForStudentContext(UUID academicYearId, UUID gradeId, UUID stageId,
            UUID branchId) {
        log.debug("Finding applicable fees for AcademicYear ID: {}, Grade: {}, Stage: {}, Branch: {}", academicYearId,
                gradeId,
                stageId, branchId);
        // Use the custom query in the repository
        return feeRepository.findApplicableFees(academicYearId, gradeId, stageId, branchId)
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public FeeDto updateFee(UUID id, UpdateFeeRequest request) {
        log.info("Updating Fee with ID: {}", id);
        Fee fee = feeRepository.findById(id)
                .orElseThrow(() -> new FeeNotFoundException(id));

        FeeCategory feeCategory = feeCategoryRepository.findById(request.getFeeCategoryId())
                .orElseThrow(() -> new FeeCategoryNotFoundException(request.getFeeCategoryId()));

        AcademicYear academicYear = academicYearService.getAcademicYearById(request.getAcademicYearId())
                .map(dto -> {
                    // Convert DTO back to entity - this is a temporary solution
                    AcademicYear entity = new AcademicYear();
                    entity.setId(dto.getId());
                    entity.setName(dto.getName());
                    entity.setStartDate(dto.getStartDate());
                    entity.setEndDate(dto.getEndDate());
                    entity.setActive(dto.isActive());
                    return entity;
                })
                .orElseThrow(() -> new IllegalArgumentException(
                        "Academic Year not found with ID: " + request.getAcademicYearId()));

        // Check for duplicates if unique constraints (English name, year, category) are
        // changed
        if (!fee.getNameEn().equalsIgnoreCase(request.getNameEn()) || // Check English name
                !fee.getAcademicYear().getId().equals(request.getAcademicYearId()) ||
                !fee.getFeeCategory().getId().equals(request.getFeeCategoryId())) {
            feeRepository
                    .findByNameEnAndAcademicYearIdAndFeeCategoryId(request.getNameEn(), request.getAcademicYearId(),
                            request.getFeeCategoryId()) // Use findByNameEn...
                    .ifPresent(f -> {
                        if (!f.getId().equals(id)) { // Ensure it's not the same fee
                            throw new IllegalArgumentException(String.format(
                                    "Another Fee with English Name '%s', Year '%s', Category '%s' already exists.",
                                    request.getNameEn(), academicYear.getName(), feeCategory.getNameEn())); // Use
                                                                                                            // nameEn
                        }
                    });
        }
        // TODO: Consider adding a similar check for nameAr if it also needs to be
        // unique

        String oldDetails = String.format("NameEn: %s, Year: %s, Cat: %s, Amount: %s, Due: %s, Active: %s", // Log
                                                                                                            // English
                                                                                                            // name
                fee.getNameEn(), fee.getAcademicYear(), fee.getFeeCategory().getNameEn(), fee.getAmount(),
                fee.getDueDate(), fee.isActive()); // Use nameEn

        fee.setNameEn(request.getNameEn());
        fee.setNameAr(request.getNameAr());
        fee.setDescriptionEn(request.getDescriptionEn());
        fee.setDescriptionAr(request.getDescriptionAr());
        fee.setAmount(request.getAmount());
        fee.setAcademicYear(academicYear);
        fee.setDueDate(request.getDueDate());
        fee.setFeeCategory(feeCategory);
        fee.setApplicableGradeId(request.getApplicableGradeId());
        fee.setApplicableStageId(request.getApplicableStageId());
        fee.setApplicableBranchId(request.getApplicableBranchId());
        fee.setActive(request.getActive());

        Fee updatedFee = feeRepository.save(fee);
        log.info("Successfully updated Fee with ID: {}", updatedFee.getId());

        String newDetails = String.format("NameEn: %s, Year: %s, Cat: %s, Amount: %s, Due: %s, Active: %s", // Log
                                                                                                            // English
                                                                                                            // name
                updatedFee.getNameEn(), updatedFee.getAcademicYear(), updatedFee.getFeeCategory().getNameEn(),
                updatedFee.getAmount(), updatedFee.getDueDate(), updatedFee.isActive()); // Use nameEn

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_FEE,
                updatedFee.getId(),
                "Updated Fee. Old: [" + oldDetails + "], New: [" + newDetails + "]");

        return mapToDto(updatedFee);
    }

    @Override
    @Transactional
    public void deleteFee(UUID id) {
        log.warn("Attempting to delete Fee with ID: {}", id);
        Fee fee = feeRepository.findById(id)
                .orElseThrow(() -> new FeeNotFoundException(id));

        // Check if this fee has been assigned to any student
        // Need a method like `existsByFeeId` in StudentFeeRepository
        // Example: if (studentFeeRepository.existsByFeeId(id)) {
        // throw new IllegalStateException("Cannot delete Fee that has been assigned to
        // students. ID: " + id);
        // }
        log.warn("Deletion check for associated StudentFees for Fee {} is not fully implemented.", id);

        feeRepository.delete(fee);
        log.warn("Successfully deleted Fee with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_FEE,
                id,
                String.format("Deleted Fee: %s (%s)", fee.getNameEn(), fee.getAcademicYear()) // Log English name
        );
    }

    @Override
    @Transactional
    public FeeDto activateFee(UUID id) {
        log.info("Activating Fee with ID: {}", id);
        return updateFeeStatus(id, true);
    }

    @Override
    @Transactional
    public FeeDto deactivateFee(UUID id) {
        log.info("Deactivating Fee with ID: {}", id);
        return updateFeeStatus(id, false);
    }

    private FeeDto updateFeeStatus(UUID id, boolean active) {
        Fee fee = feeRepository.findById(id)
                .orElseThrow(() -> new FeeNotFoundException(id));

        if (fee.isActive() == active) {
            log.warn("Fee {} already has active status {}", id, active);
            return mapToDto(fee);
        }

        boolean oldStatus = fee.isActive();
        fee.setActive(active);
        Fee updatedFee = feeRepository.save(fee);
        log.info("Successfully set active status to {} for Fee ID: {}", active, id);

        auditLogService.logAction(
                getCurrentUserId(),
                active ? AccountingActionType.ACTIVATE : AccountingActionType.DEACTIVATE,
                AccountingConstants.ENTITY_FEE,
                updatedFee.getId(),
                String.format("Changed active status from %s to %s for Fee %s (%s)", oldStatus, active,
                        updatedFee.getNameEn(), updatedFee.getAcademicYear()) // Log English name
        );

        return mapToDto(updatedFee);
    }

    // --- Helper Methods ---

    private FeeDto mapToDto(Fee fee) {
        if (fee == null)
            return null;
        return FeeDto.builder()
                .id(fee.getId())
                .nameEn(fee.getNameEn())
                .nameAr(fee.getNameAr())
                .descriptionEn(fee.getDescriptionEn())
                .descriptionAr(fee.getDescriptionAr())
                .amount(fee.getAmount())
                .academicYear(mapAcademicYearToSimpleDto(fee.getAcademicYear()))
                .dueDate(fee.getDueDate())
                .feeCategory(mapCategoryToSimpleDto(fee.getFeeCategory())) // Use Simple DTO for category
                .applicableGradeId(fee.getApplicableGradeId())
                .applicableStageId(fee.getApplicableStageId())
                .applicableBranchId(fee.getApplicableBranchId())
                .active(fee.isActive())
                .createdDate(fee.getCreatedDate())
                .lastModifiedDate(fee.getLastModifiedDate())
                .build();
    }

    private SimpleFeeCategoryDto mapCategoryToSimpleDto(FeeCategory category) {
        if (category == null)
            return null;
        // Assuming SimpleFeeCategoryDto exists or create it
        // For now, using FeeCategoryDto as placeholder if SimpleFeeCategoryDto is
        // missing
        return SimpleFeeCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn()) // Use English name
                .nameAr(category.getNameAr()) // Use Arabic name
                .build();
    }

    private SimpleAcademicYearDto mapAcademicYearToSimpleDto(AcademicYear academicYear) {
        if (academicYear == null)
            return null;
        return SimpleAcademicYearDto.builder()
                .id(academicYear.getId())
                .name(academicYear.getName())
                .startDate(academicYear.getStartDate())
                .endDate(academicYear.getEndDate())
                .active(academicYear.isActive())
                .build();
    }

    // Replaced placeholder with actual implementation
    private UUID getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || authentication.getPrincipal() == null) {
            log.error("Cannot determine current user for audit logging. No authenticated principal found.");
            // Depending on requirements, you might throw an exception or return a default
            // system user ID.
            // Throwing an exception is safer if a user context is always required.
            throw new IllegalStateException("Authenticated user context not found for audit logging.");
        }

        Object principal = authentication.getPrincipal();

        if (principal instanceof User user) { // Check if the principal is your User entity instance
            return user.getId();
        } else if (principal instanceof UserDetails userDetails) {
            // Fallback if principal is UserDetails but not your User entity (less likely
            // with custom User)
            // This might require fetching the User entity based on username if ID isn't
            // directly available
            log.warn("Principal is UserDetails but not the expected User entity type. Attempting to log username: {}",
                    userDetails.getUsername());
            // Returning null or throwing here depends on whether you can map username back
            // to UUID easily/reliably
            // For now, let's throw, as we expect our User entity.
            throw new IllegalStateException("Principal is not an instance of the expected User class.");
        } else if (principal instanceof String && "anonymousUser".equals(principal)) {
            log.error("Attempting to perform audited action as anonymous user.");
            throw new IllegalStateException("Anonymous user cannot perform this audited action.");
        } else {
            log.error("Unexpected principal type found in SecurityContext: {}", principal.getClass().getName());
            throw new IllegalStateException("Unexpected principal type for audit logging.");
        }
    }
}
