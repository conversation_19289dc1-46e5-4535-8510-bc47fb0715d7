package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.StudentFeeDiscount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.UUID;

/**
 * Repository interface for StudentFeeDiscount entities.
 */
@Repository
public interface StudentFeeDiscountRepository extends JpaRepository<StudentFeeDiscount, UUID> { // Assuming UUID is the PK type for the join table entity

    /**
     * Checks if any StudentFeeDiscount exists for a given Discount ID.
     * Used to prevent deletion of discounts that are currently assigned.
     *
     * @param discountId The UUID of the Discount.
     * @return true if an assignment exists, false otherwise.
     */
    boolean existsByDiscountId(UUID discountId);

    /**
     * Checks if any StudentFeeDiscount exists for a given StudentFee ID.
     *
     * @param studentFeeId The UUID of the StudentFee.
     * @return true if an assignment exists, false otherwise.
     */
    boolean existsByStudentFeeId(UUID studentFeeId);

    // JpaRepository provides save, findById, findAll, deleteById, deleteAll etc.
    // deleteAll(Iterable<? extends T> entities) can be used to delete the set of discounts for a student fee.
}
