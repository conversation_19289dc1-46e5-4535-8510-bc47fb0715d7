package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.UUID;

import org.maalischool.ms.accounting.model.enums.TransactionType;
import org.maalischool.ms.student.dto.SimpleStudentDto;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class JournalEntryLineDto {
    private UUID id;
    private SimpleChartOfAccountDto chartOfAccount;
    private TransactionType type;
    private BigDecimal amount;
    private String description;
    private SimpleStudentDto student;
}
