package org.maalischool.ms.accounting.dto;

import java.util.UUID;
import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto; // Import the simple DTO

import lombok.Data;

import java.math.BigDecimal;

@Data
public class TaxDto {
    private UUID id;
    private String nameAr;
    private String nameEn;
    private String descriptionAr;
    private String descriptionEn;
    private BigDecimal percent;
    private SimpleChartOfAccountDto chartOfAccount; // Use the simple DTO
    // TODO: Add other relevant fields if needed, e.g., createdAt, updatedAt
}
