package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.InventoryAdjustmentRequest;
import org.maalischool.ms.accounting.dto.InventoryTransactionDto;
import org.maalischool.ms.accounting.dto.ItemDto; // For returning current stock
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

/**
 * Service interface for managing Inventory levels and transactions.
 */
public interface InventoryService {

    /**
     * Records an inventory adjustment (increase or decrease).
     * Updates the item's quantity on hand and creates an inventory transaction record.
     *
     * @param request the request object containing adjustment details.
     * @return the created inventory transaction DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     * @throws org.maalischool.ms.accounting.exception.InsufficientStockException if attempting to decrease stock below zero (optional, based on config).
     */
    InventoryTransactionDto adjustInventory(InventoryAdjustmentRequest request);

    /**
     * Retrieves the current quantity on hand for a specific item.
     *
     * @param itemId the UUID of the item.
     * @return the current quantity on hand.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    BigDecimal getQuantityOnHand(UUID itemId);

    /**
     * Retrieves the inventory transaction history for a specific item.
     *
     * @param itemId    the UUID of the item.
     * @param startDate the start date for the history (inclusive).
     * @param endDate   the end date for the history (inclusive).
     * @param pageable  pagination information.
     * @return a page of inventory transaction DTOs for the item.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    Page<InventoryTransactionDto> getTransactionHistoryForItem(UUID itemId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Retrieves all inventory transactions within a date range.
     *
     * @param startDate the start date (inclusive).
     * @param endDate   the end date (inclusive).
     * @param pageable  pagination information.
     * @return a page of all inventory transaction DTOs within the date range.
     */
    Page<InventoryTransactionDto> getAllTransactionHistory(LocalDate startDate, LocalDate endDate, Pageable pageable);

    // Note: Direct updates or deletions of inventory transactions are generally discouraged.
    // Adjustments should be made via new transactions to maintain audit trail.
}
