package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateItemRequest;
import org.maalischool.ms.accounting.dto.ItemDto;
import org.maalischool.ms.accounting.dto.UpdateItemRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing Inventory Items.
 */
public interface ItemService {

    /**
     * Creates a new inventory item.
     *
     * @param request the request object containing item details.
     * @return the created item DTO.
     */
    ItemDto createItem(CreateItemRequest request);

    /**
     * Retrieves an item by its ID.
     *
     * @param id the UUID of the item.
     * @return the item DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    ItemDto getItemById(UUID id);

    /**
     * Retrieves an item by its Item Code.
     *
     * @param itemCode the Item Code of the item.
     * @return the item DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    ItemDto getItemByItemCode(String itemCode); // Renamed from getItemBySku

    /**
     * Retrieves all items with pagination.
     *
     * @param pageable pagination information.
     * @return a page of item DTOs.
     */
    Page<ItemDto> getAllItems(Pageable pageable);

     /**
     * Retrieves all active items.
     *
     * @return a list of active item DTOs.
     */
    List<ItemDto> getAllActiveItems();

    /**
     * Updates an existing inventory item.
     *
     * @param id      the UUID of the item to update.
     * @param request the request object containing updated item details.
     * @return the updated item DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    ItemDto updateItem(UUID id, UpdateItemRequest request);

    /**
     * Deletes an inventory item by its ID.
     * Consider implications if the item has stock or is part of transactions.
     * A soft delete (setting active=false) is strongly recommended.
     *
     * @param id the UUID of the item to delete.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     * @throws IllegalStateException if the item cannot be deleted (e.g., has stock).
     */
    void deleteItem(UUID id); // Strongly consider soft delete via update instead

    /**
     * Activates an inventory item.
     *
     * @param id the UUID of the item to activate.
     * @return the activated item DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    ItemDto activateItem(UUID id);

    /**
     * Deactivates an inventory item.
     *
     * @param id the UUID of the item to deactivate.
     * @return the deactivated item DTO.
     * @throws org.maalischool.ms.accounting.exception.ItemNotFoundException if the item is not found.
     */
    ItemDto deactivateItem(UUID id);
}
