package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class UpdateExpenseRequest {
    @NotNull(message = "Expense date cannot be null")
    private LocalDate expenseDate;

    @NotNull(message = "Amount cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount must be positive")
    private BigDecimal amount;

    @NotBlank(message = "English description cannot be blank")
    @Size(max = 500, message = "English description must be less than 500 characters")
    private String descriptionEn;

    @NotBlank(message = "Arabic description cannot be blank")
    @Size(max = 500, message = "Arabic description must be less than 500 characters")
    private String descriptionAr;

    @Size(max = 255, message = "Vendor name must be less than 255 characters")
    private String vendor;

    @Size(max = 100, message = "Reference number must be less than 100 characters")
    private String referenceNumber;

    @NotNull(message = "Expense category ID cannot be null")
    private UUID categoryId;

    // REMOVED: Expense account is derived from the category
    // @NotNull(message = "Expense account (Chart of Account) ID cannot be null")
    // private UUID expenseAccountId;

    @NotNull(message = "Payment account (Chart of Account) ID cannot be null")
    private UUID paymentAccountId;

    // Optional Tax ID
    private UUID taxId;
}
