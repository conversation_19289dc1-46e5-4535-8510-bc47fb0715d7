package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Data
@Builder
public class BudgetDto {
    private UUID id;
    private String academicYear;
    private String period;
    private SimpleChartOfAccountDto chartOfAccount; // Use Simple DTO (nullable)
    private SimpleExpenseCategoryDto expenseCategory; // Use Simple DTO (nullable)
    private BigDecimal budgetedAmount;
    private String notes;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
