package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND) // 404 Not Found
public class ChartOfAccountNotFoundException extends RuntimeException {

    public ChartOfAccountNotFoundException(UUID accountId) {
        super("Chart of Account not found with ID: " + accountId);
    }

     public ChartOfAccountNotFoundException(String accountNumber) {
        super("Chart of Account not found with Account Number: " + accountNumber);
    }
}
