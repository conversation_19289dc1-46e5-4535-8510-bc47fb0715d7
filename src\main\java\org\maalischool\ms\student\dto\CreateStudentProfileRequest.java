package org.maalischool.ms.student.dto;

import java.time.LocalDate;
import java.util.UUID;

import org.maalischool.ms.student.model.Gender;
import org.maalischool.ms.student.model.GuardianType;
import org.maalischool.ms.student.model.IdType;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Request DTO to create a Student profile and a new User account with
 * ROLE_STUDENT.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateStudentProfileRequest {

    // User information fields
    @NotBlank(message = "{validation.firstName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.firstName.size}")
    private String firstName;

    @NotBlank(message = "{validation.lastName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.lastName.size}")
    private String lastName;

    @NotBlank(message = "{validation.email.notBlank}")
    @Email(message = "{validation.email.invalid}")
    @Size(max = 100, message = "{validation.email.size}")
    private String email;

    @NotBlank(message = "{validation.password.notBlank}")
    @Size(min = 8, max = 100, message = "{validation.password.size}")
    private String password;

    @Size(max = 20, message = "{validation.phoneNumber.size}")
    private String phoneNumber; // Optional field

    // Student profile fields
    @NotNull(message = "{validation.student.dateOfBirth.notNull}")
    @PastOrPresent(message = "{validation.student.dateOfBirth.pastOrPresent}")
    private LocalDate dateOfBirth;

    @NotNull(message = "{validation.student.gender.notNull}")
    private Gender gender;

    @Size(max = 255, message = "{validation.student.address.size}")
    private String address;

    @NotBlank(message = "{validation.student.nationalId.notBlank}")
    @Size(max = 50, message = "{validation.student.nationalId.size}")
    private String nationalId;

    @NotNull(message = "{validation.student.idType.notNull}")
    private IdType idType;

    // Optional: ID of the guardian to link this student to
    private UUID guardianId;

    @NotNull(message = "{validation.student.guardianType.notNull}")
    private GuardianType guardianType;

    // Optional: ID of the nationality (country) for this student
    private UUID nationalityId;
}
