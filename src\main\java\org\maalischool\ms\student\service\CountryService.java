package org.maalischool.ms.student.service;

import org.maalischool.ms.student.dto.CountryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface CountryService {

    /**
     * Retrieves a country by its ID.
     *
     * @param countryId The UUID of the country.
     * @return Optional containing the CountryDto if found.
     */
    Optional<CountryDto> getCountryById(UUID countryId);

    /**
     * Retrieves a country by its code.
     *
     * @param code The country code.
     * @return Optional containing the CountryDto if found.
     */
    Optional<CountryDto> getCountryByCode(String code);

    /**
     * Retrieves all countries.
     *
     * @return List of all CountryDto objects.
     */
    List<CountryDto> getAllCountries();

    /**
     * Searches countries by keyword across nameAr, nameEn, and code fields.
     * Supports pagination and sorting.
     *
     * @param keyword The search keyword (can be null or empty for all countries).
     * @param pageable Pagination and sorting information.
     * @return Page of matching CountryDto objects.
     */
    Page<CountryDto> searchCountries(String keyword, Pageable pageable);

    /**
     * Checks if a country exists by code.
     *
     * @param code The country code.
     * @return true if country exists, false otherwise.
     */
    boolean existsByCode(String code);
}
