package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.*;
import org.maalischool.ms.accounting.exception.*;
import org.maalischool.ms.accounting.exception.InsufficientAmountException;
import org.maalischool.ms.accounting.exception.OverpaymentException;
import org.maalischool.ms.accounting.exception.ReceiptNotFoundException; // Add missing import
import org.maalischool.ms.accounting.model.*;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.maalischool.ms.accounting.repository.*;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.ReceiptService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.maalischool.ms.student.exception.StudentNotFoundException; // Assuming exists
import org.maalischool.ms.student.model.Student; // Correct model
import org.maalischool.ms.student.repository.StudentRepository; // Correct repository
import org.maalischool.ms.student.dto.SimpleStudentDto;
import org.maalischool.ms.schoolmanagement.dto.SimpleAcademicYearDto;
import org.maalischool.ms.schoolmanagement.model.AcademicYear; // Import SimpleStudentDto
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList; // Add missing import
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ReceiptServiceImpl implements ReceiptService {

    private static final Logger log = LoggerFactory.getLogger(ReceiptServiceImpl.class);

    private final ReceiptRepository receiptRepository;
    private final StudentFeeRepository studentFeeRepository;
    private final PaymentMethodRepository paymentMethodRepository;
    private final StudentRepository studentRepository; // Correct repository
    private final PaymentRepository paymentRepository; // Use PaymentRepository
    private final AccountingAuditLogService auditLogService;
    // private final ReceiptNumberGenerator receiptNumberGenerator; // Inject a bean
    // to generate unique receipt numbers

    @Override
    @Transactional
    public ReceiptDto createReceipt(CreateReceiptRequest request) {
        log.info("Creating new Receipt: StudentID={}, Amount={}, Date={}, MethodID={}",
                request.getStudentId(), request.getTotalAmountReceived(), request.getReceiptDate(),
                request.getPaymentMethodId()); // Use getTotalAmountReceived

        // --- Validation ---
        Student student = studentRepository.findById(request.getStudentId())
                .orElseThrow(() -> new StudentNotFoundException(request.getStudentId()));
        PaymentMethod paymentMethod = paymentMethodRepository.findById(request.getPaymentMethodId())
                .orElseThrow(() -> new PaymentMethodNotFoundException(request.getPaymentMethodId()));

        if (request.getTotalAmountReceived().compareTo(BigDecimal.ZERO) <= 0) { // Use getTotalAmountReceived
            throw new IllegalArgumentException("Receipt amount must be positive.");
        }

        // --- Receipt Creation ---
        Receipt receipt = Receipt.builder()
                .receiptNumber(generateReceiptNumber()) // Use a generator
                .receiptDate(request.getReceiptDate())
                // Remove studentId field as Receipt model uses Student object
                .student(student) // Set the Student object
                .totalAmountReceived(request.getTotalAmountReceived()) // Use totalAmountReceived
                .paymentMethod(paymentMethod)
                .referenceNumber(request.getReferenceNumber()) // Use correct builder method 'referenceNumber'
                .notes(request.getNotes())
                // Remove .cancelled(false) as the field doesn't exist on the model
                .build();

        // --- Payment Creation ---
        BigDecimal totalPaidViaThisReceipt = BigDecimal.ZERO;
        List<Payment> payments = new ArrayList<>(); // Use List from Receipt model

        if (!CollectionUtils.isEmpty(request.getPayments())) { // Use renamed field
            for (CreatePaymentDetailRequest paymentDetail : request.getPayments()) { // Use new DTO
                if (paymentDetail.getAllocatedAmount().compareTo(BigDecimal.ZERO) <= 0) {
                    log.warn("Skipping payment detail with non-positive amount: {}",
                            paymentDetail.getAllocatedAmount());
                }

                StudentFee studentFee = studentFeeRepository.findById(paymentDetail.getStudentFeeId())
                        .orElseThrow(() -> new StudentFeeNotFoundException(paymentDetail.getStudentFeeId()));

                // Ensure the fee belongs to the correct student? (Optional check)
                // Get the student ID from the associated Student object
                UUID feeStudentId = (studentFee.getStudent() != null) ? studentFee.getStudent().getId() : null;
                if (feeStudentId == null || !feeStudentId.equals(request.getStudentId())) {
                    log.error("Payment error: StudentFee ID {} does not belong to Student ID {}",
                            paymentDetail.getStudentFeeId(), request.getStudentId());
                    throw new IllegalArgumentException(
                            "Payment detail references a fee not belonging to the specified student.");
                }

                // Check for overpayment on this specific fee
                BigDecimal amountCurrentlyDue = studentFee.getAmountDue().subtract(studentFee.getAmountPaid()); // Calculate
                                                                                                                // remaining
                                                                                                                // due
                if (paymentDetail.getAllocatedAmount().compareTo(amountCurrentlyDue) > 0) {
                    log.error("Overpayment attempt: Paying {} to StudentFee ID {} which has {} currently due.",
                            paymentDetail.getAllocatedAmount(), studentFee.getId(), amountCurrentlyDue);
                    throw new OverpaymentException(studentFee.getId(), amountCurrentlyDue,
                            paymentDetail.getAllocatedAmount());
                }

                // Create Payment entity
                Payment payment = Payment.builder()
                        .receipt(receipt) // Link back to the receipt
                        .studentFee(studentFee)
                        .paymentDate(receipt.getReceiptDate()) // Use receipt date for payment date
                        .amount(paymentDetail.getAllocatedAmount())
                        // Add other fields if Payment model has them (e.g., notes)
                        .build();
                payments.add(payment); // Add to the list
                totalPaidViaThisReceipt = totalPaidViaThisReceipt.add(paymentDetail.getAllocatedAmount());

                // Update the student fee's amount paid (will trigger status update via
                // @PreUpdate)
                studentFee.setAmountPaid(studentFee.getAmountPaid().add(paymentDetail.getAllocatedAmount()));
                // No need to save studentFee here if cascade is not used, but saving is safer
                studentFeeRepository.save(studentFee); // Ensure changes are persisted before audit/return
            }
        }

        // Validate total paid amount against receipt amount
        // Allow totalPaid <= receipt.totalAmountReceived
        if (totalPaidViaThisReceipt.compareTo(receipt.getTotalAmountReceived()) > 0) {
            log.error("Payment error: Total paid amount {} exceeds receipt amount {}", totalPaidViaThisReceipt,
                    receipt.getTotalAmountReceived());
            // This should ideally be caught by OverpaymentException earlier, but
            // double-check
            throw new InsufficientAmountException("Total paid amount cannot exceed the receipt amount.");
        }
        // Handle unallocated amount (receipt.getTotalAmountReceived() -
        // totalPaidViaThisReceipt) if necessary.

        receipt.setPayments(payments); // Set the payments on the receipt

        // --- Save Receipt and Payments ---
        // Saving the receipt should cascade save the payments if configured with
        // CascadeType.ALL or PERSIST.
        Receipt savedReceipt = receiptRepository.save(receipt);
        log.info("Successfully created Receipt ID: {}, Number: {}", savedReceipt.getId(),
                savedReceipt.getReceiptNumber());

        // --- Audit Log ---
        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE, // Use generic CREATE type
                AccountingConstants.ENTITY_RECEIPT,
                savedReceipt.getId(),
                String.format("Created Receipt #: %s, Student ID: %s, Amount: %s, Method: %s, Payments: %d",
                        savedReceipt.getReceiptNumber(), student.getId(), savedReceipt.getTotalAmountReceived(), // Use
                                                                                                                 // correct
                                                                                                                 // amount
                                                                                                                 // field
                        paymentMethod.getNameEn(), payments.size()) // Use English name, correct list size
        );

        return mapToDto(savedReceipt);
    }

    @Override
    @Transactional(readOnly = true)
    public ReceiptDto getReceiptById(UUID id) {
        log.debug("Fetching Receipt by ID: {}", id);
        Receipt receipt = findReceiptOrThrow(id);
        return mapToDto(receipt);
    }

    @Override
    @Transactional(readOnly = true)
    public ReceiptDto getReceiptByNumber(String receiptNumber) {
        log.debug("Fetching Receipt by Number: {}", receiptNumber);
        Receipt receipt = receiptRepository.findByReceiptNumber(receiptNumber)
                .orElseThrow(() -> new ReceiptNotFoundException(receiptNumber));
        return mapToDto(receipt);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ReceiptDto> getAllReceipts(Pageable pageable) {
        log.debug("Fetching Receipts page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return receiptRepository.findAll(pageable).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ReceiptDto> findReceiptsByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        log.debug("Fetching Receipts between {} and {}", startDate, endDate);
        return receiptRepository.findByReceiptDateBetweenOrderByReceiptDateDesc(startDate, endDate, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ReceiptDto> findReceiptsByStudent(UUID studentId, Pageable pageable) {
        log.debug("Fetching Receipts for Student ID: {}", studentId);
        // Optional: Check if student exists
        if (!studentRepository.existsById(studentId)) {
            throw new StudentNotFoundException(studentId);
        }
        return receiptRepository.findByStudentIdOrderByReceiptDateDesc(studentId, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ReceiptDto> findReceiptsByPaymentMethod(UUID paymentMethodId, Pageable pageable) {
        log.debug("Fetching Receipts for Payment Method ID: {}", paymentMethodId);
        // Optional: Check if payment method exists
        if (!paymentMethodRepository.existsById(paymentMethodId)) {
            throw new PaymentMethodNotFoundException(paymentMethodId);
        }
        return receiptRepository.findByPaymentMethodIdOrderByReceiptDateDesc(paymentMethodId, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional
    public void cancelReceipt(UUID id, String reason) {
        log.warn("Attempting to cancel Receipt with ID: {}", id);
        Receipt receipt = findReceiptOrThrow(id);

        // Check if cancellation date is already set instead of a boolean flag
        if (receipt.getCancellationDate() != null) {
            log.warn("Receipt {} was already cancelled on {}.", id, receipt.getCancellationDate());
            throw new IllegalStateException("Receipt is already cancelled.");
        }

        // --- Reversal Logic ---
        // 1. Reverse payments: Subtract paid amounts from corresponding StudentFees
        log.info("Reversing payments for cancelled Receipt ID: {}", id);
        List<Payment> paymentsToReverse = new ArrayList<>(receipt.getPayments()); // Copy to avoid concurrent
                                                                                  // modification issues if deleting
        for (Payment payment : paymentsToReverse) {
            StudentFee studentFee = payment.getStudentFee();
            BigDecimal reversalAmount = payment.getAmount();

            // Subtract the amount back from the fee's paid amount
            studentFee.setAmountPaid(studentFee.getAmountPaid().subtract(reversalAmount));
            // The @PreUpdate on StudentFee should recalculate status (e.g., back to PARTIAL
            // or UNPAID)
            studentFeeRepository.save(studentFee); // Save the updated fee status/amount

            log.debug("Reversed payment of {} from StudentFee ID {}", reversalAmount, studentFee.getId());

            // Optionally delete the Payment record itself, or mark it as reversed if model
            // supports it
            // receipt.removePayment(payment); // If using cascade remove
            paymentRepository.delete(payment); // Explicit deletion
        }

        // 2. Mark the receipt as cancelled
        // receipt.setCancelled(true); // Assuming Receipt model has a cancelled
        // flag/status
        receipt.setCancellationReason(reason);
        receipt.setCancellationDate(LocalDate.now()); // Or Instant.now() if using Instant
        // Optionally store the user who cancelled it
        // receipt.setCancelledByUserId(getCurrentUserId());

        Receipt cancelledReceipt = receiptRepository.save(receipt);
        log.warn("Successfully cancelled Receipt ID: {}, Number: {}", cancelledReceipt.getId(),
                cancelledReceipt.getReceiptNumber());

        // --- Audit Log ---
        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE, // Use UPDATE for cancellation status change
                AccountingConstants.ENTITY_RECEIPT,
                cancelledReceipt.getId(),
                String.format("Cancelled Receipt #: %s. Reason: %s", cancelledReceipt.getReceiptNumber(), reason));

        // TODO: Consider creating reversal Journal Entries if required by accounting
        // principles.
        // This would involve debiting the cash/bank account and crediting accounts
        // receivable/student accounts.
        log.warn("Journal entry reversal for cancelled receipt {} is not implemented.", id);
    }

    // --- Helper Methods ---

    private Receipt findReceiptOrThrow(UUID id) {
        // Fetch with payments to avoid lazy loading issues if needed later
        // Ensure ReceiptRepository has a method like findByIdWithPayments or rely on
        // default fetch type
        return receiptRepository.findById(id) // Assuming default fetch or adjust query
                .orElseThrow(() -> new ReceiptNotFoundException(id));
    }

    private String generateReceiptNumber() {
        // Implement a robust receipt number generation strategy
        // e.g., using a database sequence, a dedicated service, or timestamp + random
        // part
        // Placeholder implementation:
        return "RCPT-" + System.currentTimeMillis();
        // Replace with: return receiptNumberGenerator.getNextReceiptNumber();
    }

    private ReceiptDto mapToDto(Receipt receipt) {
        if (receipt == null)
            return null;

        // Map Payments to PaymentDto
        List<PaymentDto> paymentDtos = receipt.getPayments().stream()
                .map(this::mapPaymentToDto) // Use new helper method
                .collect(Collectors.toList());

        // Map Student to SimpleStudentDto (assuming a helper or mapper exists)
        SimpleStudentDto simpleStudentDto = mapStudentToSimpleDto(receipt.getStudent());

        return ReceiptDto.builder()
                .id(receipt.getId())
                .receiptNumber(receipt.getReceiptNumber())
                .receiptDate(receipt.getReceiptDate())
                .student(simpleStudentDto) // Use student DTO
                .totalAmountReceived(receipt.getTotalAmountReceived()) // Use correct field name
                .paymentMethod(mapPaymentMethodToSimpleDto(receipt.getPaymentMethod()))
                .referenceNumber(receipt.getReferenceNumber()) // Map the referenceNumber
                .notes(receipt.getNotes())
                .payments(paymentDtos) // Use list of PaymentDto
                .cancelled(receipt.getCancellationDate() != null) // Determine cancelled status from date
                .cancellationReason(receipt.getCancellationReason()) // Map reason
                .cancellationDate(receipt.getCancellationDate()) // Map date
                .createdDate(receipt.getCreatedDate())
                // .lastModifiedDate(receipt.getLastModifiedDate()) // Add if Receipt model has
                // it
                .build();
    }

    // New helper method to map Payment to PaymentDto
    private PaymentDto mapPaymentToDto(Payment payment) {
        if (payment == null)
            return null;
        return PaymentDto.builder()
                .id(payment.getId())
                .receiptId(payment.getReceipt() != null ? payment.getReceipt().getId() : null)
                .studentFee(mapStudentFeeToSimpleDto(payment.getStudentFee()))
                .paymentDate(payment.getPaymentDate())
                .amount(payment.getAmount())
                .createdDate(payment.getCreatedDate())
                // .lastModifiedDate(payment.getLastModifiedDate()) // If applicable
                .build();
    }

    // Placeholder for mapping Student to SimpleStudentDto
    private SimpleStudentDto mapStudentToSimpleDto(Student student) {
        if (student == null)
            return null;
        // Assuming SimpleStudentDto exists and has appropriate fields
        return SimpleStudentDto.builder()
                .id(student.getId())
                // Add other relevant fields like name, studentIdNumber etc. from Student model
                // .firstName(student.getUserAccount() != null ?
                // student.getUserAccount().getFirstName() : null)
                // .lastName(student.getUserAccount() != null ?
                // student.getUserAccount().getLastName() : null)
                // .studentIdNumber(student.getStudentIdNumber())
                .build();
    }

    private SimpleStudentFeeDto mapStudentFeeToSimpleDto(StudentFee studentFee) {
        if (studentFee == null)
            return null;
        // Map the nested Fee to SimpleFeeDto first
        SimpleFeeDto simpleFeeDto = SimpleFeeDto.builder()
                .id(studentFee.getFee().getId())
                .nameEn(studentFee.getFee().getNameEn())
                .nameAr(studentFee.getFee().getNameAr())
                .amount(studentFee.getFee().getAmount()) // Include amount from Fee
                .academicYear(mapAcademicYearToSimpleDto(studentFee.getFee().getAcademicYear()))
                .dueDate(studentFee.getFee().getDueDate()) // Include dueDate from Fee
                .build();

        return SimpleStudentFeeDto.builder()
                .id(studentFee.getId())
                .fee(simpleFeeDto) // Set the nested SimpleFeeDto
                // Remove originalAmount as it's not in SimpleStudentFeeDto
                .amountDue(studentFee.getAmountDue())
                .status(studentFee.getStatus())
                .build();
    }

    // Removed findStudentFees method as it belongs in StudentFeeServiceImpl

    private SimplePaymentMethodDto mapPaymentMethodToSimpleDto(PaymentMethod method) {
        if (method == null)
            return null;
        return SimplePaymentMethodDto.builder()
                .id(method.getId())
                .nameEn(method.getNameEn()) // Use English name
                .nameAr(method.getNameAr()) // Use Arabic name
                .type(method.getType()) // Map type
                .build();
    }

    private SimpleAcademicYearDto mapAcademicYearToSimpleDto(AcademicYear academicYear) {
        if (academicYear == null)
            return null;
        return SimpleAcademicYearDto.builder()
                .id(academicYear.getId())
                .name(academicYear.getName())
                .startDate(academicYear.getStartDate())
                .endDate(academicYear.getEndDate())
                .active(academicYear.isActive())
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("00000000-0000-0000-0000-000000000000");
    }
}
