package org.maalischool.ms.accounting.mapper;

import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;

/**
 * Mapper for converting between ChartOfAccount entity and its DTOs.
 */
@Mapper(componentModel = "spring") // Integrate with Spring DI
public interface ChartOfAccountMapper {

    /**
     * Converts a ChartOfAccount entity to a SimpleChartOfAccountDto.
     * MapStruct automatically maps fields with the same name (id, nameEn, nameAr, accountNumber).
     *
     * @param chartOfAccount The ChartOfAccount entity.
     * @return The corresponding SimpleChartOfAccountDto.
     */
    SimpleChartOfAccountDto toSimpleDto(ChartOfAccount chartOfAccount);

    /**
     * Converts a list of ChartOfAccount entities to a list of SimpleChartOfAccountDto.
     *
     * @param chartOfAccounts The list of ChartOfAccount entities.
     * @return The list of corresponding SimpleChartOfAccountDto.
     */
    List<SimpleChartOfAccountDto> toSimpleDtoList(List<ChartOfAccount> chartOfAccounts);

    // Add other mapping methods if needed, e.g., for full ChartOfAccountDto
    // ChartOfAccountDto toDto(ChartOfAccount chartOfAccount);
    // ChartOfAccount toEntity(CreateChartOfAccountRequest request);
    // void updateEntityFromDto(UpdateChartOfAccountRequest request, @MappingTarget ChartOfAccount entity);
}
