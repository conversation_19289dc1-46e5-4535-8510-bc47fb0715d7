package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class FeeNotFoundException extends RuntimeException {

    public FeeNotFoundException(UUID id) {
        super("Fee not found with ID: " + id);
    }

     public FeeNotFoundException(String name, String academicYear, UUID categoryId) {
        super(String.format("Fee not found with Name: %s, Academic Year: %s, Category ID: %s", name, academicYear, categoryId));
    }
}
