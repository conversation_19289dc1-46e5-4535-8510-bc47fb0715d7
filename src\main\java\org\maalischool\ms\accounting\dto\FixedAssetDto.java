package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import org.maalischool.ms.accounting.model.enums.DepreciationMethod;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class FixedAssetDto {
    private UUID id;
    private String assetCode;
    private String nameEn;
    private String nameAr;
    private String descriptionEn;
    private String descriptionAr;
    private LocalDate acquisitionDate;
    private BigDecimal acquisitionCost;
    private SimpleChartOfAccountDto assetAccount;
    private SimpleChartOfAccountDto accumulatedDepreciationAccount;
    private SimpleChartOfAccountDto depreciationExpenseAccount;
    private Integer usefulLifeYears;
    private BigDecimal salvageValue;
    // private String depreciationMethod; // Use Enum instead
    private DepreciationMethod depreciationMethod;
    private String location;
//    private String serialNumber;
    private AssetStatus status;
//    private LocalDate disposalDate;
//    private BigDecimal disposalValue;
    // Calculated fields (could be added by service)
    // private BigDecimal currentBookValue;
    // private BigDecimal accumulatedDepreciationToDate;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
