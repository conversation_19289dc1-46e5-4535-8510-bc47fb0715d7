package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreatePaymentMethodRequest;
import org.maalischool.ms.accounting.dto.PaymentMethodDto;
import org.maalischool.ms.accounting.dto.SimplePaymentMethodDto; // Assuming this exists
import org.maalischool.ms.accounting.dto.UpdatePaymentMethodRequest;
import org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException;
import org.maalischool.ms.accounting.model.PaymentMethod;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.ExpenseRepository; // To check usage
import org.maalischool.ms.accounting.repository.PaymentMethodRepository;
import org.maalischool.ms.accounting.repository.ReceiptRepository; // To check usage
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.PaymentMethodService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification; // Import Specification
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.UUID;

@Service
@RequiredArgsConstructor
public class PaymentMethodServiceImpl implements PaymentMethodService {

    private static final Logger log = LoggerFactory.getLogger(PaymentMethodServiceImpl.class);
    private final PaymentMethodRepository paymentMethodRepository;
    private final ReceiptRepository receiptRepository; // Assuming exists
    private final ExpenseRepository expenseRepository; // Assuming exists
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public PaymentMethodDto createPaymentMethod(CreatePaymentMethodRequest request) {
        log.info("Creating new Payment Method with nameEn: {}", request.getNameEn()); // Log English name
        // Check uniqueness based on English name
        paymentMethodRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(pm -> {
            throw new IllegalArgumentException("Payment Method with English name '" + request.getNameEn() + "' already exists.");
        });
        // TODO: Consider adding a similar check for nameAr if it also needs to be unique

        PaymentMethod paymentMethod = PaymentMethod.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn()) // Map description from request
                .descriptionAr(request.getDescriptionAr())   // Map description from request
                .type(request.getType()) // Map type from request
                .active(true) // Default to active
                .build();

        PaymentMethod savedMethod = paymentMethodRepository.save(paymentMethod);
        log.info("Successfully created Payment Method with ID: {}", savedMethod.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_PAYMENT_METHOD,
                savedMethod.getId(),
                "Created Payment Method: " + savedMethod.getNameEn() // Log English name
        );

        return mapToDto(savedMethod);
    }

    @Override
    @Transactional(readOnly = true)
    public PaymentMethodDto getPaymentMethodById(UUID id) {
        log.debug("Fetching Payment Method by ID: {}", id);
        PaymentMethod method = findMethodOrThrow(id);
        return mapToDto(method);
    }

    @Override
    @Transactional(readOnly = true)
    public PaymentMethodDto getPaymentMethodByName(String nameEn) { // Parameter name changed for clarity
        log.debug("Fetching Payment Method by English name: {}", nameEn);
        PaymentMethod method = paymentMethodRepository.findByNameEnIgnoreCase(nameEn) // Use findByNameEnIgnoreCase
                .orElseThrow(() -> new PaymentMethodNotFoundException(nameEn));
        return mapToDto(method);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentMethodDto> getAllActivePaymentMethods(String search, Pageable pageable) {
        log.debug("Fetching active Payment Methods with search: '{}', page: {}, size: {}, sort: {}",
                 search, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        Specification<PaymentMethod> spec = PaymentMethodRepository.createSpecification(search, true); // Use static helper
        Page<PaymentMethod> activeMethodsPage = paymentMethodRepository.findAll(spec, pageable);
        return activeMethodsPage.map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PaymentMethodDto> getAllPaymentMethods(String search, Pageable pageable) {
        log.debug("Fetching all Payment Methods with search: '{}', page: {}, size: {}, sort: {}",
                 search, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        Specification<PaymentMethod> spec = PaymentMethodRepository.createSpecification(search, null); // Use static helper, active=null
        return paymentMethodRepository.findAll(spec, pageable).map(this::mapToDto);
    }

    @Override
    @Transactional
    public PaymentMethodDto updatePaymentMethod(UUID id, UpdatePaymentMethodRequest request) {
        log.info("Updating Payment Method with ID: {}", id);
        PaymentMethod method = findMethodOrThrow(id);

        // Check if English name is being changed and if the new name already exists
        if (!method.getNameEn().equalsIgnoreCase(request.getNameEn())) {
            paymentMethodRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(pm -> { // Check by English name
                 if (!pm.getId().equals(id)) { // Ensure it's not the same method
                    throw new IllegalArgumentException("Payment Method with English name '" + request.getNameEn() + "' already exists.");
                 }
            });
        }
        // TODO: Consider adding a similar check for nameAr if it also needs to be unique

        String oldDetails = String.format("NameEn: %s, DescEn: %s, Active: %s", method.getNameEn(), method.getDescriptionEn(), method.isActive()); // Log English fields

        method.setNameEn(request.getNameEn());
        method.setNameAr(request.getNameAr());
        method.setDescriptionEn(request.getDescriptionEn()); // Map description from request
        method.setDescriptionAr(request.getDescriptionAr());   // Map description from request
        method.setType(request.getType()); // Map type from request
        method.setActive(request.getActive()); // Allow updating active status here

        PaymentMethod updatedMethod = paymentMethodRepository.save(method);
        log.info("Successfully updated Payment Method with ID: {}", updatedMethod.getId());

        String newDetails = String.format("NameEn: %s, DescEn: %s, Active: %s", updatedMethod.getNameEn(), updatedMethod.getDescriptionEn(), updatedMethod.isActive()); // Log English fields

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_PAYMENT_METHOD,
                updatedMethod.getId(),
                "Updated Payment Method. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedMethod);
    }

    @Override
    @Transactional
    public void deletePaymentMethod(UUID id) {
        log.warn("Attempting to delete Payment Method with ID: {}", id);
        PaymentMethod method = findMethodOrThrow(id);

        // Check if the payment method has been used in Receipts or Expenses
        // boolean usedInReceipts = receiptRepository.existsByPaymentMethodId(id); // Assuming method exists
        // boolean usedInExpenses = expenseRepository.existsByPaymentMethodId(id); // Assuming method exists
        // if (usedInReceipts || usedInExpenses) {
        //     log.error("Cannot delete Payment Method {} because it has been used in transactions.", id);
        //     throw new IllegalStateException("Cannot delete Payment Method that has been used in transactions. Consider deactivating it instead.");
        // }
        log.warn("Deletion check for associated Receipts/Expenses for PaymentMethod {} is not fully implemented.", id);


        paymentMethodRepository.delete(method);
        log.warn("Successfully deleted Payment Method with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_PAYMENT_METHOD,
                id,
                "Deleted Payment Method: " + method.getNameEn() // Log English name
        );
    }

     @Override
    @Transactional
    public PaymentMethodDto activatePaymentMethod(UUID id) {
        log.info("Activating Payment Method with ID: {}", id);
        return updatePaymentMethodStatus(id, true);
    }

    @Override
    @Transactional
    public PaymentMethodDto deactivatePaymentMethod(UUID id) {
        log.info("Deactivating Payment Method with ID: {}", id);
        return updatePaymentMethodStatus(id, false);
    }

     private PaymentMethodDto updatePaymentMethodStatus(UUID id, boolean active) {
        PaymentMethod method = findMethodOrThrow(id);

        if (method.isActive() == active) {
            log.warn("Payment Method {} already has active status {}", id, active);
            return mapToDto(method);
        }

        boolean oldStatus = method.isActive();
        method.setActive(active);
        PaymentMethod updatedMethod = paymentMethodRepository.save(method);
        log.info("Successfully set active status to {} for Payment Method ID: {}", active, id);

        auditLogService.logAction(
                getCurrentUserId(),
                active ? AccountingActionType.ACTIVATE : AccountingActionType.DEACTIVATE,
                AccountingConstants.ENTITY_PAYMENT_METHOD,
                updatedMethod.getId(),
                String.format("Changed active status from %s to %s for Payment Method %s", oldStatus, active, updatedMethod.getNameEn()) // Log English name
        );

        return mapToDto(updatedMethod);
    }


    private PaymentMethod findMethodOrThrow(UUID id) {
        return paymentMethodRepository.findById(id)
                .orElseThrow(() -> new PaymentMethodNotFoundException(id));
    }

    // --- Helper Methods ---

    private PaymentMethodDto mapToDto(PaymentMethod method) {
        if (method == null) return null;
        return PaymentMethodDto.builder()
                .id(method.getId())
                .nameEn(method.getNameEn())
                .nameAr(method.getNameAr())
                .descriptionEn(method.getDescriptionEn())
                .descriptionAr(method.getDescriptionAr())
                .type(method.getType()) // Map type
                .active(method.isActive())
                .createdDate(method.getCreatedDate())
                .lastModifiedDate(method.getLastModifiedDate())
                .build();
    }

     private SimplePaymentMethodDto mapToSimpleDto(PaymentMethod method) {
        if (method == null) return null;
        return SimplePaymentMethodDto.builder()
                .id(method.getId())
                .nameEn(method.getNameEn()) // Use English name
                .nameAr(method.getNameAr())   // Use Arabic name
                .type(method.getType()) // Map type
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("00000000-0000-0000-0000-000000000000");
    }
}
