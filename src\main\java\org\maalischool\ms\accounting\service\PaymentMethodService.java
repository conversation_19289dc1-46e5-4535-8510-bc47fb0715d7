package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreatePaymentMethodRequest;
import org.maalischool.ms.accounting.dto.PaymentMethodDto;
import org.maalischool.ms.accounting.dto.UpdatePaymentMethodRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.UUID;

/**
 * Service interface for managing Payment Methods.
 */
public interface PaymentMethodService {

    /**
     * Creates a new payment method.
     *
     * @param request the request object containing payment method details.
     * @return the created payment method DTO.
     */
    PaymentMethodDto createPaymentMethod(CreatePaymentMethodRequest request);

    /**
     * Retrieves a payment method by its ID.
     *
     * @param id the UUID of the payment method.
     * @return the payment method DTO.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    PaymentMethodDto getPaymentMethodById(UUID id);

    /**
     * Retrieves a payment method by its name (case-insensitive).
     *
     * @param name the name of the payment method.
     * @return the payment method DTO.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    PaymentMethodDto getPaymentMethodByName(String name);

    /**
     * Retrieves all active payment methods, ordered by name.
     * Retrieves all active payment methods with pagination.
     *
     * @param search   Optional search term for nameEn or nameAr (case-insensitive, partial match).
     * @param pageable pagination information.
     * @return a page of active payment method DTOs.
     */
    Page<PaymentMethodDto> getAllActivePaymentMethods(String search, Pageable pageable); // Added search param

    /**
     * Retrieves all payment methods with pagination and optional search.
     *
     * @param search   Optional search term for nameEn or nameAr (case-insensitive, partial match).
     * @param pageable pagination information.
     * @return a page of payment method DTOs.
     */
    Page<PaymentMethodDto> getAllPaymentMethods(String search, Pageable pageable); // Added search param

    /**
     * Updates an existing payment method.
     *
     * @param id      the UUID of the payment method to update.
     * @param request the request object containing updated payment method details.
     * @return the updated payment method DTO.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    PaymentMethodDto updatePaymentMethod(UUID id, UpdatePaymentMethodRequest request);

    /**
     * Deletes a payment method by its ID.
     * Consider implications if this method has been used in transactions (receipts, expenses).
     * A soft delete (setting active=false) might be preferable.
     *
     * @param id the UUID of the payment method to delete.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    void deletePaymentMethod(UUID id); // Consider soft delete via update instead

     /**
     * Activates a payment method.
     *
     * @param id the UUID of the payment method to activate.
     * @return the activated payment method DTO.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    PaymentMethodDto activatePaymentMethod(UUID id);

    /**
     * Deactivates a payment method.
     *
     * @param id the UUID of the payment method to deactivate.
     * @return the deactivated payment method DTO.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method is not found.
     */
    PaymentMethodDto deactivatePaymentMethod(UUID id);
}
