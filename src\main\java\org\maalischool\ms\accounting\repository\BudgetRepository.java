package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Budget;
import org.springframework.data.domain.Page; // Import Page
import org.springframework.data.domain.Pageable; // Import Pageable
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BudgetRepository extends JpaRepository<Budget, UUID> {

    // Corrected OrderBy: Use NameEn for ChartOfAccount and ExpenseCategory
    List<Budget> findByAcademicYearAndPeriodOrderByChartOfAccountNameEnAscExpenseCategoryNameEnAsc(String academicYear, String period);

    // Modify this method to accept Pageable and return Page<Budget>
    // Corrected OrderBy: Use NameEn for ChartOfAccount and ExpenseCategory
    Page<Budget> findByAcademicYearOrderByPeriodAscChartOfAccountNameEnAscExpenseCategoryNameEnAsc(String academicYear, Pageable pageable);

    Optional<Budget> findByAcademicYearAndPeriodAndChartOfAccountId(String academicYear, String period, UUID chartOfAccountId);

    Optional<Budget> findByAcademicYearAndPeriodAndExpenseCategoryId(String academicYear, String period, UUID expenseCategoryId);
}
