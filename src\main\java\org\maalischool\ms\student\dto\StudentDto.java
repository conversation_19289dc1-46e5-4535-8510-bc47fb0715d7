package org.maalischool.ms.student.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.maalischool.ms.auth.dto.UserDto; // Include basic user info
import org.maalischool.ms.student.model.Gender; // Import Gender enum
import org.maalischool.ms.student.model.GuardianType; // Import GuardianType
import org.maalischool.ms.student.model.IdType; // Import IdType

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO for Student.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StudentDto {
    private UUID id;
    private UserDto userAccount; // Include basic user details
    private String admissionNumber;
    private LocalDate dateOfBirth;
    private Gender gender;
    private String address;
    private String nationalId; // Added field
    private IdType idType; // Added field
    private SimpleGuardianDto guardian; // Renamed field, use SimpleGuardianDto
    private GuardianType guardianType; // Added field
    private SimpleCountryDto nationality; // Added nationality field
    // Add fields for currentSection, grades, files later
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
