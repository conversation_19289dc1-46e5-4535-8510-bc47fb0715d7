package org.maalischool.ms.auth.service;

import org.maalischool.ms.auth.dto.RegisterRequest;
import org.maalischool.ms.auth.dto.ResetPasswordRequest;
import org.maalischool.ms.auth.dto.UserDto; // Import UserDto
import org.maalischool.ms.auth.model.User;
import org.springframework.data.domain.Page; // Import Page
import org.springframework.data.domain.Pageable; // Import Pageable

import java.util.Optional;
import java.util.UUID;

public interface UserService {

    /**
     * Registers a new user.
     * Handles password encoding and potentially sending activation email.
     * Allows optional role assignment during registration.
     *
     * @param registerRequest DTO containing registration details (including optional roleName).
     * @return The newly created User entity (potentially before activation).
     * @throws EmailAlreadyExistsException if email already exists.
     * @throws RoleNotFoundException if the provided roleName is invalid.
     */
    User registerUser(RegisterRequest registerRequest);

    /**
     * Activates a user account using an activation token.
     *
     * @param token The activation token.
     * @return An Optional containing the activated User if the token is valid and not expired.
     * @throws InvalidTokenException if token is not found.
     * @throws ExpiredTokenException if token is expired.
     */
    Optional<User> activateUser(String token);

    /**
     * Finds a user by their email address.
     *
     * @param email The email address.
     * @return An Optional containing the User if found, otherwise empty.
     */
    Optional<User> findByEmail(String email);

    /**
     * Finds a user by their ID.
     *
     * @param id The UUID of the user.
     * @return An Optional containing the User if found, otherwise empty.
     */
    Optional<User> findById(UUID id);

    /**
     * Initiates the password reset process for a user with the given email.
     * Generates a reset token and potentially sends a password reset email.
     * Does not throw an error if user is not found to prevent email enumeration.
     *
     * @param email The email address of the user requesting password reset.
     */
    void initiatePasswordReset(String email);

    /**
     * Completes the password reset process using a reset token and new password.
     *
     * @param resetPasswordRequest DTO containing the reset token and new password.
     * @return true if the password reset was successful.
     * @throws InvalidTokenException if token is not found.
     * @throws ExpiredTokenException if token is expired.
     */
    boolean completePasswordReset(ResetPasswordRequest resetPasswordRequest);

    /**
     * Checks if a user exists with the given email.
     *
     * @param email The email address to check.
     * @return true if a user exists, false otherwise.
     */
    boolean existsByEmail(String email);

    /**
     * Assigns a role to a user. Adds the role to the user's existing roles.
     *
     * @param userId The UUID of the user.
     * @param roleName The name of the role to assign.
     * @return The updated User entity.
     * @throws UserNotFoundException if user is not found.
     * @throws RoleNotFoundException if role is not found.
     */
    User assignRoleToUser(UUID userId, String roleName);

     /**
     * Removes a role from a user.
     *
     * @param userId The UUID of the user.
     * @param roleName The name of the role to remove.
     * @return The updated User entity.
     * @throws UserNotFoundException if user is not found.
     * @throws RoleNotFoundException if role is not found or user doesn't have it.
     */
    User removeRoleFromUser(UUID userId, String roleName);

    /**
     * Retrieves a paginated list of all users, optionally filtered by role name.
     *
     * @param roleName Optional role name to filter by. If null or blank, all users are returned.
     * @param pageable Pagination and sorting information.
     * @return A Page containing UserDto objects.
     */
    Page<UserDto> findAllUsers(String roleName, Pageable pageable); // Updated signature
}
