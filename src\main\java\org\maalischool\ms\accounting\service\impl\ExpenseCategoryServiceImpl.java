package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreateExpenseCategoryRequest;
import org.maalischool.ms.accounting.dto.ExpenseCategoryDto;
import org.maalischool.ms.accounting.dto.*; // Import all DTOs from package
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException; // Import exception
import org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException;
import org.maalischool.ms.accounting.model.ChartOfAccount; // Import ChartOfAccount
import org.maalischool.ms.accounting.model.ExpenseCategory;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.ExpenseCategoryRepository;
import org.maalischool.ms.accounting.repository.ExpenseRepository; // To check for associated expenses
import org.maalischool.ms.accounting.repository.specification.ExpenseCategorySpecification; // Import Specification
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.ExpenseCategoryService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification; // Import Specification
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository; // Inject repository
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ExpenseCategoryServiceImpl implements ExpenseCategoryService {

    private static final Logger log = LoggerFactory.getLogger(ExpenseCategoryServiceImpl.class);
    private final ExpenseCategoryRepository expenseCategoryRepository;
    private final ChartOfAccountRepository chartOfAccountRepository; // Inject ChartOfAccountRepository
    private final ExpenseRepository expenseRepository; // Inject repository to check usage
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public ExpenseCategoryDto createExpenseCategory(CreateExpenseCategoryRequest request) {
        log.info("Creating new Expense Category with nameEn: {}", request.getNameEn());
        // Check uniqueness based on English name
        expenseCategoryRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(ec -> {
            throw new IllegalArgumentException("Expense Category with English name '" + request.getNameEn() + "' already exists.");
        });
        // TODO: Add similar check for nameAr if needed

        // Fetch the mandatory expense account
        ChartOfAccount expenseAccount = chartOfAccountRepository.findById(request.getExpenseAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getExpenseAccountId()));

        // TODO: Fetch parent category if parentCategoryId is provided

        ExpenseCategory expenseCategory = ExpenseCategory.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .expenseAccount(expenseAccount) // Set mandatory account
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .build();

        ExpenseCategory savedCategory = expenseCategoryRepository.save(expenseCategory);
        log.info("Successfully created Expense Category with ID: {}", savedCategory.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_EXPENSE_CATEGORY,
                savedCategory.getId(),
                "Created Expense Category: " + savedCategory.getNameEn() // Log English name
        );

        return mapToDto(savedCategory);
    }

    @Override
    @Transactional(readOnly = true)
    public ExpenseCategoryDto getExpenseCategoryById(UUID id) {
        log.debug("Fetching Expense Category by ID: {}", id);
        ExpenseCategory category = findCategoryOrThrow(id);
        return mapToDto(category);
    }

    @Override
    @Transactional(readOnly = true)
    public ExpenseCategoryDto getExpenseCategoryByName(String nameEn) { // Parameter name changed for clarity
        log.debug("Fetching Expense Category by English name: {}", nameEn);
        ExpenseCategory category = expenseCategoryRepository.findByNameEnIgnoreCase(nameEn) // Use findByNameEnIgnoreCase
                .orElseThrow(() -> new ExpenseCategoryNotFoundException(nameEn));
        return mapToDto(category);
    }

    @Override
    @Transactional(readOnly = true)
    public List<ExpenseCategoryDto> getAllExpenseCategories() {
        log.debug("Fetching all Expense Categories");
        return expenseCategoryRepository.findAllByOrderByNameEnAsc() // Order by English name
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ExpenseCategoryDto> getAllExpenseCategories(String searchTerm, Pageable pageable) {
        log.debug("Fetching Expense Categories page: {}, size: {}, searchTerm: '{}'",
                  pageable.getPageNumber(), pageable.getPageSize(), searchTerm);

        // Create the specification based on the search term
        Specification<ExpenseCategory> spec = ExpenseCategorySpecification.filterBySearchTerm(searchTerm);

        // Fetch data using the specification and pageable
        Page<ExpenseCategory> categoryPage = expenseCategoryRepository.findAll(spec, pageable);

        log.debug("Found {} expense categories matching criteria.", categoryPage.getTotalElements());
        return categoryPage.map(this::mapToDto);
    }

    @Override
    @Transactional
    public ExpenseCategoryDto updateExpenseCategory(UUID id, UpdateExpenseCategoryRequest request) {
        log.info("Updating Expense Category with ID: {}", id);
        ExpenseCategory category = findCategoryOrThrow(id);

        // Check if English name is being changed and if the new name already exists
        if (!category.getNameEn().equalsIgnoreCase(request.getNameEn())) {
            expenseCategoryRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(ec -> {
                 if (!ec.getId().equals(id)) {
                    throw new IllegalArgumentException("Expense Category with English name '" + request.getNameEn() + "' already exists.");
                 }
            });
        }
        // TODO: Add similar check for nameAr if needed

        // Fetch and update the mandatory expense account
        ChartOfAccount newExpenseAccount = chartOfAccountRepository.findById(request.getExpenseAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getExpenseAccountId()));

        // TODO: Handle parent category update if parentCategoryId is provided

        String oldDetails = String.format("NameEn: %s, DescEn: %s, AccountId: %s",
                                          category.getNameEn(), category.getDescriptionEn(), category.getExpenseAccount().getId());

        category.setNameEn(request.getNameEn());
        category.setNameAr(request.getNameAr());
        category.setExpenseAccount(newExpenseAccount); // Update account
        category.setDescriptionEn(request.getDescriptionEn());
        category.setDescriptionAr(request.getDescriptionAr());

        ExpenseCategory updatedCategory = expenseCategoryRepository.save(category);
        log.info("Successfully updated Expense Category with ID: {}", updatedCategory.getId());

        String newDetails = String.format("NameEn: %s, DescEn: %s, AccountId: %s",
                                          updatedCategory.getNameEn(), updatedCategory.getDescriptionEn(), updatedCategory.getExpenseAccount().getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_EXPENSE_CATEGORY,
                updatedCategory.getId(),
                "Updated Expense Category. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedCategory);
    }

    @Override
    @Transactional
    public void deleteExpenseCategory(UUID id) {
        log.warn("Attempting to delete Expense Category with ID: {}", id);
        ExpenseCategory category = findCategoryOrThrow(id);

        // Check if any Expenses are associated with this category
        // boolean hasExpenses = expenseRepository.existsByExpenseCategoryId(id); // Assuming this method exists
        // if (hasExpenses) {
        //     throw new IllegalStateException("Cannot delete Expense Category with associated Expenses. ID: " + id);
        // }
        log.warn("Deletion check for associated Expenses in ExpenseCategory {} is not fully implemented.", id);


        expenseCategoryRepository.delete(category);
        log.warn("Successfully deleted Expense Category with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_EXPENSE_CATEGORY,
                id,
                "Deleted Expense Category: " + category.getNameEn() // Log English name
        );
    }

    private ExpenseCategory findCategoryOrThrow(UUID id) {
        return expenseCategoryRepository.findById(id)
                .orElseThrow(() -> new ExpenseCategoryNotFoundException(id));
    }

    // --- Helper Methods ---

    private ExpenseCategoryDto mapToDto(ExpenseCategory category) {
        if (category == null) return null;
        return ExpenseCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn())
                .nameAr(category.getNameAr())
                .descriptionEn(category.getDescriptionEn())
                .descriptionAr(category.getDescriptionAr())
                .expenseAccount(mapToSimpleCoADto(category.getExpenseAccount())) // Map the expense account
                // .parentCategory(mapToSimpleDto(category.getParentCategory())) // Map parent if implemented
                .createdDate(category.getCreatedDate())
                .lastModifiedDate(category.getLastModifiedDate())
                .build();
    }

    // Helper to map ChartOfAccount to SimpleChartOfAccountDto
    private SimpleChartOfAccountDto mapToSimpleCoADto(ChartOfAccount account) {
        if (account == null) return null;
        return SimpleChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn())
                .nameAr(account.getNameAr())
                .build();
    }

    private SimpleExpenseCategoryDto mapToSimpleDto(ExpenseCategory category) {
        if (category == null) return null;
        return SimpleExpenseCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn())
                .nameAr(category.getNameAr())
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("********-0000-0000-0000-************");
    }
}
