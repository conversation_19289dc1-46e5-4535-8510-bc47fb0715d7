package org.maalischool.ms.accounting.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreateFeeRequest;
import org.maalischool.ms.accounting.dto.FeeDto;
import org.maalischool.ms.accounting.dto.UpdateFeeRequest;
import org.maalischool.ms.accounting.service.FeeService;
import org.maalischool.ms.exception.ErrorResponse;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;
import java.util.List;
import java.util.UUID;

@RestController
@RequestMapping("/api/v1/accounting/fees")
@RequiredArgsConstructor
@Tag(name = "Fees", description = "APIs for managing Fees")
public class FeeController {

        private final FeeService feeService;

        @PostMapping
        @Operation(summary = "Create a new Fee", responses = {
                        @ApiResponse(responseCode = "201", description = "Fee created successfully"),
                        @ApiResponse(responseCode = "400", description = "Invalid input data", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Fee Category not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
                        @ApiResponse(responseCode = "409", description = "Fee already exists for this name/year/category", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<FeeDto> createFee(@Valid @RequestBody CreateFeeRequest request) {
                FeeDto createdFee = feeService.createFee(request);
                URI location = ServletUriComponentsBuilder.fromCurrentRequest()
                                .path("/{id}")
                                .buildAndExpand(createdFee.getId())
                                .toUri();
                return ResponseEntity.created(location).body(createdFee);
        }

        @GetMapping("/{id}")
        @Operation(summary = "Get Fee by ID", responses = {
                        @ApiResponse(responseCode = "200", description = "Fee found"),
                        @ApiResponse(responseCode = "404", description = "Fee not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<FeeDto> getFeeById(@Parameter(description = "ID of the fee") @PathVariable UUID id) {
                FeeDto fee = feeService.getFeeById(id);
                return ResponseEntity.ok(fee);
        }

        @GetMapping
        @Operation(summary = "Get all Fees (Paginated)", description = "Retrieves a paginated list of fees. Can filter by academic year and category.", responses = {
                        @ApiResponse(responseCode = "200", description = "Fees retrieved successfully")
        })
        public ResponseEntity<Page<FeeDto>> getAllFees(
                        @Parameter(description = "Filter by academic year (e.g., 2024-2025)") @RequestParam(required = false) String academicYear,
                        @Parameter(description = "Filter by fee category ID") @RequestParam(required = false) UUID categoryId,
                        @ParameterObject Pageable pageable) {
                // TODO: Enhance service/repository to handle filtering
                Page<FeeDto> fees = feeService.getAllFees(pageable); // Assuming basic pagination for now
                return ResponseEntity.ok(fees);
        }

        @GetMapping("/applicable")
        @Operation(summary = "Find applicable Fees for a student context", description = "Retrieves fees applicable based on academic year, grade, stage, and branch.", responses = {
                        @ApiResponse(responseCode = "200", description = "Applicable fees retrieved successfully")
        })
        public ResponseEntity<List<FeeDto>> findApplicableFees(
                        @Parameter(description = "Academic year ID", required = true) @RequestParam UUID academicYearId,
                        @Parameter(description = "Student's Grade Level ID", required = true) @RequestParam UUID gradeId,
                        @Parameter(description = "Student's Educational Stage ID", required = true) @RequestParam UUID stageId,
                        @Parameter(description = "Student's Branch ID", required = true) @RequestParam UUID branchId) {
                List<FeeDto> applicableFees = feeService.findApplicableFeesForStudentContext(academicYearId, gradeId,
                                stageId, branchId);
                return ResponseEntity.ok(applicableFees);
        }

        @PutMapping("/{id}")
        @Operation(summary = "Update a Fee", responses = {
                        @ApiResponse(responseCode = "200", description = "Fee updated successfully"),
                        @ApiResponse(responseCode = "400", description = "Invalid input data", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
                        @ApiResponse(responseCode = "404", description = "Fee or Fee Category not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
                        @ApiResponse(responseCode = "409", description = "Fee name conflict", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<FeeDto> updateFee(
                        @Parameter(description = "ID of the fee to update") @PathVariable UUID id,
                        @Valid @RequestBody UpdateFeeRequest request) {
                FeeDto updatedFee = feeService.updateFee(id, request);
                return ResponseEntity.ok(updatedFee);
        }

        @PutMapping("/{id}/activate")
        @Operation(summary = "Activate a Fee", responses = {
                        @ApiResponse(responseCode = "200", description = "Fee activated successfully"),
                        @ApiResponse(responseCode = "404", description = "Fee not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<FeeDto> activateFee(
                        @Parameter(description = "ID of the fee to activate") @PathVariable UUID id) {
                FeeDto fee = feeService.activateFee(id);
                return ResponseEntity.ok(fee);
        }

        @PutMapping("/{id}/deactivate")
        @Operation(summary = "Deactivate a Fee", responses = {
                        @ApiResponse(responseCode = "200", description = "Fee deactivated successfully"),
                        @ApiResponse(responseCode = "404", description = "Fee not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<FeeDto> deactivateFee(
                        @Parameter(description = "ID of the fee to deactivate") @PathVariable UUID id) {
                FeeDto fee = feeService.deactivateFee(id);
                return ResponseEntity.ok(fee);
        }

        @DeleteMapping("/{id}")
        @Operation(summary = "Delete a Fee", responses = {
                        @ApiResponse(responseCode = "204", description = "Fee deleted successfully"),
                        @ApiResponse(responseCode = "404", description = "Fee not found", content = @Content(schema = @Schema(implementation = ErrorResponse.class))),
                        @ApiResponse(responseCode = "409", description = "Cannot delete fee (e.g., assigned to students)", content = @Content(schema = @Schema(implementation = ErrorResponse.class)))
        })
        public ResponseEntity<Void> deleteFee(
                        @Parameter(description = "ID of the fee to delete") @PathVariable UUID id) {
                feeService.deleteFee(id);
                return ResponseEntity.noContent().build();
        }
}
