package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class BudgetNotFoundException extends RuntimeException {

    public BudgetNotFoundException(UUID id) {
        super("Budget entry not found with ID: " + id);
    }

    public BudgetNotFoundException(UUID chartOfAccountId, String fiscalYear) {
        super(String.format("Budget entry not found for Account ID %s and Fiscal Year %s", chartOfAccountId, fiscalYear));
    }

     public BudgetNotFoundException(String message) {
        super(message);
    }

     public BudgetNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
