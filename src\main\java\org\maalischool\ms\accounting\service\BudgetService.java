package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.BudgetDto;
import org.maalischool.ms.accounting.dto.CreateBudgetRequest;
import org.maalischool.ms.accounting.dto.UpdateBudgetRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing Budgets.
 */
public interface BudgetService {

    /**
     * Creates a new budget entry.
     *
     * @param request the request object containing budget details.
     * @return the created budget DTO.
     * @throws org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException if the associated account is not found.
     */
    BudgetDto createBudget(CreateBudgetRequest request);

    /**
     * Retrieves a budget entry by its ID.
     *
     * @param id the UUID of the budget entry.
     * @return the budget DTO.
     * @throws org.maalischool.ms.accounting.exception.BudgetNotFoundException if the budget entry is not found.
     */
    BudgetDto getBudgetById(UUID id);

    /**
     * Retrieves all budget entries for a specific academic year.
     *
     * @param academicYear the academic year string (e.g., "2024-2025").
     * @param pageable     pagination information.
     * @return a page of budget DTOs for the academic year.
     */
    Page<BudgetDto> getBudgetsByAcademicYear(String academicYear, Pageable pageable); // Renamed parameter and method

    /**
     * Retrieves the budget entry for a specific account, academic year, and period.
     *
     * @param chartOfAccountId the UUID of the chart of account.
     * @param academicYear     the academic year string.
     * @param period           the budget period string (e.g., "ANNUAL", "Q1").
     * @return the budget DTO.
     * @throws org.maalischool.ms.accounting.exception.BudgetNotFoundException if the budget entry is not found.
     */
    BudgetDto getBudgetByAccountAndPeriod(UUID chartOfAccountId, String academicYear, String period); // Updated signature


    /**
     * Retrieves all budget entries with pagination.
     *
     * @param pageable pagination information.
     * @return a page of budget DTOs.
     */
    Page<BudgetDto> getAllBudgets(Pageable pageable);

    /**
     * Updates an existing budget entry.
     *
     * @param id      the UUID of the budget entry to update.
     * @param request the request object containing updated budget details.
     * @return the updated budget DTO.
     * @throws org.maalischool.ms.accounting.exception.BudgetNotFoundException if the budget entry is not found.
     * @throws org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException if the associated account in the request is not found.
     */
    BudgetDto updateBudget(UUID id, UpdateBudgetRequest request);

    /**
     * Deletes a budget entry by its ID.
     *
     * @param id the UUID of the budget entry to delete.
     * @throws org.maalischool.ms.accounting.exception.BudgetNotFoundException if the budget entry is not found.
     */
    void deleteBudget(UUID id);
}
