package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GeneralLedgerEntryDto {

    private LocalDate date;
    private UUID journalEntryId; // Assuming JournalEntry uses UUID
    private String accountCode;
    private String accountName;
    private String description;
    private BigDecimal debit;
    private BigDecimal credit;
    private BigDecimal runningBalance;
    private BigDecimal cumulativeBalance; // Opening balance at the start of the period

    // Student information
    private UUID studentId;
    private String studentAdmissionNumber;
    private String studentFirstName;
    private String studentLastName;
}
