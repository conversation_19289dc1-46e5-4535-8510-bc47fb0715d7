package org.maalischool.ms.auth.service.impl;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
// Removed unused SignatureAlgorithm import
import io.jsonwebtoken.io.Decoders;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import org.maalischool.ms.auth.service.JwtService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import javax.crypto.SecretKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class JwtServiceImpl implements JwtService {

    private static final Logger log = LoggerFactory.getLogger(JwtServiceImpl.class);

    @Value("${jwt.secret.key}")
    private String secretKeyString;

    @Value("${jwt.expiration.ms}")
    private long jwtExpirationMs;

    private SecretKey secretKey;

    @PostConstruct
    public void init() {
        try {
            // Use Base64URL decoder as it's common for JWTs and handles '_' and '-'
            byte[] keyBytes = Decoders.BASE64URL.decode(secretKeyString);
            if (keyBytes.length < 32) { // HS256 requires at least 256 bits (32 bytes)
                 log.warn("JWT Secret Key is shorter than the recommended 32 bytes for HS256.");
                 // Consider throwing an exception if a minimum length is strictly required
                 // throw new IllegalArgumentException("JWT secret key is too short. Minimum 32 bytes required.");
            }
            this.secretKey = Keys.hmacShaKeyFor(keyBytes);
            log.info("JWT Secret Key initialized successfully.");
        } catch (Exception e) {
            // This error will still occur if the key in .env contains invalid characters other than _ or -
            log.error("Error initializing JWT Secret Key: {}. Ensure the key in '.env' (JWT_SECRET_KEY) is a valid Base64URL encoded string and is sufficiently long.", e.getMessage());
            // Consider throwing a runtime exception or preventing application startup
            // if the key is essential and invalid.
            throw new IllegalArgumentException("Invalid JWT secret key configuration. Please check JWT_SECRET_KEY in your .env file.", e);
        }
    }


    @Override
    public String extractUsername(String token) {
        return extractClaim(token, Claims::getSubject);
    }

    @Override
    public <T> T extractClaim(String token, Function<Claims, T> claimsResolver) {
        final Claims claims = extractAllClaims(token);
        return claimsResolver.apply(claims);
    }

    @Override
    public String generateToken(UserDetails userDetails) {
        return generateToken(new HashMap<>(), userDetails);
    }

    @Override
    public String generateToken(Map<String, Object> extraClaims, UserDetails userDetails) {
        // Add roles/authorities to claims if needed
         extraClaims.put("authorities", userDetails.getAuthorities().stream()
                                                   .map(GrantedAuthority::getAuthority)
                                                   .collect(Collectors.toList()));

        return buildToken(extraClaims, userDetails, jwtExpirationMs);
    }

    private String buildToken(Map<String, Object> extraClaims, UserDetails userDetails, long expiration) {
        return Jwts.builder()
                .claims(extraClaims) // Use claims() instead of setClaims() for newer versions
                .subject(userDetails.getUsername()) // Usually email
                .issuedAt(new Date(System.currentTimeMillis()))
                .expiration(new Date(System.currentTimeMillis() + expiration)) // Use expiration() method
                .signWith(getSignInKey(), Jwts.SIG.HS256) // Use Jwts.SIG constant for algorithm
                .compact();
    }


    @Override
    public boolean isTokenValid(String token, UserDetails userDetails) {
        try {
            final String username = extractUsername(token);
            return (username.equals(userDetails.getUsername())) && !isTokenExpired(token);
        } catch (Exception e) {
            log.warn("JWT token validation failed: {}", e.getMessage());
            return false;
        }
    }

    private boolean isTokenExpired(String token) {
        return extractExpiration(token).before(new Date());
    }

    @Override
    public Date extractExpiration(String token) {
        return extractClaim(token, Claims::getExpiration);
    }

    private Claims extractAllClaims(String token) {
         // Use the modern approach with parser() method
        return Jwts.parser() // Use parser() instead of parserBuilder() if verifyWith is used directly
                .verifyWith(getSignInKey()) // Specify the key for verification
                .build()
                .parseSignedClaims(token) // Use parseSignedClaims for JWS
                .getPayload(); // Use getPayload() to get Claims
    }

    private SecretKey getSignInKey() {
        if (this.secretKey == null) {
            // This should ideally not happen due to @PostConstruct, but as a safeguard:
            log.error("JWT Secret Key is null during signing key retrieval!");
            throw new IllegalStateException("JWT Secret Key has not been initialized.");
        }
        return this.secretKey;
    }
}
