package org.maalischool.ms.accounting.dto;

import jakarta.validation.Valid;
import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class CreateReceiptRequest {
    @NotNull(message = "Student ID cannot be null")
    private UUID studentId;

    @NotNull(message = "Receipt date cannot be null")
    private LocalDate receiptDate;

    @NotNull(message = "Payment method ID cannot be null")
    private UUID paymentMethodId;

    @NotNull(message = "Total amount received cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Total amount received must be positive")
    private BigDecimal totalAmountReceived;

    @Size(max = 100, message = "Reference number must be less than 100 characters")
    private String referenceNumber; // e.g., Cheque number, transaction ID

    @Size(max = 1000, message = "Notes must be less than 1000 characters")
    private String notes;

    @NotEmpty(message = "Receipt must have at least one payment detail")
    @Valid
    private List<CreatePaymentDetailRequest> payments; // Renamed from allocations
}
