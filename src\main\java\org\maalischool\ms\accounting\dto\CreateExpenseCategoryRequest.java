package org.maalischool.ms.accounting.dto;

import java.util.UUID;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateExpenseCategoryRequest {
    @NotBlank(message = "English expense category name cannot be blank")
    @Size(max = 100, message = "English expense category name must be less than 100 characters")
    private String nameEn;

    @NotBlank(message = "Arabic expense category name cannot be blank")
    @Size(max = 100, message = "Arabic expense category name must be less than 100 characters")
    private String nameAr;

    @Size(max = 500, message = "English description must be less than 500 characters")
    private String descriptionEn;

    @Size(max = 500, message = "Arabic description must be less than 500 characters")
    private String descriptionAr;

    private UUID parentCategoryId; // Optional parent

    @NotNull(message = "Expense Account ID cannot be null") // Make mandatory
    private UUID expenseAccountId;
}
