package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ItemNotFoundException extends RuntimeException {

    public ItemNotFoundException(UUID id) {
        super("Inventory Item not found with ID: " + id);
    }

    public ItemNotFoundException(String sku) {
        super("Inventory Item not found with SKU: " + sku);
    }

     public ItemNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
