package org.maalischool.ms.auth.config;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.filter.JwtAuthenticationFilter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod; // Import HttpMethod
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.CorsConfigurationSource;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;

import java.util.Arrays;
import java.util.List;

@Configuration
@EnableWebSecurity // Enable Spring Security's web security support
@EnableMethodSecurity(prePostEnabled = true, securedEnabled = true, jsr250Enabled = true) // Enable method-level security
@RequiredArgsConstructor
public class SecurityConfig {

    private final JwtAuthenticationFilter jwtAuthFilter;
    private final AuthenticationProvider authenticationProvider; // Provided by ApplicationConfig
    private final JwtAuthenticationEntryPoint jwtAuthenticationEntryPoint; // Custom entry point

    // Define public endpoints
    private static final String[] PUBLIC_ENDPOINTS = {
            "/api/v1/auth/**", // Registration, Login, Activation, Password Reset etc.
            // Swagger UI v3 (OpenAPI)
            "/v3/api-docs/**",
            "/swagger-ui/**",
            "/api-docs/**", // Legacy or custom path if used
            // Actuator endpoints (adjust exposure in application.properties for production)
            // Allow unauthenticated access to health/info, secure others if needed
            "/actuator/health",
            "/actuator/info",
            // Add other public endpoints like static resources if needed
            "/error"
    };

    // Define Admin-only endpoints pattern (used for reference or if central config is preferred over @PreAuthorize)
    private static final String ADMIN_ENDPOINTS_PATTERN = "/api/v1/admin/**";
    // Define general student/guardian profile endpoints (specific authorization handled by @PreAuthorize)
    private static final String STUDENT_ENDPOINTS_PATTERN = "/api/v1/students/**";
    private static final String GUARDIAN_ENDPOINTS_PATTERN = "/api/v1/guardians/**"; // Corrected path


    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                // Disable CSRF protection - common for stateless REST APIs
                .csrf(AbstractHttpConfigurer::disable)

                // Configure CORS - Allow requests from specific origins
                // Ensure CORS is enabled and uses the defined source
                .cors(cors -> cors.configurationSource(corsConfigurationSource())) // Apply CORS config

                // Configure exception handling, specifically for authentication entry point
                .exceptionHandling(exception -> exception
                        .authenticationEntryPoint(jwtAuthenticationEntryPoint)
                )

                // Configure authorization rules
                .authorizeHttpRequests(auth -> auth
                        .requestMatchers(PUBLIC_ENDPOINTS).permitAll() // Allow public access to specified endpoints
                        // Secure actuator endpoints beyond health/info
                        .requestMatchers("/actuator/**").permitAll() // Or restrict further, e.g., .hasRole("ADMIN")
                        // Other endpoints require authentication (specific roles checked via @PreAuthorize)
                        // .requestMatchers(ADMIN_ENDPOINTS_PATTERN).authenticated() // Let @PreAuthorize handle admin routes
                        .requestMatchers(STUDENT_ENDPOINTS_PATTERN).authenticated() // Require auth for student routes
                        .requestMatchers(GUARDIAN_ENDPOINTS_PATTERN).authenticated() // Require auth for guardian routes
                        .anyRequest().authenticated() // Default deny for any other path
                )

                // Configure session management to be stateless
                .sessionManagement(session -> session
                        .sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )

                // Set the custom authentication provider
                .authenticationProvider(authenticationProvider)

                // Add the JWT filter before the standard UsernamePasswordAuthenticationFilter
                .addFilterBefore(jwtAuthFilter, UsernamePasswordAuthenticationFilter.class);

        return http.build();
    }

    /**
     * Configures CORS (Cross-Origin Resource Sharing).
     * Adjust allowed origins, methods, and headers as needed for your frontend application.
     *
     * @return CorsConfigurationSource
     */
    @Bean
    public CorsConfigurationSource corsConfigurationSource() {
        CorsConfiguration configuration = new CorsConfiguration();
        // Allow requests from these origins (e.g., your frontend URL)
        // Use "*" for development only, be specific in production!
        configuration.setAllowedOrigins(List.of(
                "http://localhost:4200", // Default Angular dev server origin
                "http://localhost:3000", // Example React dev server
                "http://localhost:8081",  // Example: If backend serves
                "http://**************:3000"

        ));
        configuration.setAllowedMethods(Arrays.asList("GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"));
        configuration.setAllowedHeaders(Arrays.asList(
                "Authorization",
                "Cache-Control",
                "Content-Type",
                "X-Requested-With",
                "Accept",
                "Accept-Language", // Ensure Accept-Language is allowed for i18n
                "Origin",
                "Access-Control-Request-Method",
                "Access-Control-Request-Headers"
        ));
        configuration.setExposedHeaders(Arrays.asList("Authorization", "Content-Type")); // Expose headers needed by client
        configuration.setAllowCredentials(true); // Important if you need to send cookies or Authorization headers
        configuration.setMaxAge(3600L); // How long the results of a preflight request can be cached

        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", configuration); // Apply this configuration to all paths
        return source;
    }
}
