package org.maalischool.ms.auth.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.AuthResponse;
import org.maalischool.ms.auth.dto.LoginRequest;
import org.maalischool.ms.auth.model.Permission; // Import Permission
import org.maalischool.ms.auth.model.Role; // Import Role
import org.maalischool.ms.auth.repository.UserRepository;
import org.maalischool.ms.auth.service.AuthService;
import org.maalischool.ms.auth.service.JwtService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.List; // Import List
import java.util.stream.Collectors; // Import Collectors

@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

    private final AuthenticationManager authenticationManager;
    private final UserRepository userRepository;
    private final JwtService jwtService;

    @Override
    public AuthResponse login(LoginRequest loginRequest) {
        // AuthenticationManager handles the password verification using UserDetailsService
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getEmail(),
                        loginRequest.getPassword()
                )
        );

        // If authentication is successful, principal should be UserDetails
        // We fetch the user again to ensure we have the full User entity if needed,
        // though UserDetails from authentication.getPrincipal() might suffice.
        var user = userRepository.findByEmail(loginRequest.getEmail())
                .orElseThrow(() -> new UsernameNotFoundException("User not found after authentication")); // Should not happen if auth succeeded

        String jwtToken = jwtService.generateToken(user);

        // Extract role names
        List<String> roleNames = user.getRoles().stream()
                                     .map(Role::getName)
                                     .collect(Collectors.toList());

        // Extract unique permission names from all roles
        List<String> permissionNames = user.getRoles().stream()
                                           .flatMap(role -> role.getPermissions().stream()) // Get all permissions from all roles
                                           .map(Permission::getName) // Get the name of each permission
                                           .distinct() // Ensure uniqueness
                                           .collect(Collectors.toList()); // Collect into a list

        // Consider adding refresh token logic here as well
        return AuthResponse.builder()
                .accessToken(jwtToken)
                .userId(user.getId())
                .email(user.getEmail())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .roles(roleNames) // Add the list of role names
                .permissions(permissionNames) // Add the list of permission names
                .build();
    }
}
