package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.ExpenseReportDto;

import java.time.LocalDate;

/**
 * Service interface for generating expense reports
 */
public interface ExpenseReportService {
    
    /**
     * Generate a comprehensive expense report for a specified date range
     * 
     * @param startDate The start date of the report period
     * @param endDate The end date of the report period
     * @return An expense report containing detailed expense information and summary statistics
     */
    ExpenseReportDto generateExpenseReport(LocalDate startDate, LocalDate endDate);
}
