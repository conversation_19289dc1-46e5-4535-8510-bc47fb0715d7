package org.maalischool.ms.accounting.service;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.GeneralLedgerEntryDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface GeneralLedgerService {

    /**
     * Get general ledger entries with cumulative balance for the specified period
     *
     * @param startDate Start date of the period
     * @param endDate End date of the period
     * @param accountIds Optional list of account IDs to filter by
     * @param isPosted Optional filter for posted entries
     * @param pageable Pagination information
     * @return Page of general ledger entries with running and cumulative balances
     */
    Page<GeneralLedgerEntryDto> getGeneralLedger(LocalDate startDate, LocalDate endDate, List<UUID> accountIds,
            Boolean isPosted, Pageable pageable);
}
