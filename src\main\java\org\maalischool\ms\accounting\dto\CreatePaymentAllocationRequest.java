package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
public class CreatePaymentAllocationRequest {
    @NotNull(message = "Student Fee ID cannot be null")
    private UUID studentFeeId;

    @NotNull(message = "Amount allocated cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount allocated must be positive")
    private BigDecimal amountAllocated;
}
