package org.maalischool.ms.accounting.repository;

import jakarta.persistence.criteria.Predicate;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.maalischool.ms.accounting.model.enums.AccountingCategory; // Import AccountingCategory
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ChartOfAccountRepository extends JpaRepository<ChartOfAccount, UUID>, JpaSpecificationExecutor<ChartOfAccount> { // Extend JpaSpecificationExecutor

    Optional<ChartOfAccount> findByAccountNumber(String accountNumber);

    // findByActiveTrue method is removed, filtering will be done via Specification

    /**
     * Helper static method to create a Specification based on search criteria.
     * Can be used by the service layer.
     *
     * @param accountNumber Optional account number filter (contains)
     * @param nameEn        Optional English name filter (contains, case-insensitive)
     * @param nameAr        Optional Arabic name filter (contains, case-insensitive)
     * @param category      Optional AccountingCategory filter (exact match)
     * @param active        Optional active status filter (exact match)
     * @return A Specification for ChartOfAccount
     */
    static Specification<ChartOfAccount> createSpecification(String accountNumber, String nameEn, String nameAr, AccountingCategory category, Boolean active) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(accountNumber)) {
                predicates.add(criteriaBuilder.like(root.get("accountNumber"), "%" + accountNumber + "%"));
            }
            if (StringUtils.hasText(nameEn)) {
                predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get("nameEn")), "%" + nameEn.toLowerCase() + "%"));
            }
            if (StringUtils.hasText(nameAr)) {
                predicates.add(criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")), "%" + nameAr.toLowerCase() + "%"));
            }
            if (category != null) {
                predicates.add(criteriaBuilder.equal(root.get("category"), category)); // Add category filter
            }
            if (active != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), active));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
