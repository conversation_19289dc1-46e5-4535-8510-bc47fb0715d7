package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.student.model.Student; // Import Student
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "acc_receipts", uniqueConstraints = {
        @UniqueConstraint(columnNames = "receipt_number")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id", exclude = {"student", "paymentMethod", "payments"}) // Exclude relationships
@EntityListeners(AuditingEntityListener.class)
public class Receipt {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "receipt_number", nullable = false, unique = true, length = 50)
    private String receiptNumber; // Should be generated (e.g., sequence or pattern)

    @Column(name = "receipt_date", nullable = false)
    private LocalDate receiptDate;

    // Payer information (can be Student or Guardian or Other)
    // For simplicity, linking to Student for now. Could be more generic.
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_id") // Nullable if receipt is not for a student fee
    private Student student;

    @Column(name = "payer_name", length = 255) // Store name if not linked to student/guardian
    private String payerName;

    @Column(name = "total_amount_received", nullable = false, precision = 19, scale = 4)
    private BigDecimal totalAmountReceived;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "payment_method_id", nullable = false)
    private PaymentMethod paymentMethod;

    @Column(name = "reference_number", length = 100) // Add reference number field
    private String referenceNumber;

    @Column(length = 1000)
    private String notes;

    // Link to the individual payment allocations made under this receipt
    @OneToMany(mappedBy = "receipt", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Payment> payments = new ArrayList<>();

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    // No last modified date for receipts typically, they are immutable once created.

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID createdBy; // Link to User ID

    // Fields for cancellation
    @Column(name = "cancellation_date")
    private LocalDate cancellationDate;

    @Column(name = "cancellation_reason", length = 500)
    private String cancellationReason;

    // Helper method
    public void addPayment(Payment payment) {
        payments.add(payment);
        payment.setReceipt(this);
    }

    public void removePayment(Payment payment) {
        payments.remove(payment);
        payment.setReceipt(null);
    }
}
