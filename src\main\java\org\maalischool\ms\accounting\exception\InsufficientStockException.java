package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.math.BigDecimal;
import java.util.UUID;

@ResponseStatus(HttpStatus.BAD_REQUEST) // Or HttpStatus.CONFLICT
public class InsufficientStockException extends RuntimeException {

    public InsufficientStockException(UUID itemId, String itemSku, BigDecimal currentStock, BigDecimal requestedDecrease) {
        super(String.format("Insufficient stock for item %s (ID: %s). Current stock: %s, Requested decrease: %s",
                itemSku, itemId, currentStock, requestedDecrease));
    }

    public InsufficientStockException(String message) {
        super(message);
    }

    public InsufficientStockException(String message, Throwable cause) {
        super(message, cause);
    }
}
