package org.maalischool.ms.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
// Ensure correct ApiResponse annotation is imported for Swagger
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
// Import DTOs including the ApiResponse DTO
import org.maalischool.ms.auth.dto.*;
// Explicitly import the ApiResponse DTO to avoid confusion with the annotation
import org.maalischool.ms.auth.exception.InvalidTokenException;
import org.maalischool.ms.auth.service.AuthService;
import org.maalischool.ms.auth.service.UserService;
import org.maalischool.ms.exception.ErrorResponse;
import org.springframework.context.MessageSource; // Import MessageSource
import org.springframework.context.i18n.LocaleContextHolder; // Import LocaleContextHolder
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/v1/auth") // Base path from project context
@RequiredArgsConstructor
@Tag(name = "Authentication", description = "Endpoints for user registration, login, activation, and password management")
public class AuthController {

    private final AuthService authService;
    private final UserService userService;
    private final MessageSource messageSource; // Inject MessageSource

    // Helper to get localized message
    private String getMessage(String code, Object... args) {
        return messageSource.getMessage(code, args, code, LocaleContextHolder.getLocale());
    }

    @Operation(summary = "Register a new user", description = "Creates a new user account. Requires email activation.")
    @ApiResponses(value = {
            // Use the Swagger annotation here
            @ApiResponse(responseCode = "201", description = "User registered successfully (requires activation)",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = org.maalischool.ms.auth.dto.ApiResponse.class))), // Reference DTO in schema
            @ApiResponse(responseCode = "400", description = "Invalid input data",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "409", description = "Email already exists",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/register")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> register( // Use DTO in ResponseEntity type
            @Valid @RequestBody RegisterRequest registerRequest
    ) {
        userService.registerUser(registerRequest);
        // Use the DTO's builder here with localized message
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(org.maalischool.ms.auth.dto.ApiResponse.builder()
                        .success(true)
                        .message(getMessage("response.registration.success"))
                        .build());
    }

    @Operation(summary = "Authenticate user", description = "Logs in a user and returns an access token.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Login successful",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = AuthResponse.class))),
            @ApiResponse(responseCode = "400", description = "Invalid input data",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class))),
            @ApiResponse(responseCode = "401", description = "Invalid credentials or user not found/enabled",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/login")
    public ResponseEntity<AuthResponse> login(
            @Valid @RequestBody LoginRequest loginRequest
    ) {
        AuthResponse authResponse = authService.login(loginRequest);
        return ResponseEntity.ok(authResponse);
    }

    @Operation(summary = "Activate user account", description = "Activates a user account using the provided token.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Account activated successfully",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = org.maalischool.ms.auth.dto.ApiResponse.class))), // Reference DTO
            @ApiResponse(responseCode = "400", description = "Invalid or expired activation token",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping("/activate")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> activateAccount(@RequestParam("token") String token) { // Use DTO
        userService.activateUser(token); // Throws exception if invalid/expired handled by GlobalExceptionHandler
        // Use DTO builder with localized message
        return ResponseEntity.ok(org.maalischool.ms.auth.dto.ApiResponse.builder()
                .success(true)
                .message(getMessage("response.activation.success"))
                .build());
    }

    @Operation(summary = "Initiate password reset", description = "Sends a password reset link/token to the user's email.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset email sent (if user exists)",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = org.maalischool.ms.auth.dto.ApiResponse.class))), // Reference DTO
            @ApiResponse(responseCode = "400", description = "Invalid email format",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class)))
            // Note: Typically no 404 here to avoid revealing if an email exists
    })
    @PostMapping("/forgot-password")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> forgotPassword( // Use DTO
            @Valid @RequestBody ForgotPasswordRequest forgotPasswordRequest
    ) {
        userService.initiatePasswordReset(forgotPasswordRequest.getEmail());
        // Always return OK to prevent user enumeration attacks
        // Use DTO builder with localized message
        return ResponseEntity.ok(org.maalischool.ms.auth.dto.ApiResponse.builder()
                .success(true)
                .message(getMessage("response.password.reset.init"))
                .build());
    }

    @Operation(summary = "Reset user password", description = "Sets a new password using a valid reset token.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Password reset successfully",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = org.maalischool.ms.auth.dto.ApiResponse.class))), // Reference DTO
            @ApiResponse(responseCode = "400", description = "Invalid/expired token or invalid new password",
                         content = @Content(mediaType = "application/json",
                         schema = @Schema(implementation = ErrorResponse.class)))
    })
    @PostMapping("/reset-password")
    public ResponseEntity<org.maalischool.ms.auth.dto.ApiResponse> resetPassword( // Use DTO
            @Valid @RequestBody ResetPasswordRequest resetPasswordRequest
    ) {
        boolean success = userService.completePasswordReset(resetPasswordRequest);
        // userService throws exceptions for invalid/expired tokens, handled globally
        if (!success) {
             // This path might not be reachable if exceptions are thrown correctly
             throw new InvalidTokenException("reset", getMessage("error.password.reset.failed"));
        }
         // Use DTO builder with localized message
        return ResponseEntity.ok(org.maalischool.ms.auth.dto.ApiResponse.builder()
                .success(true)
                .message(getMessage("response.password.reset.success"))
                .build());
    }
}
