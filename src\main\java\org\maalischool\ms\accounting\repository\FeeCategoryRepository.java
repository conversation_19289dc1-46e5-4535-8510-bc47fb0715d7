package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.FeeCategory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface FeeCategoryRepository extends JpaRepository<FeeCategory, UUID> {

    // Optional: Keep if exact match lookup by English name is still needed elsewhere.
    Optional<FeeCategory> findByNameEnIgnoreCase(String nameEn);

    /**
     * Finds FeeCategories where either the English name or Arabic name contains the given search term, ignoring case.
     * Supports pagination and sorting via the Pageable parameter.
     *
     * @param searchTerm The term to search for within nameEn or nameAr. Can be null or empty to fetch all.
     * @param pageable   Pagination and sorting information.
     * @return A Page of matching FeeCategory entities.
     */
    @Query("SELECT fc FROM FeeCategory fc WHERE " +
           "(:searchTerm IS NULL OR :searchTerm = '' OR " +
           "LOWER(fc.nameEn) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(fc.nameAr) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<FeeCategory> findByNameEnOrNameArContainingIgnoreCase(@Param("searchTerm") String searchTerm, Pageable pageable);

    // The check for associated fees belongs in FeeRepository, not here.
}
