package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "acc_expense_categories", uniqueConstraints = {
        @UniqueConstraint(columnNames = "name") // Name should be unique, perhaps within parent?
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class ExpenseCategory {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(nullable = false, unique = true, length = 100)
    private String nameEn;
    @Column(nullable = false, unique = true, length = 100)
    private String nameAr;

    @Column(length = 500)
    private String descriptionEn;
    @Column(length = 500)
    private String descriptionAr;

    // Optional: For hierarchical categories
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_category_id")
    private ExpenseCategory parentCategory;

    // Mandatory link to the expense account in ChartOfAccounts
    @ManyToOne(fetch = FetchType.LAZY, optional = false) // Make mandatory
    @JoinColumn(name = "expense_account_id", nullable = false) // Rename column and make non-nullable
    private ChartOfAccount expenseAccount;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
