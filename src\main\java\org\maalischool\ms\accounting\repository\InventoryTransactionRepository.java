package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.InventoryTransaction;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Repository
public interface InventoryTransactionRepository extends JpaRepository<InventoryTransaction, UUID> {

    List<InventoryTransaction> findByItemIdOrderByTransactionDateDescCreatedDateDesc(UUID itemId);

    Page<InventoryTransaction> findByItemIdOrderByTransactionDateDescCreatedDateDesc(UUID itemId, Pageable pageable);

    List<InventoryTransaction> findByItemIdAndTransactionDateBetweenOrderByTransactionDateAscCreatedDateAsc(UUID itemId, LocalDate startDate, LocalDate endDate);

    // Calculate stock level for an item up to a certain date
    // Note: This query needs adjustment based on how quantityChange is stored (always positive)
    // and transactionType indicates direction. A more complex query or service-level calculation
    // might be needed for accurate historical stock levels.
    // For now, commenting out the potentially incorrect query.
    /*
    @Query("SELECT COALESCE(SUM(CASE WHEN it.transactionType IN ('PURCHASE_RECEIPT', 'TRANSFER_IN', 'ADJUSTMENT_POSITIVE', 'OPENING_BALANCE') THEN it.quantityChange ELSE -it.quantityChange END), 0) " +
           "FROM InventoryTransaction it " +
           "WHERE it.item.id = :itemId AND it.transactionDate <= :date")
    BigDecimal calculateStockLevelAsOfDate(@Param("itemId") UUID itemId, @Param("date") LocalDate date);
    */

    // Calculate stock level for an item based on all transactions (current stock)
    // Note: Similar to above, this needs adjustment. The Item.quantityOnHand field is now the source of truth.
    // Commenting out this potentially incorrect query.
    /*
    @Query("SELECT COALESCE(SUM(CASE WHEN it.transactionType IN ('PURCHASE_RECEIPT', 'TRANSFER_IN', 'ADJUSTMENT_POSITIVE', 'OPENING_BALANCE') THEN it.quantityChange ELSE -it.quantityChange END), 0) " +
           "FROM InventoryTransaction it " +
           "WHERE it.item.id = :itemId")
    BigDecimal calculateCurrentStockLevelFromTransactions(@Param("itemId") UUID itemId);
    */

    // Method used in InventoryServiceImpl.getTransactionHistoryForItem
    Page<InventoryTransaction> findByItemIdAndTransactionDateBetweenOrderByTransactionDateDesc(
            UUID itemId, LocalDate startDate, LocalDate endDate, Pageable pageable);

    // Method used in InventoryServiceImpl.getAllTransactionHistory
    Page<InventoryTransaction> findByTransactionDateBetweenOrderByTransactionDateDesc(
            LocalDate startDate, LocalDate endDate, Pageable pageable);
}
