package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class AssignFeeRequest {
    @NotNull(message = "Student ID cannot be null")
    private UUID studentId;

    @NotNull(message = "Fee ID cannot be null")
    private UUID feeId;

    // Optional overrides (if different from the base Fee)
    private LocalDate dueDate;

    @DecimalMin(value = "0.0", message = "Amount must be non-negative")
    private BigDecimal amount; // Override amount if needed (e.g., pro-rated)

    // Optional initial discount
    private UUID discountId;

    @Size(max = 500, message = "English notes must be less than 500 characters")
    private String notesEn;

    @Size(max = 500, message = "Arabic notes must be less than 500 characters")
    private String notesAr;
}
