package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.UUID;

import jakarta.validation.constraints.DecimalMax;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class CreateTaxRequest {

    @NotBlank(message = "{validation.tax.nameAr.notBlank}")
    private String nameAr;

    @NotBlank(message = "{validation.tax.nameEn.notBlank}")
    private String nameEn;

    private String descriptionAr;

    private String descriptionEn;

    @NotNull(message = "{validation.tax.percent.notNull}")
    @DecimalMin(value = "0.00", message = "{validation.tax.percent.min}")
    @DecimalMax(value = "100.00", message = "{validation.tax.percent.max}")
    private BigDecimal percent;

    @NotNull(message = "{validation.tax.chartOfAccountId.notNull}")
    private UUID chartOfAccountId;
}
