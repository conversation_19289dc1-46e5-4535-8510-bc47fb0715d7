package org.maalischool.ms.student.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.maalischool.ms.student.model.Country;
import org.maalischool.ms.student.repository.CountryRepository;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.List;

@Service
@RequiredArgsConstructor
@Slf4j
public class CountryDataInitializationService {

    private final CountryRepository countryRepository;

    @Bean
    @Transactional
    public ApplicationRunner initializeCountryData() {
        return args -> {
            log.info("Starting country data initialization...");
            
            if (countryRepository.count() > 0) {
                log.info("Countries already exist in database. Skipping initialization.");
                return;
            }

            List<Country> countries = getDefaultCountries();
            countryRepository.saveAll(countries);
            
            log.info("Successfully initialized {} countries", countries.size());
        };
    }

    private List<Country> getDefaultCountries() {
        return Arrays.asList(
            // Middle East & Arab Countries
            Country.builder().nameAr("المملكة العربية السعودية").nameEn("Saudi Arabia").code("SA").build(),
            Country.builder().nameAr("الإمارات العربية المتحدة").nameEn("United Arab Emirates").code("AE").build(),
            Country.builder().nameAr("الكويت").nameEn("Kuwait").code("KW").build(),
            Country.builder().nameAr("قطر").nameEn("Qatar").code("QA").build(),
            Country.builder().nameAr("البحرين").nameEn("Bahrain").code("BH").build(),
            Country.builder().nameAr("عُمان").nameEn("Oman").code("OM").build(),
            Country.builder().nameAr("الأردن").nameEn("Jordan").code("JO").build(),
            Country.builder().nameAr("لبنان").nameEn("Lebanon").code("LB").build(),
            Country.builder().nameAr("سوريا").nameEn("Syria").code("SY").build(),
            Country.builder().nameAr("العراق").nameEn("Iraq").code("IQ").build(),
            Country.builder().nameAr("فلسطين").nameEn("Palestine").code("PS").build(),
            Country.builder().nameAr("مصر").nameEn("Egypt").code("EG").build(),
            Country.builder().nameAr("ليبيا").nameEn("Libya").code("LY").build(),
            Country.builder().nameAr("تونس").nameEn("Tunisia").code("TN").build(),
            Country.builder().nameAr("الجزائر").nameEn("Algeria").code("DZ").build(),
            Country.builder().nameAr("المغرب").nameEn("Morocco").code("MA").build(),
            Country.builder().nameAr("السودان").nameEn("Sudan").code("SD").build(),
            Country.builder().nameAr("اليمن").nameEn("Yemen").code("YE").build(),
            
            // Major International Countries
            Country.builder().nameAr("الولايات المتحدة الأمريكية").nameEn("United States").code("US").build(),
            Country.builder().nameAr("المملكة المتحدة").nameEn("United Kingdom").code("GB").build(),
            Country.builder().nameAr("كندا").nameEn("Canada").code("CA").build(),
            Country.builder().nameAr("أستراليا").nameEn("Australia").code("AU").build(),
            Country.builder().nameAr("ألمانيا").nameEn("Germany").code("DE").build(),
            Country.builder().nameAr("فرنسا").nameEn("France").code("FR").build(),
            Country.builder().nameAr("إيطاليا").nameEn("Italy").code("IT").build(),
            Country.builder().nameAr("إسبانيا").nameEn("Spain").code("ES").build(),
            Country.builder().nameAr("هولندا").nameEn("Netherlands").code("NL").build(),
            Country.builder().nameAr("بلجيكا").nameEn("Belgium").code("BE").build(),
            Country.builder().nameAr("سويسرا").nameEn("Switzerland").code("CH").build(),
            Country.builder().nameAr("النمسا").nameEn("Austria").code("AT").build(),
            Country.builder().nameAr("السويد").nameEn("Sweden").code("SE").build(),
            Country.builder().nameAr("النرويج").nameEn("Norway").code("NO").build(),
            Country.builder().nameAr("الدنمارك").nameEn("Denmark").code("DK").build(),
            Country.builder().nameAr("فنلندا").nameEn("Finland").code("FI").build(),
            
            // Asian Countries
            Country.builder().nameAr("الصين").nameEn("China").code("CN").build(),
            Country.builder().nameAr("اليابان").nameEn("Japan").code("JP").build(),
            Country.builder().nameAr("كوريا الجنوبية").nameEn("South Korea").code("KR").build(),
            Country.builder().nameAr("الهند").nameEn("India").code("IN").build(),
            Country.builder().nameAr("باكستان").nameEn("Pakistan").code("PK").build(),
            Country.builder().nameAr("بنغلاديش").nameEn("Bangladesh").code("BD").build(),
            Country.builder().nameAr("إندونيسيا").nameEn("Indonesia").code("ID").build(),
            Country.builder().nameAr("ماليزيا").nameEn("Malaysia").code("MY").build(),
            Country.builder().nameAr("سنغافورة").nameEn("Singapore").code("SG").build(),
            Country.builder().nameAr("تايلاند").nameEn("Thailand").code("TH").build(),
            Country.builder().nameAr("الفلبين").nameEn("Philippines").code("PH").build(),
            Country.builder().nameAr("فيتنام").nameEn("Vietnam").code("VN").build(),
            
            // Other Important Countries
            Country.builder().nameAr("روسيا").nameEn("Russia").code("RU").build(),
            Country.builder().nameAr("البرازيل").nameEn("Brazil").code("BR").build(),
            Country.builder().nameAr("الأرجنتين").nameEn("Argentina").code("AR").build(),
            Country.builder().nameAr("المكسيك").nameEn("Mexico").code("MX").build(),
            Country.builder().nameAr("جنوب أفريقيا").nameEn("South Africa").code("ZA").build(),
            Country.builder().nameAr("نيجيريا").nameEn("Nigeria").code("NG").build(),
            Country.builder().nameAr("كينيا").nameEn("Kenya").code("KE").build(),
            Country.builder().nameAr("إثيوبيا").nameEn("Ethiopia").code("ET").build(),
            Country.builder().nameAr("تركيا").nameEn("Turkey").code("TR").build(),
            Country.builder().nameAr("إيران").nameEn("Iran").code("IR").build(),
            Country.builder().nameAr("أفغانستان").nameEn("Afghanistan").code("AF").build(),
            Country.builder().nameAr("أوزبكستان").nameEn("Uzbekistan").code("UZ").build()
        );
    }
}
