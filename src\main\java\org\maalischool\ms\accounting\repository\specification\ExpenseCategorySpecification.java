package org.maalischool.ms.accounting.repository.specification;

import jakarta.persistence.criteria.Predicate;
import org.maalischool.ms.accounting.model.ExpenseCategory;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

/**
 * Utility class for creating JPA Specifications for ExpenseCategory entities.
 */
public class ExpenseCategorySpecification {

    private ExpenseCategorySpecification() {
        // Private constructor
    }

    /**
     * Creates a Specification to filter Expense Categories by a search term
     * matching either nameEn or nameAr (case-insensitive, partial match).
     *
     * @param searchTerm The term to search for in nameEn or nameAr.
     * @return A Specification<ExpenseCategory>.
     */
    public static Specification<ExpenseCategory> filterBySearchTerm(String searchTerm) {
        return (root, query, criteriaBuilder) -> {
            if (!StringUtils.hasText(searchTerm)) {
                // No search term provided, return null (no filtering) or an empty predicate list
                return criteriaBuilder.conjunction(); // Represents an empty WHERE clause
            }

            String searchTermLower = "%" + searchTerm.toLowerCase() + "%";

            Predicate nameEnPredicate = criteriaBuilder.like(criteriaBuilder.lower(root.get("nameEn")), searchTermLower);
            Predicate nameArPredicate = criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")), searchTermLower);

            // Combine with OR
            Predicate combinedPredicate = criteriaBuilder.or(nameEnPredicate, nameArPredicate);

            // Apply default sorting if none is provided in Pageable
            if (query.getOrderList().isEmpty()) {
                query.orderBy(criteriaBuilder.asc(root.get("nameEn"))); // Default sort by nameEn ascending
            }

            return combinedPredicate;
        };
    }
}
