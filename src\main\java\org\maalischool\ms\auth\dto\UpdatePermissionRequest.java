package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// Allow partial updates, so fields are not strictly @NotBlank
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdatePermissionRequest {

    @Size(min = 3, max = 100, message = "{validation.permissionName.size}")
    private String name; // Optional: only if changing name

    @Size(max = 255, message = "Description cannot exceed 255 characters")
    private String description; // Optional: only if changing description
}
