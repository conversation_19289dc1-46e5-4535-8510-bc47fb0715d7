package org.maalischool.ms.auth.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * Base exception for authentication and authorization errors.
 */
@ResponseStatus(HttpStatus.UNAUTHORIZED) // Default status if not overridden
public class AuthException extends RuntimeException {
    public AuthException(String message) {
        super(message);
    }

    public AuthException(String message, Throwable cause) {
        super(message, cause);
    }
}
