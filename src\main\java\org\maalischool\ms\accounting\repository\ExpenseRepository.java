package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Expense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Repository
public interface ExpenseRepository extends JpaRepository<Expense, UUID> {

    List<Expense> findByExpenseDateBetweenOrderByExpenseDateDesc(LocalDate startDate, LocalDate endDate);

    Page<Expense> findByExpenseDateBetweenOrderByExpenseDateDesc(LocalDate startDate, LocalDate endDate, Pageable pageable);

    List<Expense> findByCategoryIdOrderByExpenseDateDesc(UUID categoryId);

    Page<Expense> findByCategoryIdOrderByExpenseDateDesc(UUID categoryId, Pageable pageable);

    List<Expense> findByVendorContainingIgnoreCaseOrderByExpenseDateDesc(String vendor);

    // Calculate total expenses for a category within a date range
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e " +
           "WHERE e.category.id = :categoryId " +
           "AND e.expenseDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalForCategoryBetweenDates(
            @Param("categoryId") UUID categoryId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);

    // Calculate total expenses for an expense account (via category) within a date range
    @Query("SELECT COALESCE(SUM(e.amount), 0) FROM Expense e " +
           "WHERE e.category.expenseAccount.id = :accountId " + // Access account via category
           "AND e.expenseDate BETWEEN :startDate AND :endDate")
    BigDecimal calculateTotalForExpenseAccountBetweenDates(
            @Param("accountId") UUID accountId,
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate);
}
