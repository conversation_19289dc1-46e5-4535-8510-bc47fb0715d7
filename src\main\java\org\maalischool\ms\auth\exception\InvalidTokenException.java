package org.maalischool.ms.auth.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.BAD_REQUEST) // 400 Bad Request
public class InvalidTokenException extends RuntimeException {
    public InvalidTokenException(String tokenType) {
        super("Invalid " + tokenType + " token provided.");
    }
     public InvalidTokenException(String tokenType, String message) {
        super("Invalid " + tokenType + " token: " + message);
    }
}
