package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import org.maalischool.ms.accounting.model.enums.DepreciationMethod;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class CreateFixedAssetRequest {
    @NotBlank(message = "Asset code cannot be blank")
    @Size(max = 50, message = "Asset code must be less than 50 characters")
    private String assetCode;

    @NotBlank(message = "English asset name cannot be blank")
    @Size(max = 255, message = "English asset name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic asset name cannot be blank")
    @Size(max = 255, message = "Arabic asset name must be less than 255 characters")
    private String nameAr;

//    @Size(max = 100, message = "Asset tag must be less than 100 characters") // Add Asset Tag field
//    private String assetTag;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    @NotNull(message = "Acquisition date cannot be null")
    private LocalDate acquisitionDate;

    @NotNull(message = "Acquisition cost cannot be null")
    @DecimalMin(value = "0.0", message = "Acquisition cost must be non-negative")
    private BigDecimal acquisitionCost;

    @NotNull(message = "Asset account (Chart of Account) ID cannot be null")
    private UUID assetAccountId; // e.g., Furniture & Fixtures Asset

    @NotNull(message = "Accumulated Depreciation account (Chart of Account) ID cannot be null")
    private UUID accumulatedDepreciationAccountId;

    @NotNull(message = "Depreciation Expense account (Chart of Account) ID cannot be null")
    private UUID depreciationExpenseAccountId;

    @NotNull(message = "Useful life (years) cannot be null")
    @Positive(message = "Useful life must be positive")
    private Integer usefulLifeYears;

    @NotNull(message = "Salvage value cannot be null")
    @DecimalMin(value = "0.0", message = "Salvage value must be non-negative")
    private BigDecimal salvageValue;

    @NotNull(message = "Depreciation method cannot be null")
    private DepreciationMethod depreciationMethod;

    @Size(max = 255, message = "Location must be less than 255 characters")
    private String location;

//    @Size(max = 100, message = "Serial number must be less than 100 characters")
//    private String serialNumber;

    @NotNull(message = "Initial status cannot be null")
    private AssetStatus status;
}
