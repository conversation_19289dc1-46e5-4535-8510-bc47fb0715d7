package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.AccountingAuditLogDto; // Import DTO
import org.maalischool.ms.accounting.model.AccountingAuditLog;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.AccountingAuditLogRepository;
import org.maalischool.ms.accounting.repository.specification.AccountingAuditLogSpecification; // Import Specification
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page; // Import Page
import org.springframework.data.domain.Pageable; // Import Pageable
import org.springframework.data.jpa.domain.Specification; // Import Specification
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant; // Import Instant
import java.util.List; // Import List
import java.util.UUID;
import java.util.stream.Collectors; // Import Collectors

@Service
@RequiredArgsConstructor
public class AccountingAuditLogServiceImpl implements AccountingAuditLogService {

    private static final Logger log = LoggerFactory.getLogger(AccountingAuditLogServiceImpl.class);
    private final AccountingAuditLogRepository auditLogRepository;

    @Override
    @Transactional(propagation = Propagation.REQUIRED)
    public void logAction(UUID userId, AccountingActionType actionType, String entityType, UUID entityId, String details) {
        try {
            AccountingAuditLog auditEntry = AccountingAuditLog.builder()
                    .userId(userId)
                    .actionType(actionType)
                    .entityType(entityType)
                    .entityId(entityId)
                    .details(details)
                    .build();
            auditLogRepository.save(auditEntry);
        } catch (Exception e) {
            log.error("Failed to save accounting audit log for user {} action {}: {}", userId, actionType, e.getMessage(), e);
            // Consider re-throwing a custom exception if the audit log failure should halt the main transaction
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AccountingAuditLogDto> findAuditLogs(UUID userId, String entityType, UUID entityId, Instant startTime, Instant endTime, Pageable pageable) {
        log.debug("Finding audit logs with criteria: userId={}, entityType={}, entityId={}, startTime={}, endTime={}, pageable={}",
                  userId, entityType, entityId, startTime, endTime, pageable);

        Specification<AccountingAuditLog> spec = AccountingAuditLogSpecification.filterBy(
                userId, entityType, entityId, startTime, endTime
        );

        Page<AccountingAuditLog> auditLogPage = auditLogRepository.findAll(spec, pageable);

        log.debug("Found {} audit logs matching criteria.", auditLogPage.getTotalElements());
        return auditLogPage.map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<AccountingAuditLogDto> findAllByEntityTypeAndEntityId(String entityType, UUID entityId) {
        log.debug("Finding all audit logs for entityType={} and entityId={}", entityType, entityId);
        List<AccountingAuditLog> logs = auditLogRepository.findAllByEntityTypeAndEntityIdOrderByActionTimestampDesc(entityType, entityId);
        log.debug("Found {} audit logs for entityType={} and entityId={}", logs.size(), entityType, entityId);
        return logs.stream()
                   .map(this::mapToDto)
                   .collect(Collectors.toList());
    }

    private AccountingAuditLogDto mapToDto(AccountingAuditLog logEntry) {
        if (logEntry == null) {
            return null;
        }
        return AccountingAuditLogDto.builder()
                .id(logEntry.getId())
                .actionTimestamp(logEntry.getActionTimestamp())
                .userId(logEntry.getUserId())
                .actionType(logEntry.getActionType())
                .entityType(logEntry.getEntityType())
                .entityId(logEntry.getEntityId())
                .details(logEntry.getDetails())
                .build();
    }
}
