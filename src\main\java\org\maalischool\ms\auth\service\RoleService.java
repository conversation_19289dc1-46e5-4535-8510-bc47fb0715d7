package org.maalischool.ms.auth.service;

import org.maalischool.ms.auth.dto.CreateRoleRequest;
import org.maalischool.ms.auth.dto.RoleDto;
import org.maalischool.ms.auth.dto.UpdateRoleRequest;
import org.maalischool.ms.auth.exception.PermissionNotFoundException; // Ensure this import exists if used in JavaDoc
import org.maalischool.ms.auth.exception.RoleNotFoundException; // Ensure this import exists if used in JavaDoc
import org.maalischool.ms.auth.model.Role;


import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface RoleService {

    // Define role constants (implicitly public static final)
    String ROLE_ADMIN = "ROLE_ADMIN";
    String ROLE_USER = "ROLE_USER"; // Default role
    String ROLE_STUDENT = "ROLE_STUDENT";
    String ROLE_PARENT = "ROLE_PARENT";
    String ROLE_TEACHER = "ROLE_TEACHER"; // Example other role

    /**
     * Finds a role by its name.
     *
     * @param name The name of the role (e.g., "ROLE_ADMIN").
     * @return An Optional containing the Role if found, otherwise empty.
     */
    Optional<Role> findByName(String name);

    /**
     * Finds a role by name, or creates it if it doesn't exist.
     *
     * @param name The name of the role.
     * @param description A description for the role if created.
     * @return The existing or newly created Role.
     */
    Role findOrCreateRole(String name, String description);

    /**
     * Creates a new role with specified permissions.
     *
     * @param request DTO containing role details and permission names.
     * @return DTO of the created role.
     * @throws PermissionNotFoundException if any specified permission name is not found.
     * @throws org.springframework.dao.DataIntegrityViolationException if role name already exists.
     */
    RoleDto createRole(CreateRoleRequest request);

    /**
     * Retrieves all roles.
     *
     * @return List of all role DTOs.
     */
    List<RoleDto> findAllRoles();

    /**
     * Finds a role by its ID.
     *
     * @param id The UUID of the role.
     * @return An Optional containing the Role DTO if found.
     */
    Optional<RoleDto> findRoleById(UUID id);

    /**
     * Updates an existing role, optionally changing its name, description, and assigned permissions.
     * If permissionNames is provided, it replaces all existing permissions for the role.
     *
     * @param id The UUID of the role to update.
     * @param request DTO containing updated details.
     * @return DTO of the updated role.
     * @throws RoleNotFoundException if role with id is not found.
     * @throws PermissionNotFoundException if any specified permission name is not found.
     * @throws org.springframework.dao.DataIntegrityViolationException if new name conflicts.
     */
    RoleDto updateRole(UUID id, UpdateRoleRequest request);

    /**
     * Deletes a role by its ID.
     * Consider implications: What happens to users with this role? (Currently, just deletes the role).
     *
     * @param id The UUID of the role to delete.
     * @throws RoleNotFoundException if role with id is not found.
     */
    void deleteRole(UUID id);

    /**
     * Assigns a set of permissions (by name) to an existing role.
     * This replaces all existing permissions for the role.
     *
     * @param roleId The UUID of the role.
     * @param permissionNames List of permission names to assign.
     * @return DTO of the updated role.
     * @throws RoleNotFoundException if role is not found.
     * @throws PermissionNotFoundException if any permission name is not found.
     */
    RoleDto assignPermissionsToRole(UUID roleId, List<String> permissionNames);
}
