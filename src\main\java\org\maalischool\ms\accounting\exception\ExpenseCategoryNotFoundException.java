package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ExpenseCategoryNotFoundException extends RuntimeException {

    public ExpenseCategoryNotFoundException(UUID id) {
        super("Expense Category not found with ID: " + id);
    }

    public ExpenseCategoryNotFoundException(String name) {
        super("Expense Category not found with name: " + name);
    }

     public ExpenseCategoryNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
