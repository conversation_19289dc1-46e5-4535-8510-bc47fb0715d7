package org.maalischool.ms.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List; // Import List
import java.util.UUID;
// Consider adding refreshToken String

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AuthResponse {
    private String accessToken;
    private UUID userId;
    private String email;
    private String firstName;
    private String lastName;
    private List<String> roles; // Field for role names
    private List<String> permissions; // Added field for permission names
}
