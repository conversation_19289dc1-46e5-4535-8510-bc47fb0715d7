package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreateFeeCategoryRequest;
import org.maalischool.ms.accounting.dto.FeeCategoryDto;
import org.maalischool.ms.accounting.dto.SimpleFeeCategoryDto; // Added import
import org.maalischool.ms.accounting.dto.UpdateFeeCategoryRequest;
import org.maalischool.ms.accounting.exception.FeeCategoryNotFoundException;
import org.maalischool.ms.accounting.model.FeeCategory;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.FeeCategoryRepository;
import org.maalischool.ms.accounting.repository.FeeRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.FeeCategoryService;
import org.apache.commons.lang3.StringUtils; // Add this import
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

// Assuming your User entity implements UserDetails or you have a custom principal
import org.maalischool.ms.auth.model.User; // Import your User entity

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class FeeCategoryServiceImpl implements FeeCategoryService {

    private static final Logger log = LoggerFactory.getLogger(FeeCategoryServiceImpl.class);
    private final FeeCategoryRepository feeCategoryRepository;
    private final FeeRepository feeRepository; // To check for associated fees before deletion
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public FeeCategoryDto createFeeCategory(CreateFeeCategoryRequest request) {
        log.info("Creating new Fee Category with nameEn: {}", request.getNameEn());
        // Check uniqueness based on English name
        feeCategoryRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(fc -> {
            throw new IllegalArgumentException("Fee Category with English name '" + request.getNameEn() + "' already exists.");
        });
        // TODO: Consider adding a similar check for nameAr if it also needs to be unique

        FeeCategory feeCategory = FeeCategory.builder()
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .build();

        FeeCategory savedCategory = feeCategoryRepository.save(feeCategory);
        log.info("Successfully created Fee Category with ID: {}", savedCategory.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_FEE_CATEGORY,
                savedCategory.getId(),
                "Created Fee Category: " + savedCategory.getNameEn() // Log English name
        );

        return mapToDto(savedCategory);
    }

    @Override
    @Transactional(readOnly = true)
    public FeeCategoryDto getFeeCategoryById(UUID id) {
        log.debug("Fetching Fee Category by ID: {}", id);
        FeeCategory category = feeCategoryRepository.findById(id)
                .orElseThrow(() -> new FeeCategoryNotFoundException(id));
        return mapToDto(category);
    }

    // Removed getFeeCategoryByName, getAllFeeCategories() and getAllFeeCategories(Pageable pageable)

    @Override
    @Transactional(readOnly = true)
    public Page<FeeCategoryDto> searchFeeCategories(String searchTerm, Pageable pageable) {
        Page<FeeCategory> categoryPage;
        // Use the updated repository method which handles null/blank search term internally
        log.debug("Searching Fee Categories page: {}, size: {}, searchTerm: '{}'", pageable.getPageNumber(), pageable.getPageSize(), searchTerm);
        categoryPage = feeCategoryRepository.findByNameEnOrNameArContainingIgnoreCase(searchTerm, pageable);
        return categoryPage.map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<SimpleFeeCategoryDto> getAllFeeCategoriesSimple() {
        log.debug("Fetching all Fee Categories (simple view)");
        // Consider adding default sorting if needed, e.g., Sort.by("nameEn")
        List<FeeCategory> categories = feeCategoryRepository.findAll();
        return categories.stream()
                .map(this::mapToSimpleDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public FeeCategoryDto updateFeeCategory(UUID id, UpdateFeeCategoryRequest request) {
        log.info("Updating Fee Category with ID: {}", id);
        FeeCategory category = feeCategoryRepository.findById(id)
                .orElseThrow(() -> new FeeCategoryNotFoundException(id));

        // Check if English name is being changed and if the new name already exists
        if (!category.getNameEn().equalsIgnoreCase(request.getNameEn())) {
            feeCategoryRepository.findByNameEnIgnoreCase(request.getNameEn()).ifPresent(fc -> { // Check by English name
                 if (!fc.getId().equals(id)) { // Ensure it's not the same category
                    throw new IllegalArgumentException("Fee Category with English name '" + request.getNameEn() + "' already exists.");
                 }
            });
        }
        // TODO: Consider adding a similar check for nameAr if it also needs to be unique

        String oldDetails = String.format("NameEn: %s, DescEn: %s", category.getNameEn(), category.getDescriptionEn()); // Log English fields

        category.setNameEn(request.getNameEn());
        category.setNameAr(request.getNameAr());
        category.setDescriptionEn(request.getDescriptionEn());
        category.setDescriptionAr(request.getDescriptionAr());

        FeeCategory updatedCategory = feeCategoryRepository.save(category);
        log.info("Successfully updated Fee Category with ID: {}", updatedCategory.getId());

        String newDetails = String.format("NameEn: %s, DescEn: %s", updatedCategory.getNameEn(), updatedCategory.getDescriptionEn()); // Log English fields

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_FEE_CATEGORY,
                updatedCategory.getId(),
                "Updated Fee Category. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedCategory);
    }

    @Override
    @Transactional
    public void deleteFeeCategory(UUID id) {
        log.warn("Attempting to delete Fee Category with ID: {}", id);
        FeeCategory category = feeCategoryRepository.findById(id)
                .orElseThrow(() -> new FeeCategoryNotFoundException(id));

        // Check if any Fees are associated with this category
        // Need a method like `existsByFeeCategoryId` in FeeRepository
        // Example: if (feeRepository.existsByFeeCategoryId(id)) {
        //     throw new IllegalStateException("Cannot delete Fee Category with associated Fees. ID: " + id);
        // }
        // For now, we'll log a warning if the check isn't implemented.
        // log.warn("Deletion check for associated Fees in FeeCategory {} is not fully implemented.", id); // Comment out the warning

        // Check if any Fees are associated with this category
        boolean hasFees = feeRepository.existsByFeeCategoryId(id); // Use FeeRepository to check
        if (hasFees) {
             log.error("Cannot delete Fee Category {} because it has associated Fees.", id);
             throw new IllegalStateException("Cannot delete Fee Category with associated Fees. ID: " + id);
        }

        feeCategoryRepository.delete(category);
        log.warn("Successfully deleted Fee Category with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_FEE_CATEGORY,
                id,
                "Deleted Fee Category: " + category.getNameEn() // Log English name
        );
    }

    // --- Helper Methods ---

    private FeeCategoryDto mapToDto(FeeCategory category) {
        if (category == null) return null;
        return FeeCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn())
                .nameAr(category.getNameAr())
                .descriptionEn(category.getDescriptionEn())
                .descriptionAr(category.getDescriptionAr())
                .createdDate(category.getCreatedDate())
                .lastModifiedDate(category.getLastModifiedDate())
                .build();
    }

    // Helper to map to Simple DTO
    private SimpleFeeCategoryDto mapToSimpleDto(FeeCategory category) {
        if (category == null) return null;
        return SimpleFeeCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn())
                .nameAr(category.getNameAr())
                .build();
    }

    private UUID getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated() || authentication.getPrincipal() == null) {
            log.error("Cannot determine current user for audit logging. No authenticated principal found.");
            // Depending on requirements, you might throw an exception or return a default system user ID.
            // Throwing an exception is safer if a user context is always required.
            throw new IllegalStateException("Authenticated user context not found for audit logging.");
        }

        Object principal = authentication.getPrincipal();

        if (principal instanceof User user) { // Check if the principal is your User entity instance
            return user.getId();
        } else if (principal instanceof UserDetails userDetails) {
            // Fallback if principal is UserDetails but not your User entity (less likely with custom User)
            // This might require fetching the User entity based on username if ID isn't directly available
            log.warn("Principal is UserDetails but not the expected User entity type. Attempting to log username: {}", userDetails.getUsername());
            // Returning null or throwing here depends on whether you can map username back to UUID easily/reliably
            // For now, let's throw, as we expect our User entity.
             throw new IllegalStateException("Principal is not an instance of the expected User class.");
        } else if (principal instanceof String && "anonymousUser".equals(principal)) {
             log.error("Attempting to perform audited action as anonymous user.");
             throw new IllegalStateException("Anonymous user cannot perform this audited action.");
        } else {
            log.error("Unexpected principal type found in SecurityContext: {}", principal.getClass().getName());
            throw new IllegalStateException("Unexpected principal type for audit logging.");
        }
    }
}
