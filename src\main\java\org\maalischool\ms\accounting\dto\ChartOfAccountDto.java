package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AccountingCategory;

import java.time.Instant;
import java.util.UUID;

@Data // Ensures getters/setters/etc.
@Builder
public class ChartOfAccountDto {
    private UUID id;
    private String accountNumber;
    private String nameEn;
    private String nameAr;
    private String descriptionEn;
    private String descriptionAr;
    private AccountingCategory category;
    private boolean active;
    private SimpleChartOfAccountDto parentAccount;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
