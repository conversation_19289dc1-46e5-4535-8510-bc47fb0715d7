package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.CreateDiscountRequest;
import org.maalischool.ms.accounting.dto.DiscountDto;
import org.maalischool.ms.accounting.dto.SimpleDiscountDto; // Assuming this exists for mapping
import org.maalischool.ms.accounting.dto.UpdateDiscountRequest;
import org.maalischool.ms.accounting.exception.DiscountNotFoundException;
import org.maalischool.ms.accounting.model.Discount;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.repository.DiscountRepository;
import org.maalischool.ms.accounting.repository.StudentFeeDiscountRepository; // Needed to check assignments
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.DiscountService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class DiscountServiceImpl implements DiscountService {

    private static final Logger log = LoggerFactory.getLogger(DiscountServiceImpl.class);
    private final DiscountRepository discountRepository;
    private final StudentFeeDiscountRepository studentFeeDiscountRepository; // Inject repository to check usage
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public DiscountDto createDiscount(CreateDiscountRequest request) {
        log.info("Creating new Discount: Code={}, Type={}, Value={}", request.getCode(), request.getType(), request.getValue());

        // Optional: Check for duplicate code
        discountRepository.findByCodeIgnoreCase(request.getCode()).ifPresent(d -> { // Corrected variable name
            throw new IllegalArgumentException("Discount with code '" + request.getCode() + "' already exists.");
        });

        // Initialize the builder
        Discount.DiscountBuilder discountBuilder = Discount.builder()
                .code(request.getCode())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .type(request.getType());

        // Set percentage or fixedAmount based on type
        if (request.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE) {
            discountBuilder.percentage(request.getValue());
        } else { // Assuming FIXED_AMOUNT is the other type
            discountBuilder.fixedAmount(request.getValue());
        }

        // Build the final Discount object
        Discount discount = discountBuilder.active(true) // Default to active
                .build();

        Discount savedDiscount = discountRepository.save(discount);
        log.info("Successfully created Discount with ID: {}", savedDiscount.getId());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_DISCOUNT,
                savedDiscount.getId(),
                String.format("Created Discount: %s (%s) - Type: %s, Value: %s",
                        savedDiscount.getCode(), savedDiscount.getDescriptionEn(), // Log English description
                        savedDiscount.getType(),
                        // Get correct value based on type for logging
                        (savedDiscount.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE ? savedDiscount.getPercentage() : savedDiscount.getFixedAmount()))
        );

        return mapToDto(savedDiscount);
    }

    @Override
    @Transactional(readOnly = true)
    public DiscountDto getDiscountById(UUID id) {
        log.debug("Fetching Discount by ID: {}", id);
        Discount discount = findDiscountOrThrow(id);
        return mapToDto(discount);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DiscountDto> getAllDiscounts(Pageable pageable) {
        log.debug("Fetching Discounts page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return discountRepository.findAll(pageable).map(this::mapToDto);
    }

     @Override
    @Transactional(readOnly = true)
    public List<DiscountDto> getAllActiveDiscounts() {
        log.debug("Fetching all active Discounts");
        return discountRepository.findByActiveTrueOrderByCodeAsc()
                .stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional
    public DiscountDto updateDiscount(UUID id, UpdateDiscountRequest request) {
        log.info("Updating Discount with ID: {}", id);
        Discount discount = findDiscountOrThrow(id);

        // Optional: Check for duplicate code if changed
        if (!discount.getCode().equalsIgnoreCase(request.getCode())) {
            discountRepository.findByCodeIgnoreCase(request.getCode()).ifPresent(d -> {
                if (!d.getId().equals(id)) {
                    throw new IllegalArgumentException("Another Discount with code '" + request.getCode() + "' already exists.");
                }
            });
        }

        // Get old value for logging
        String oldValueStr = (discount.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE ? discount.getPercentage() : discount.getFixedAmount()).toString();
        String oldDetails = String.format("Code: %s, DescEn: %s, Type: %s, Value: %s, Active: %s", // Log English desc
                                          discount.getCode(), discount.getDescriptionEn(), discount.getType(), oldValueStr, discount.isActive());

        discount.setCode(request.getCode());
        discount.setDescriptionEn(request.getDescriptionEn());
        discount.setDescriptionAr(request.getDescriptionAr());
        discount.setType(request.getType());
        // Set percentage or fixedAmount based on type, clearing the other
        if (request.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE) {
            discount.setPercentage(request.getValue());
            discount.setFixedAmount(null); // Clear fixed amount
        } else { // Assuming FIXED_AMOUNT
            discount.setFixedAmount(request.getValue());
            discount.setPercentage(null); // Clear percentage
        }
        discount.setActive(request.getActive()); // Allow updating active status here

        Discount updatedDiscount = discountRepository.save(discount);
        log.info("Successfully updated Discount with ID: {}", updatedDiscount.getId());

        // Get new value for logging
        String newValueStr = (updatedDiscount.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE ? updatedDiscount.getPercentage() : updatedDiscount.getFixedAmount()).toString();
        String newDetails = String.format("Code: %s, DescEn: %s, Type: %s, Value: %s, Active: %s", // Log English desc
                                          updatedDiscount.getCode(), updatedDiscount.getDescriptionEn(), updatedDiscount.getType(), newValueStr, updatedDiscount.isActive());

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_DISCOUNT,
                updatedDiscount.getId(),
                "Updated Discount. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedDiscount);
    }

    @Override
    @Transactional
    public void deleteDiscount(UUID id) {
        log.warn("Attempting to delete Discount with ID: {}", id);
        Discount discount = findDiscountOrThrow(id);

        // Check if this discount is assigned to any student fee
        // boolean isAssigned = studentFeeDiscountRepository.existsByDiscountId(id); // Assuming this method exists
        // if (isAssigned) {
        //     throw new IllegalStateException("Cannot delete Discount that has been assigned to student fees. ID: " + id);
        // }
        log.warn("Deletion check for associated StudentFeeDiscounts for Discount {} is not fully implemented.", id);


        discountRepository.delete(discount);
        log.warn("Successfully deleted Discount with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_DISCOUNT,
                id,
                String.format("Deleted Discount: %s", discount.getCode())
        );
    }

    @Override
    @Transactional
    public DiscountDto activateDiscount(UUID id) {
        log.info("Activating Discount with ID: {}", id);
        return updateDiscountStatus(id, true);
    }

    @Override
    @Transactional
    public DiscountDto deactivateDiscount(UUID id) {
        log.info("Deactivating Discount with ID: {}", id);
        return updateDiscountStatus(id, false);
    }

    private DiscountDto updateDiscountStatus(UUID id, boolean active) {
        Discount discount = findDiscountOrThrow(id);

        if (discount.isActive() == active) {
            log.warn("Discount {} already has active status {}", id, active);
            return mapToDto(discount);
        }

        boolean oldStatus = discount.isActive();
        discount.setActive(active);
        Discount updatedDiscount = discountRepository.save(discount);
        log.info("Successfully set active status to {} for Discount ID: {}", active, id);

        auditLogService.logAction(
                getCurrentUserId(),
                active ? AccountingActionType.ACTIVATE : AccountingActionType.DEACTIVATE,
                AccountingConstants.ENTITY_DISCOUNT,
                updatedDiscount.getId(),
                String.format("Changed active status from %s to %s for Discount %s", oldStatus, active, updatedDiscount.getCode())
        );

        return mapToDto(updatedDiscount);
    }

    private Discount findDiscountOrThrow(UUID id) {
        return discountRepository.findById(id)
                .orElseThrow(() -> new DiscountNotFoundException(id));
    }

    private DiscountDto mapToDto(Discount discount) {
        if (discount == null) return null;
        DiscountDto.DiscountDtoBuilder dtoBuilder = DiscountDto.builder() // Initialize builder
                .id(discount.getId())
                .code(discount.getCode())
                .descriptionEn(discount.getDescriptionEn())
                .descriptionAr(discount.getDescriptionAr())
                .type(discount.getType()); // Keep semicolon here, dtoBuilder is initialized

        // Get the correct value based on type and set it on the builder
        if (discount.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE) {
            dtoBuilder.value(discount.getPercentage());
        } else {
            dtoBuilder.value(discount.getFixedAmount());
        }

        // Explicitly chain the remaining steps
        dtoBuilder = dtoBuilder.active(discount.isActive());
        dtoBuilder = dtoBuilder.createdDate(discount.getCreatedDate());
        dtoBuilder = dtoBuilder.lastModifiedDate(discount.getLastModifiedDate());

        // Build at the end
        return dtoBuilder.build();
    }

    // Helper to map to SimpleDiscountDto if needed elsewhere
    private SimpleDiscountDto mapToSimpleDto(Discount discount) {
         if (discount == null) return null;
         SimpleDiscountDto.SimpleDiscountDtoBuilder simpleDtoBuilder = SimpleDiscountDto.builder() // Initialize builder
                 .id(discount.getId())
                 .code(discount.getCode())
                 .type(discount.getType()); // Keep semicolon here

         // Get correct value based on type and set it on the builder
         if (discount.getType() == org.maalischool.ms.accounting.model.enums.DiscountType.PERCENTAGE) {
             simpleDtoBuilder.value(discount.getPercentage());
         } else {
             simpleDtoBuilder.value(discount.getFixedAmount());
         }

         // Build at the end (already explicit here, no change needed but keeping for consistency)
         return simpleDtoBuilder.build();
    }


    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("********-0000-0000-0000-********0000");
    }
}
