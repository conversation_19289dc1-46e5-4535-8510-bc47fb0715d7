package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class IncomeStatementDto {
    private LocalDate startDate;
    private LocalDate endDate;
    private List<AccountBalanceDto> incomeAccounts;
    private List<AccountBalanceDto> expenseAccounts;
    private BigDecimal totalRevenue;
    private BigDecimal totalExpenses;
    private BigDecimal netIncome;
}