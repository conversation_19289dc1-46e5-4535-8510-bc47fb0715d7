package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Receipt;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ReceiptRepository extends JpaRepository<Receipt, UUID> {

    Optional<Receipt> findByReceiptNumber(String receiptNumber);

    List<Receipt> findByReceiptDateBetweenOrderByReceiptDateDesc(LocalDate startDate, LocalDate endDate);

    Page<Receipt> findByReceiptDateBetweenOrderByReceiptDateDesc(LocalDate startDate, LocalDate endDate, Pageable pageable);

    List<Receipt> findByStudentIdOrderByReceiptDateDesc(UUID studentId);

    Page<Receipt> findByStudentIdOrderByReceiptDateDesc(UUID studentId, Pageable pageable);

    Page<Receipt> findByPaymentMethodIdOrderByReceiptDateDesc(UUID paymentMethodId, Pageable pageable); // Add Pageable and return Page
}
