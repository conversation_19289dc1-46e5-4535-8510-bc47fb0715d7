package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.StudentFee;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface StudentFeeRepository extends JpaRepository<StudentFee, UUID> {

    // Renamed method to correctly traverse the 'fee' relationship for
    // 'academicYear' and 'feeId'
    boolean existsByStudentIdAndFee_IdAndFee_AcademicYear_Id(UUID studentId, UUID feeId, UUID academicYearId);

    @Query("SELECT sf.fee.id FROM StudentFee sf WHERE sf.student.id = :studentId AND sf.fee.academicYear.id = :academicYearId")
    Set<UUID> findFeeIdsByStudentIdAndAcademicYear(@Param("studentId") UUID studentId,
            @Param("academicYearId") UUID academicYearId);

    @Query("SELECT sf FROM StudentFee sf WHERE sf.student.id = :studentId AND sf.fee.academicYear.id = :academicYearId ORDER BY sf.fee.dueDate ASC")
    Page<StudentFee> findByStudentIdAndAcademicYearOrderByFeeDueDateAsc(@Param("studentId") UUID studentId,
            @Param("academicYearId") UUID academicYearId, Pageable pageable);

    @Query("SELECT sf FROM StudentFee sf WHERE sf.fee.academicYear.id = :academicYearId AND sf.status = :status ORDER BY sf.fee.dueDate ASC")
    Page<StudentFee> findByAcademicYearAndStatusOrderByFeeDueDateAsc(@Param("academicYearId") UUID academicYearId,
            @Param("status") FeeStatus status, Pageable pageable);

    List<StudentFee> findByStudentIdOrderByDueDateAsc(UUID studentId);

    Page<StudentFee> findByStudentIdOrderByDueDateAsc(UUID studentId, Pageable pageable);

    List<StudentFee> findByStudentIdAndStatusOrderByDueDateAsc(UUID studentId, FeeStatus status);

    List<StudentFee> findByFeeId(UUID feeId);

    // Find unpaid/partially paid fees for a student
    List<StudentFee> findByStudentIdAndStatusInOrderByDueDateAsc(UUID studentId, List<FeeStatus> statuses);

    // Find overdue fees (added based on service needs)
    List<StudentFee> findByStatusInAndDueDateBeforeOrderByDueDateAsc(List<FeeStatus> statuses, LocalDate date);

    // Check if a specific fee has already been assigned to a student for a given
    // period (e.g., academic year via Fee)
    Optional<StudentFee> findByStudentIdAndFeeId(UUID studentId, UUID feeId);

    // Calculate total amount due for a student
    @Query("SELECT COALESCE(SUM(sf.amountDue - sf.amountPaid), 0) FROM StudentFee sf " +
            "WHERE sf.student.id = :studentId AND sf.status IN :statuses")
    BigDecimal calculateTotalOutstandingForStudent(
            @Param("studentId") UUID studentId,
            @Param("statuses") List<FeeStatus> statuses); // Typically UNPAID, PARTIALLY_PAID, OVERDUE

    boolean existsByFeeId(UUID feeId); // Needed for Fee deletion check

    // Find student fees by fee ID, ordered by student ID (Added to fix compilation
    // error)
    Page<StudentFee> findByFeeIdOrderByStudentIdAsc(UUID feeId, Pageable pageable);

    // Find overdue fees (added based on service needs)
    // List<StudentFee>
    // findByStatusInAndDueDateBeforeOrderByDueDateAsc(List<FeeStatus> statuses,
    // LocalDate date); // Duplicate removed
}
