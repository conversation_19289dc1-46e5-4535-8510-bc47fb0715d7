package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.maalischool.ms.student.dto.SimpleStudentDto; // Assuming this exists

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List; // Add import for List
import java.util.UUID;

@Data
@Builder
public class StudentFeeDto {
    private UUID id;
    private SimpleStudentDto student; // Use Simple DTO
    private SimpleFeeDto fee;         // Use Simple DTO
    private LocalDate dueDate;
    private BigDecimal amount;        // Original assigned amount
    private BigDecimal amountPaid;
    private BigDecimal amountDue;     // Calculated: amount - amountPaid - discountAmount
    // private SimpleDiscountDto discount; // Remove single discount field if using a list
    private List<SimpleDiscountDto> appliedDiscounts; // Add list of applied discounts
    private BigDecimal discountAmount; // Total discount amount
    private FeeStatus status;
    private String notesEn;
    private String notesAr;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
