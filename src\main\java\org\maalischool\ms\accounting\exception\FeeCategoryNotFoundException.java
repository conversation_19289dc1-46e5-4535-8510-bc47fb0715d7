package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class FeeCategoryNotFoundException extends RuntimeException {

    public FeeCategoryNotFoundException(UUID id) {
        super("Fee Category not found with ID: " + id);
    }

    public FeeCategoryNotFoundException(String name) {
        super("Fee Category not found with name: " + name);
    }
}
