package org.maalischool.ms.auth.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.CreatePermissionRequest;
import org.maalischool.ms.auth.dto.PermissionDto;
import org.maalischool.ms.auth.dto.UpdatePermissionRequest;
import org.maalischool.ms.auth.exception.PermissionNotFoundException;
import org.maalischool.ms.auth.model.Permission;
import org.maalischool.ms.auth.repository.PermissionRepository;
import org.maalischool.ms.auth.service.PermissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements PermissionService {

    private static final Logger log = LoggerFactory.getLogger(PermissionServiceImpl.class);
    private final PermissionRepository permissionRepository;

    @Override
    public Optional<Permission> findByName(String name) {
        return permissionRepository.findByName(name);
    }

    @Override
    @Transactional // Ensure atomicity if creation happens
    public Permission findOrCreatePermission(String name, String description) {
        return permissionRepository.findByName(name)
                .orElseGet(() -> {
                    log.info("Permission '{}' not found, creating new one.", name);
                    Permission newPermission = new Permission();
                    newPermission.setName(name);
                    newPermission.setDescription(description);
                    return permissionRepository.save(newPermission);
                });
    }

    @Override
    @Transactional
    public PermissionDto createPermission(CreatePermissionRequest request) {
        if (permissionRepository.findByName(request.getName()).isPresent()) {
            // Let DataIntegrityViolationException handler catch this for unique constraint
            // Or throw a specific exception here if preferred
            log.warn("Attempted to create permission with existing name: {}", request.getName());
        }
        Permission permission = new Permission();
        permission.setName(request.getName());
        permission.setDescription(request.getDescription());
        Permission savedPermission = permissionRepository.save(permission);
        log.info("Created permission '{}' with ID {}", savedPermission.getName(), savedPermission.getId());
        return mapToDto(savedPermission);
    }

    @Override
    @Transactional(readOnly = true)
    public List<PermissionDto> findAllPermissions() {
        return permissionRepository.findAll().stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PermissionDto> findPermissionById(UUID id) {
        return permissionRepository.findById(id).map(this::mapToDto);
    }

    @Override
    @Transactional
    public PermissionDto updatePermission(UUID id, UpdatePermissionRequest request) {
        Permission permission = permissionRepository.findById(id)
                .orElseThrow(() -> new PermissionNotFoundException("Permission not found with ID: " + id));

        boolean updated = false;
        if (StringUtils.hasText(request.getName()) && !request.getName().equals(permission.getName())) {
             if (permissionRepository.findByName(request.getName()).isPresent()) {
                // Let DataIntegrityViolationException handler catch this for unique constraint
                 log.warn("Attempted to update permission {} to existing name: {}", id, request.getName());
             }
            permission.setName(request.getName());
            updated = true;
        }
        if (request.getDescription() != null && !request.getDescription().equals(permission.getDescription())) {
            permission.setDescription(request.getDescription());
            updated = true;
        }

        if (updated) {
            Permission updatedPermission = permissionRepository.save(permission);
            log.info("Updated permission '{}' with ID {}", updatedPermission.getName(), updatedPermission.getId());
            return mapToDto(updatedPermission);
        } else {
             log.info("No changes detected for permission with ID {}", id);
            return mapToDto(permission); // Return current state if no changes
        }
    }

    @Override
    @Transactional
    public void deletePermission(UUID id) {
        if (!permissionRepository.existsById(id)) {
            throw new PermissionNotFoundException("Permission not found with ID: " + id);
        }
        // Consider checking if permission is in use by roles before deleting
        log.warn("Deleting permission with ID: {}", id); // Log deletion
        permissionRepository.deleteById(id);
    }

    // --- Mapper ---
    private PermissionDto mapToDto(Permission permission) {
        return PermissionDto.builder()
                .id(permission.getId())
                .name(permission.getName())
                .description(permission.getDescription())
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getUpdatedAt())
                .build();
    }
}
