package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.time.Instant;
import java.util.UUID;

@Data
@Builder
public class ExpenseCategoryDto {
    private UUID id;
    private String nameEn;
    private String nameAr;
    private String descriptionEn;
    private String descriptionAr;
    private SimpleExpenseCategoryDto parentCategory; // Use Simple DTO
    private SimpleChartOfAccountDto expenseAccount; // Add mandatory expense account
    private Instant createdDate;
    private Instant lastModifiedDate;
}
