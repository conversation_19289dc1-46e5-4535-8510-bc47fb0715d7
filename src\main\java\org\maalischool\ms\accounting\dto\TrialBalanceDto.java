package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;

public class TrialBalanceDto {

    private LocalDate startDate;
    private LocalDate endDate;
    private List<TrialBalanceAccountDto> accounts;
    private BigDecimal totalDebits;
    private BigDecimal totalCredits;

    public TrialBalanceDto() {
    }

    public TrialBalanceDto(LocalDate startDate, LocalDate endDate, List<TrialBalanceAccountDto> accounts, BigDecimal totalDebits, BigDecimal totalCredits) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.accounts = accounts;
        this.totalDebits = totalDebits;
        this.totalCredits = totalCredits;
    }

    public LocalDate getStartDate() {
        return startDate;
    }

    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }

    public LocalDate getEndDate() {
        return endDate;
    }

    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }

    public List<TrialBalanceAccountDto> getAccounts() {
        return accounts;
    }

    public void setAccounts(List<TrialBalanceAccountDto> accounts) {
        this.accounts = accounts;
    }

    public BigDecimal getTotalDebits() {
        return totalDebits;
    }

    public void setTotalDebits(BigDecimal totalDebits) {
        this.totalDebits = totalDebits;
    }

    public BigDecimal getTotalCredits() {
        return totalCredits;
    }

    public void setTotalCredits(BigDecimal totalCredits) {
        this.totalCredits = totalCredits;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TrialBalanceDto that = (TrialBalanceDto) o;
        return Objects.equals(startDate, that.startDate) && Objects.equals(endDate, that.endDate) && Objects.equals(accounts, that.accounts) && Objects.equals(totalDebits, that.totalDebits) && Objects.equals(totalCredits, that.totalCredits);
    }

    @Override
    public int hashCode() {
        return Objects.hash(startDate, endDate, accounts, totalDebits, totalCredits);
    }

    @Override
    public String toString() {
        return "TrialBalanceDto{" +
               "startDate=" + startDate +
               ", endDate=" + endDate +
               ", accounts=" + accounts +
               ", totalDebits=" + totalDebits +
               ", totalCredits=" + totalCredits +
               '}';
    }
}
