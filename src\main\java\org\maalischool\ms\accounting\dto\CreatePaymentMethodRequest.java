package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.PaymentMethodType;

@Data
@Builder
public class CreatePaymentMethodRequest {
    @NotBlank(message = "English payment method name cannot be blank")
    @Size(max = 100, message = "English payment method name must be less than 100 characters")
    private String nameEn;

    @NotBlank(message = "Arabic payment method name cannot be blank")
    @Size(max = 100, message = "Arabic payment method name must be less than 100 characters")
    private String nameAr;

    @NotNull(message = "Payment method type cannot be null")
    private PaymentMethodType type;

    @Size(max = 500, message = "English description must be less than 500 characters")
    private String descriptionEn; // e.g., Account number for bank transfer

    @Size(max = 500, message = "Arabic description must be less than 500 characters")
    private String descriptionAr;
}
