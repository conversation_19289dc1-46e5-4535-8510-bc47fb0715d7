package org.maalischool.ms.auth.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RoleDto {
    private UUID id;
    private String name;
    private String description;
    private List<PermissionDto> permissions; // Include permissions in the Role DTO
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
