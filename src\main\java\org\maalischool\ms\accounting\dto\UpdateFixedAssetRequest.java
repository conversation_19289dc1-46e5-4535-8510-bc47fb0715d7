package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import org.maalischool.ms.accounting.model.enums.DepreciationMethod;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class UpdateFixedAssetRequest {
    // Fields typically updatable after acquisition
    @NotBlank(message = "English asset name cannot be blank")
    @Size(max = 255, message = "English asset name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic asset name cannot be blank")
    @Size(max = 255, message = "Arabic asset name must be less than 255 characters")
    private String nameAr;

    @NotBlank(message = "Asset tag / code cannot be blank")
    @Size(max = 50, message = "Asset tag / code must be less than 100 characters")
    private String assetCode;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    // Acquisition details are usually fixed, but some might be correctable
    // private LocalDate acquisitionDate;
    // private BigDecimal acquisitionCost;

    // Account links might change if CoA is restructured, but carefully
    private UUID assetAccountId;
    private UUID accumulatedDepreciationAccountId;
    private UUID depreciationExpenseAccountId;

    // Depreciation parameters might be adjusted based on reassessment
    private Integer usefulLifeYears;
    private BigDecimal salvageValue;
    private DepreciationMethod depreciationMethod;

    @Size(max = 255, message = "Location must be less than 255 characters")
    private String location;

//    @Size(max = 100, message = "Serial number must be less than 100 characters")
//    private String serialNumber;

    @NotNull(message = "Status cannot be null")
    private AssetStatus status;

    private LocalDate disposalDate; // Set when disposed
    private BigDecimal disposalValue; // Set when disposed
}
