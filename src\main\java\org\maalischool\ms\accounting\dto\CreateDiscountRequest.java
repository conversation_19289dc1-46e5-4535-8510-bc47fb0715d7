package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.DiscountType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class CreateDiscountRequest {
    @NotBlank(message = "Discount code cannot be blank")
    @Size(max = 50, message = "Discount code must be less than 50 characters")
    private String code;

    @NotBlank(message = "English description cannot be blank")
    @Size(max = 255, message = "English description must be less than 255 characters")
    private String descriptionEn;

    @NotBlank(message = "Arabic description cannot be blank")
    @Size(max = 255, message = "Arabic description must be less than 255 characters")
    private String descriptionAr;

    @NotNull(message = "Discount type cannot be null")
    private DiscountType type;

    @NotNull(message = "Value cannot be null")
    @DecimalMin(value = "0.0", message = "Discount value must be non-negative")
    private BigDecimal value; // Percentage or fixed amount based on type

    private LocalDate validFrom;
    private LocalDate validUntil;

    // Optional applicability constraints
    private UUID applicableFeeId;
    private UUID applicableStudentId;
}
