package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class StudentFeeNotFoundException extends RuntimeException {

    public StudentFeeNotFoundException(UUID id) {
        super("Student Fee assignment not found with ID: " + id);
    }

     public StudentFeeNotFoundException(String message) {
        super(message);
    }

     public StudentFeeNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
