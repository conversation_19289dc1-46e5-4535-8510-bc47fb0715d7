package org.maalischool.ms.auth.service;

import org.maalischool.ms.auth.dto.CreatePermissionRequest;
import org.maalischool.ms.auth.dto.PermissionDto;
import org.maalischool.ms.auth.dto.UpdatePermissionRequest;
import org.maalischool.ms.auth.model.Permission;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface PermissionService {
    /**
     * Finds a permission by its name.
     *
     * @param name The name of the permission.
     * @return An Optional containing the Permission if found, otherwise empty.
     */
    Optional<Permission> findByName(String name);

    /**
     * Finds a permission by name, or creates it if it doesn't exist.
     *
     * @param name The name of the permission.
     * @param description A description for the permission if created.
     * @return The existing or newly created Permission.
     */
    Permission findOrCreatePermission(String name, String description);

    /**
     * Creates a new permission.
     *
     * @param request DTO containing permission details.
     * @return DTO of the created permission.
     * @throws org.springframework.dao.DataIntegrityViolationException if name already exists.
     */
    PermissionDto createPermission(CreatePermissionRequest request);

    /**
     * Retrieves all permissions.
     *
     * @return List of all permission DTOs.
     */
    List<PermissionDto> findAllPermissions();

    /**
     * Finds a permission by its ID.
     *
     * @param id The UUID of the permission.
     * @return An Optional containing the Permission DTO if found.
     */
    Optional<PermissionDto> findPermissionById(UUID id);

    /**
     * Updates an existing permission.
     *
     * @param id The UUID of the permission to update.
     * @param request DTO containing updated details (null fields are ignored).
     * @return DTO of the updated permission.
     * @throws PermissionNotFoundException if permission with id is not found.
     * @throws org.springframework.dao.DataIntegrityViolationException if new name conflicts.
     */
    PermissionDto updatePermission(UUID id, UpdatePermissionRequest request);

    /**
     * Deletes a permission by its ID.
     *
     * @param id The UUID of the permission to delete.
     * @throws PermissionNotFoundException if permission with id is not found.
     */
    void deletePermission(UUID id);
}
