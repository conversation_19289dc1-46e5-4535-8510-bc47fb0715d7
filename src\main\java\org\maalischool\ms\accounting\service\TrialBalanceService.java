package org.maalischool.ms.accounting.service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

import org.maalischool.ms.accounting.dto.TrialBalanceAccountDto;
import org.maalischool.ms.accounting.dto.TrialBalanceDto;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.JournalEntryLineRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class TrialBalanceService {

    private final ChartOfAccountRepository chartOfAccountRepository;
    private final JournalEntryLineRepository journalEntryLineRepository;

    @Autowired
    public TrialBalanceService(ChartOfAccountRepository chartOfAccountRepository, JournalEntryLineRepository journalEntryLineRepository) {
        this.chartOfAccountRepository = chartOfAccountRepository;
        this.journalEntryLineRepository = journalEntryLineRepository;
    }

    public TrialBalanceDto generateTrialBalance(LocalDate startDate, LocalDate endDate) {
        List<ChartOfAccount> accounts = chartOfAccountRepository.findAll();
        List<TrialBalanceAccountDto> accountBalances = new ArrayList<>();
        BigDecimal totalDebits = BigDecimal.ZERO;
        BigDecimal totalCredits = BigDecimal.ZERO;

        for (ChartOfAccount account : accounts) {
            BigDecimal debits = journalEntryLineRepository.calculateTotalDebitsBetweenDates(account.getId(), startDate, endDate);
            BigDecimal credits = journalEntryLineRepository.calculateTotalCreditsBetweenDates(account.getId(), startDate, endDate);

            accountBalances.add(new TrialBalanceAccountDto(
                    account.getAccountNumber(),
                    account.getNameEn(), // Use getNameEn() as getAccountName() does not exist
                    debits,
                    credits
            ));

            totalDebits = totalDebits.add(debits);
            totalCredits = totalCredits.add(credits);
        }

        return new TrialBalanceDto(startDate, endDate, accountBalances, totalDebits, totalCredits);
    }
}
