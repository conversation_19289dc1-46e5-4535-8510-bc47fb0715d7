package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "acc_fixed_assets", uniqueConstraints = {
        @UniqueConstraint(columnNames = "asset_code")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class FixedAsset {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "asset_code", nullable = false, unique = true, length = 50)
    private String assetCode;

    @Column(nullable = false, length = 255)
    private String nameEn;
    @Column(nullable = false, length = 255)
    private String nameAr;

    @Column(length = 1000)
    private String descriptionEn;
    @Column(length = 1000)
    private String descriptionAr;

    @Column(name = "purchase_date", nullable = false)
    private LocalDate purchaseDate;

    @Column(name = "purchase_cost", nullable = false, precision = 19, scale = 4)
    private BigDecimal purchaseCost;

    @Column(name = "depreciation_method", length = 50) // e.g., "Straight Line", "Declining Balance"
    private String depreciationMethod;

    @Column(name = "useful_life_years")
    private Integer usefulLifeYears;

    @Column(name = "salvage_value", precision = 19, scale = 4)
    @Builder.Default
    private BigDecimal salvageValue = BigDecimal.ZERO;

    // Optional: Store calculated current book value (Cost - Accumulated Depreciation)
    // This might be better calculated on the fly or updated via a batch process
    @Column(name = "current_book_value", precision = 19, scale = 4)
    private BigDecimal currentBookValue;

    @Column(length = 255)
    private String location;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    private AssetStatus status;

    // Link to ChartOfAccounts for Asset Cost and Accumulated Depreciation
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "asset_account_id")
    private ChartOfAccount assetAccount; // e.g., Furniture & Fixtures Cost

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "accumulated_depreciation_account_id")
    private ChartOfAccount accumulatedDepreciationAccount; // e.g., Acc Dep - Furniture

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "depreciation_expense_account_id")
    private ChartOfAccount depreciationExpenseAccount; // e.g., Depreciation Expense

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
