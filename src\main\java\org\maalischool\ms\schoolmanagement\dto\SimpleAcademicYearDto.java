package org.maalischool.ms.schoolmanagement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Simplified DTO for AcademicYear, used within other DTOs to avoid circular dependencies.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimpleAcademicYearDto {
    private UUID id;
    private String name; // e.g., "2024-2025"
    private LocalDate startDate;
    private LocalDate endDate;
    private boolean active;
}
