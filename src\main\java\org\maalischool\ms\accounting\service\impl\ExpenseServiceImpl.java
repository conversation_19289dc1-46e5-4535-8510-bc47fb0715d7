package org.maalischool.ms.accounting.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode; // Import RoundingMode
import java.time.LocalDate;
import java.util.ArrayList; // Import ArrayList
import java.util.List;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.CreateExpenseRequest;
import org.maalischool.ms.accounting.dto.CreateJournalEntryLineRequest;
import org.maalischool.ms.accounting.dto.CreateJournalEntryRequest;
import org.maalischool.ms.accounting.dto.ExpenseDto;
import org.maalischool.ms.accounting.dto.JournalEntryDto;
import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto;
import org.maalischool.ms.accounting.dto.SimpleExpenseCategoryDto;
import org.maalischool.ms.accounting.dto.SimpleTaxDto; // Import SimpleTaxDto
import org.maalischool.ms.accounting.dto.UpdateExpenseRequest;
// Import all exceptions from package
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException;
import org.maalischool.ms.accounting.exception.ExpenseCategoryNotFoundException;
import org.maalischool.ms.accounting.exception.ExpenseNotFoundException;
import org.maalischool.ms.accounting.exception.TaxNotFoundException;
// Import all models from package
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.Expense;
import org.maalischool.ms.accounting.model.ExpenseCategory;
import org.maalischool.ms.accounting.model.Tax;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.TransactionType;
// Import all repositories from package
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.ExpenseCategoryRepository;
import org.maalischool.ms.accounting.repository.ExpenseRepository;
import org.maalischool.ms.accounting.repository.TaxRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.ExpenseService;
import org.maalischool.ms.accounting.service.JournalEntryService; // Import JournalEntryService
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class ExpenseServiceImpl implements ExpenseService {

    private static final Logger log = LoggerFactory.getLogger(ExpenseServiceImpl.class);
    private final ExpenseRepository expenseRepository;
    private final ExpenseCategoryRepository expenseCategoryRepository;
    private final ChartOfAccountRepository chartOfAccountRepository;
    private final TaxRepository taxRepository; // Inject TaxRepository
    // private final PaymentMethodRepository paymentMethodRepository; // Removed as not directly used here anymore
    private final AccountingAuditLogService auditLogService;
    private final JournalEntryService journalEntryService;

    @Override
    @Transactional
    public ExpenseDto createExpense(CreateExpenseRequest request) {
        log.info("Creating new Expense: Date={}, Amount={}, CategoryID={}, TaxID={}",
                 request.getExpenseDate(), request.getAmount(), request.getCategoryId(), request.getTaxId());

        ExpenseCategory category = expenseCategoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new ExpenseCategoryNotFoundException(request.getCategoryId()));

        // Fetch the Payment Account (ChartOfAccount)
        ChartOfAccount paymentAccount = chartOfAccountRepository.findById(request.getPaymentAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getPaymentAccountId()));

        // Fetch Tax if ID is provided
        Tax tax = null;
        BigDecimal taxAmount = BigDecimal.ZERO; // Default to zero
        if (request.getTaxId() != null) {
            tax = taxRepository.findById(request.getTaxId())
                    .orElseThrow(() -> new TaxNotFoundException(request.getTaxId()));
            // Calculate tax amount: amount * (percent / 100)
            // Use scale 4 for intermediate calculation, then scale to match Expense.taxAmount precision (e.g., 4)
            taxAmount = request.getAmount()
                               .multiply(tax.getPercent().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP)) // Higher precision for division
                               .setScale(4, RoundingMode.HALF_UP); // Scale to match entity field precision
            log.debug("Calculated tax amount: {} for tax rate: {}", taxAmount, tax.getPercent());
        }

        Expense expense = Expense.builder()
                .expenseDate(request.getExpenseDate())
                .amount(request.getAmount()) // This is amount before tax
                .tax(tax) // Set the tax entity (can be null)
                .taxAmount(taxAmount) // Set the calculated tax amount (can be zero)
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .referenceNumber(request.getReferenceNumber())
                .category(category)
                .paymentAccount(paymentAccount)
                .vendor(request.getVendor())
                .build();

        Expense savedExpense = expenseRepository.save(expense);
        log.info("Successfully created Expense with ID: {}, Tax Amount: {}", savedExpense.getId(), savedExpense.getTaxAmount());

        // --- Create Double-Entry (or Triple-Entry with tax) Journal Entry ---
        createJournalEntryForExpense(savedExpense, category.getExpenseAccount(), paymentAccount, tax);
        // --- End Journal Entry Creation ---

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.CREATE,
                AccountingConstants.ENTITY_EXPENSE,
                savedExpense.getId(),
                String.format("Created Expense: Date: %s, Amount: %s, Category: %s, Vendor: %s, DescEn: %s", // Use Vendor and DescEn in log
                        savedExpense.getExpenseDate(), savedExpense.getAmount(), category.getNameEn(), savedExpense.getVendor(), savedExpense.getDescriptionEn()) // Use getNameEn() and getDescriptionEn()
        );

        return mapToDto(savedExpense);
    }

    private void createJournalEntryForExpense(Expense expense, ChartOfAccount expenseAccount, ChartOfAccount paymentAccount, Tax tax) {
        log.debug("Creating journal entry for Expense ID: {}", expense.getId());

        List<CreateJournalEntryLineRequest> lines = new ArrayList<>();
        String baseDescription = expense.getDescriptionEn() != null ? expense.getDescriptionEn() : "Expense";

        // 1. Debit the Expense Account (Amount Before Tax)
        lines.add(CreateJournalEntryLineRequest.builder()
                .chartOfAccountId(expenseAccount.getId())
                .amount(expense.getAmount()) // Amount before tax
                .type(TransactionType.DEBIT)
                .description("Expense: " + baseDescription)
                .build());

        BigDecimal totalAmount = expense.getAmount();

        // 2. Debit the Tax Account (if applicable and tax amount > 0)
        if (tax != null && expense.getTaxAmount() != null && expense.getTaxAmount().compareTo(BigDecimal.ZERO) > 0) {
            ChartOfAccount taxAccount = tax.getChartOfAccount();
            if (taxAccount == null) {
                // Critical error: Tax configuration is incomplete.
                log.error("Tax ID {} [{}] does not have an associated ChartOfAccount. Cannot create tax journal line.", tax.getId(), tax.getNameEn());
                // Throw an exception to halt the process and indicate a configuration problem.
                throw new IllegalStateException("Tax configuration error: Tax " + tax.getId() + " [" + tax.getNameEn() + "] is missing its ChartOfAccount link.");
            }
            log.debug("Adding tax debit line: Account={}, Amount={}", taxAccount.getId(), expense.getTaxAmount());
            lines.add(CreateJournalEntryLineRequest.builder()
                    .chartOfAccountId(taxAccount.getId())
                    .amount(expense.getTaxAmount())
                    .type(TransactionType.DEBIT) // This assumes the tax amount increases an asset (e.g., Input VAT Recoverable) or is part of the expense cost.
                                                 // If it's a tax payable liability, the credit/debit logic might differ.
                                                 // CONSULT ACCOUNTANT FOR CORRECT TAX ACCOUNTING TREATMENT.
                    .description("Tax (" + tax.getNameEn() + ") for: " + baseDescription)
                    .build());
            totalAmount = totalAmount.add(expense.getTaxAmount()); // Add tax to total amount
        }

        // 3. Credit the Payment Account (Total Amount)
        log.debug("Adding payment credit line: Account={}, Amount={}", paymentAccount.getId(), totalAmount);
        lines.add(CreateJournalEntryLineRequest.builder()
                .chartOfAccountId(paymentAccount.getId())
                .amount(totalAmount) // Total amount (expense + tax)
                .type(TransactionType.CREDIT)
                .description("Payment for: " + baseDescription)
                .build());


        CreateJournalEntryRequest journalEntryRequest = CreateJournalEntryRequest.builder()
                .entryDate(expense.getExpenseDate())
                .description("Automatic entry for Expense ID: " + expense.getId() + " - " + baseDescription)
                .referenceNumber("EXP-" + expense.getId().toString().substring(0, 8)) // Example reference
                .lines(lines) // Use the list of lines
                .build();

        try {
            JournalEntryDto createdJournalEntry = journalEntryService.createJournalEntry(journalEntryRequest);
            log.info("Successfully created Journal Entry ID: {} for Expense ID: {}", createdJournalEntry.getId(), expense.getId());
            // Optionally link the journal entry ID back to the expense if the model supports it
            // expense.setJournalEntryId(createdJournalEntry.getId()); // Example, requires Expense model change
            // expenseRepository.save(expense); // Save again if modified
        } catch (Exception e) {
            // Log the error, but don't fail the expense creation? Or rethrow as a specific exception?
            // Depending on business requirements, you might want to make expense creation transactional
            // with journal entry creation. If the journal entry fails, the expense should roll back.
            // The @Transactional on createExpense already covers this if JournalEntryService call is not in a separate transaction.
            log.error("Failed to create journal entry for Expense ID: {}. Error: {}", expense.getId(), e.getMessage(), e);
            // Consider throwing a custom exception or rethrowing e if journal entry is mandatory
            // throw new JournalEntryCreationFailedException("Failed to create journal entry for expense " + expense.getId(), e);
        }
    }


    @Override
    @Transactional(readOnly = true)
    public ExpenseDto getExpenseById(UUID id) {
        log.debug("Fetching Expense by ID: {}", id);
        Expense expense = findExpenseOrThrow(id);
        return mapToDto(expense);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ExpenseDto> getAllExpenses(Pageable pageable) {
        log.debug("Fetching Expenses page: {}, size: {}", pageable.getPageNumber(), pageable.getPageSize());
        return expenseRepository.findAll(pageable).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ExpenseDto> findExpensesByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        log.debug("Fetching Expenses between {} and {}", startDate, endDate);
        return expenseRepository.findByExpenseDateBetweenOrderByExpenseDateDesc(startDate, endDate, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ExpenseDto> findExpensesByCategory(UUID categoryId, Pageable pageable) {
        log.debug("Fetching Expenses for category ID: {}", categoryId);
        // Optional: Check if category exists first
        if (!expenseCategoryRepository.existsById(categoryId)) {
            throw new ExpenseCategoryNotFoundException(categoryId);
        }
        // Correct the method name to match the repository definition
        return expenseRepository.findByCategoryIdOrderByExpenseDateDesc(categoryId, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional
    public ExpenseDto updateExpense(UUID id, UpdateExpenseRequest request) {
        // IMPORTANT: Updating financial transactions like expenses often requires more complex logic
        // than simply overwriting fields, especially regarding posted journal entries.
        // Common approaches:
        // 1. Disallow updates after a certain state (e.g., after journal entry is posted).
        // 2. Create reversing journal entries for the old state and new entries for the updated state.
        // This implementation currently performs a simple field update on the Expense entity
        // and DOES NOT adjust the associated journal entry. This might lead to inconsistencies.
        // Consider implementing journal entry reversal/update logic if required.
        log.warn("Updating Expense with ID: {}. NOTE: Associated journal entry is NOT automatically updated.", id);
        Expense expense = findExpenseOrThrow(id);

        ExpenseCategory category = expenseCategoryRepository.findById(request.getCategoryId())
                .orElseThrow(() -> new ExpenseCategoryNotFoundException(request.getCategoryId()));

        // Fetch the Payment Account (ChartOfAccount)
        ChartOfAccount paymentAccount = chartOfAccountRepository.findById(request.getPaymentAccountId())
                .orElseThrow(() -> new ChartOfAccountNotFoundException(request.getPaymentAccountId()));

        // Fetch Tax if ID is provided in the update request
        Tax tax = null;
        BigDecimal taxAmount = BigDecimal.ZERO;
        if (request.getTaxId() != null) {
            tax = taxRepository.findById(request.getTaxId())
                    .orElseThrow(() -> new TaxNotFoundException(request.getTaxId()));
            // Recalculate tax amount based on potentially updated amount and tax rate
            taxAmount = request.getAmount()
                               .multiply(tax.getPercent().divide(BigDecimal.valueOf(100), 8, RoundingMode.HALF_UP))
                               .setScale(4, RoundingMode.HALF_UP);
            log.debug("Recalculated tax amount for update: {}", taxAmount);
        } else {
            // If taxId is null in the request, ensure tax entity and amount are cleared
            tax = null;
            taxAmount = BigDecimal.ZERO;
            log.debug("Tax association removed during update.");
        }

        String oldDetails = String.format("Date: %s, Amount: %s, TaxAmt: %s, TaxId: %s, Cat: %s, PmtAcct: %s, Vendor: %s",
                                          expense.getExpenseDate(), expense.getAmount(), expense.getTaxAmount(),
                                          expense.getTax() != null ? expense.getTax().getId() : "null",
                                          expense.getCategory().getId(), expense.getPaymentAccount().getId(), expense.getVendor());

        expense.setExpenseDate(request.getExpenseDate());
        expense.setAmount(request.getAmount()); // Amount before tax
        expense.setTax(tax); // Update tax entity association
        expense.setTaxAmount(taxAmount); // Update tax amount
        expense.setDescriptionEn(request.getDescriptionEn());
        expense.setDescriptionAr(request.getDescriptionAr());
        expense.setReferenceNumber(request.getReferenceNumber());
        expense.setCategory(category); // Use correct setter 'setCategory'
        // expense.setPaymentMethod(paymentMethod); // Remove setting PaymentMethod
        expense.setPaymentAccount(paymentAccount); // Set the paymentAccount (ChartOfAccount) - Requires Expense model update
        expense.setVendor(request.getVendor());

        Expense updatedExpense = expenseRepository.save(expense);
        log.info("Successfully updated Expense with ID: {}, New Tax Amount: {}", updatedExpense.getId(), updatedExpense.getTaxAmount());

        String newDetails = String.format("Date: %s, Amount: %s, TaxAmt: %s, TaxId: %s, Cat: %s, PmtAcct: %s, Vendor: %s",
                                          updatedExpense.getExpenseDate(), updatedExpense.getAmount(), updatedExpense.getTaxAmount(),
                                          updatedExpense.getTax() != null ? updatedExpense.getTax().getId() : "null",
                                          category.getId(), paymentAccount.getId(), updatedExpense.getVendor());

        auditLogService.logAction(
                getCurrentUserId(), // Placeholder
                AccountingActionType.UPDATE,
                AccountingConstants.ENTITY_EXPENSE,
                updatedExpense.getId(),
                "Updated Expense. Old: [" + oldDetails + "], New: [" + newDetails + "]"
        );

        return mapToDto(updatedExpense);
    }

    @Override
    @Transactional
    public void deleteExpense(UUID id) {
        log.warn("Attempting to delete Expense with ID: {}", id);
        Expense expense = findExpenseOrThrow(id);

        expenseRepository.delete(expense);
        log.warn("Successfully deleted Expense with ID: {}", id);

        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.DELETE,
                AccountingConstants.ENTITY_EXPENSE,
                id,
                String.format("Deleted Expense: Date: %s, Amount: %s, Category: %s",
                        expense.getExpenseDate(), expense.getAmount(), expense.getCategory().getNameEn()) // Use getNameEn()
        );
    }



    private Expense findExpenseOrThrow(UUID id) {
        return expenseRepository.findById(id)
                .orElseThrow(() -> new ExpenseNotFoundException(id));
    }

    // --- Helper Methods ---

    private ExpenseDto mapToDto(Expense expense) {
        if (expense == null) return null;

        BigDecimal amountBeforeTax = expense.getAmount() != null ? expense.getAmount() : BigDecimal.ZERO;
        BigDecimal taxAmount = expense.getTaxAmount() != null ? expense.getTaxAmount() : BigDecimal.ZERO;
        BigDecimal totalAmount = amountBeforeTax.add(taxAmount);

        return ExpenseDto.builder()
                .id(expense.getId())
                .expenseDate(expense.getExpenseDate())
                .amountBeforeTax(amountBeforeTax) // Use renamed field
                .taxAmount(taxAmount)
                .totalAmount(totalAmount) // Set calculated total
                .descriptionEn(expense.getDescriptionEn())
                .descriptionAr(expense.getDescriptionAr())
                .referenceNumber(expense.getReferenceNumber())
                .category(mapCategoryToSimpleDto(expense.getCategory()))
                .tax(mapTaxToSimpleDto(expense.getTax())) // Map tax
                .paymentAccount(mapAccountToSimpleDto(expense.getPaymentAccount()))
                .vendor(expense.getVendor())
                .createdDate(expense.getCreatedDate())
                .lastModifiedDate(expense.getLastModifiedDate())
                .build();
    }

    private SimpleExpenseCategoryDto mapCategoryToSimpleDto(ExpenseCategory category) {
        if (category == null) return null;
        return SimpleExpenseCategoryDto.builder()
                .id(category.getId())
                .nameEn(category.getNameEn())
                .nameAr(category.getNameAr())
                .build();
    }

    // Remove mapPaymentMethodToSimpleDto as it's no longer needed here
    /*
    private SimplePaymentMethodDto mapPaymentMethodToSimpleDto(PaymentMethod paymentMethod) {
        if (paymentMethod == null) return null;
        // Assuming SimplePaymentMethodDto exists
        return SimplePaymentMethodDto.builder()
                .id(paymentMethod.getId())
                .name(paymentMethod.getName())
                // Add other relevant simple fields if needed
                .build();
    }
    */

    // Helper to map Tax to SimpleTaxDto
    private SimpleTaxDto mapTaxToSimpleDto(Tax tax) {
        if (tax == null) return null;
        // Based on the Tax model provided: nameEn holds English name, descriptionAr holds Arabic name.
        return SimpleTaxDto.builder()
                .id(tax.getId())
                .nameEn(tax.getNameEn())
                .nameAr(tax.getDescriptionAr()) // Using descriptionAr for Arabic name as per Tax model
                .percent(tax.getPercent())
                .build();
    }

    // Helper to map ChartOfAccount to SimpleChartOfAccountDto
    private SimpleChartOfAccountDto mapAccountToSimpleDto(ChartOfAccount account) {
        if (account == null) return null;
        return SimpleChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn()) // Use English name
                .nameAr(account.getNameAr())   // Use Arabic name
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("********-0000-0000-0000-********0000");
    }
}
