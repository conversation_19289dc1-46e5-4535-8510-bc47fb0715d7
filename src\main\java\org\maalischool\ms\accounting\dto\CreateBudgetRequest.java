package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

@Data // Ensures getters/setters/etc.
@Builder
public class CreateBudgetRequest {
    @NotBlank(message = "Academic year cannot be blank")
    @Size(max = 50, message = "Academic year must be less than 50 characters")
    private String academicYear;

    @NotBlank(message = "Budget period cannot be blank")
    @Size(max = 50, message = "Budget period must be less than 50 characters") // e.g., "Q1", "Annual", "January"
    private String period;

    // Budget can be linked to EITHER a ChartOfAccount OR an ExpenseCategory, not both
    private UUID chartOfAccountId; // For income/expense accounts directly
    private UUID expenseCategoryId; // For budgeting against a category

    @NotNull(message = "Budgeted amount cannot be null")
    @DecimalMin(value = "0.0", message = "Budgeted amount must be non-negative")
    private BigDecimal budgetedAmount;

    @Size(max = 500, message = "Notes must be less than 500 characters")
    private String notes;
}
