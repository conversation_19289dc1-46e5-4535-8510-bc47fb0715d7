package org.maalischool.ms.accounting.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;

import java.time.Instant;
import java.util.UUID;

@Data
@Builder
@Schema(description = "Data Transfer Object for Accounting Audit Log entries")
public class AccountingAuditLogDto {

    @Schema(description = "Unique identifier of the audit log entry", example = "a1b2c3d4-e5f6-7890-1234-567890abcdef")
    private UUID id;

    @Schema(description = "Timestamp when the action occurred", example = "2024-04-10T10:15:30Z")
    private Instant actionTimestamp;

    @Schema(description = "ID of the user who performed the action", example = "f0e9d8c7-b6a5-4321-fedc-ba9876543210")
    private UUID userId; // Consider returning User details if needed/secure

    @Schema(description = "Type of action performed", example = "CREATE")
    private AccountingActionType actionType;

    @Schema(description = "Type of the entity affected (e.g., JournalEntry)", example = "JournalEntry")
    private String entityType;

    @Schema(description = "ID of the entity affected", example = "123e4567-e89b-12d3-a456-************")
    private UUID entityId;

    @Schema(description = "Details about the action performed", example = "Created Journal Entry ID: 123e4567-e89b-12d3-a456-************")
    private String details;
}
