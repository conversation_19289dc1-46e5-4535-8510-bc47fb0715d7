package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Fee;
import org.maalischool.ms.schoolmanagement.model.AcademicYear;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FeeRepository extends JpaRepository<Fee, UUID> {

    // Find by English name, year, and category
    Optional<Fee> findByNameEnAndAcademicYearIdAndFeeCategoryId(String nameEn, UUID academicYearId, UUID feeCategoryId);

    // Order by English category name, then English fee name
    List<Fee> findByAcademicYearIdAndActiveTrueOrderByFeeCategoryNameEnAscNameEnAsc(UUID academicYearId);

    // Order by English fee name
    List<Fee> findByFeeCategoryIdAndActiveTrueOrderByNameEnAsc(UUID feeCategoryId);

    List<Fee> findByAcademicYearIdAndApplicableGradeIdAndActiveTrue(UUID academicYearId, UUID gradeId);

    List<Fee> findByAcademicYearIdAndApplicableStageIdAndActiveTrue(UUID academicYearId, UUID stageId);

    List<Fee> findByAcademicYearIdAndApplicableBranchIdAndActiveTrue(UUID academicYearId, UUID branchId);

    @Query("SELECT f FROM Fee f WHERE f.active = true AND f.academicYear.id = :academicYearId " +
            "AND (f.applicableGradeId = :gradeId OR f.applicableGradeId IS NULL) " +
            "AND (f.applicableStageId = :stageId OR f.applicableStageId IS NULL) " +
            "AND (f.applicableBranchId = :branchId OR f.applicableBranchId IS NULL) " +
            "ORDER BY f.feeCategory.nameEn, f.nameEn") // Order by English names
    List<Fee> findApplicableFees(
            @Param("academicYearId") UUID academicYearId,
            @Param("gradeId") UUID gradeId,
            @Param("stageId") UUID stageId,
            @Param("branchId") UUID branchId);

    /**
     * Checks if any Fee exists associated with the given FeeCategory ID.
     * This is used to prevent deletion of a FeeCategory if it's still in use.
     * Spring Data JPA will look for a 'feeCategory' field in the Fee entity
     * and then check its 'id' property.
     *
     * @param feeCategoryId The UUID of the FeeCategory to check for.
     * @return true if at least one Fee is associated with the category, false
     *         otherwise.
     */
    boolean existsByFeeCategoryId(UUID feeCategoryId);
}
