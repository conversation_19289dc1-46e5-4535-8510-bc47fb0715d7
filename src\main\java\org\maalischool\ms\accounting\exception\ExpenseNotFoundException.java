package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class ExpenseNotFoundException extends RuntimeException {

    public ExpenseNotFoundException(UUID id) {
        super("Expense not found with ID: " + id);
    }

     public ExpenseNotFoundException(String message) {
        super(message);
    }

     public ExpenseNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
