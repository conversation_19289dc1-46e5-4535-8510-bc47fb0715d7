package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.ChartOfAccountDto;
import org.maalischool.ms.accounting.dto.CreateChartOfAccountRequest;
import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto;
import org.maalischool.ms.accounting.dto.UpdateChartOfAccountRequest;
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException; // Ensure this import exists
import org.maalischool.ms.accounting.exception.DuplicateChartOfAccountNumberException;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.AccountingCategory; // Import AccountingCategory
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.ChartOfAccountService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
// Removed duplicate imports and unused Predicate/ArrayList/List/StringUtils imports from this specific block
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
// Removed unused imports: StringUtils, ArrayList, List
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class ChartOfAccountServiceImpl implements ChartOfAccountService {

    private static final Logger log = LoggerFactory.getLogger(ChartOfAccountServiceImpl.class);
    private final ChartOfAccountRepository chartOfAccountRepository;
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public ChartOfAccountDto createChartOfAccount(CreateChartOfAccountRequest request) {
        log.info("Attempting to create Chart of Account with number: {}", request.getAccountNumber());

        chartOfAccountRepository.findByAccountNumber(request.getAccountNumber()).ifPresent(existing -> {
            throw new DuplicateChartOfAccountNumberException(request.getAccountNumber());
        });

        ChartOfAccount parent = null;
        if (request.getParentAccountId() != null) {
            parent = findChartOfAccountOrThrow(request.getParentAccountId());
        }

        ChartOfAccount chartOfAccount = ChartOfAccount.builder()
                .accountNumber(request.getAccountNumber())
                .nameEn(request.getNameEn())
                .nameAr(request.getNameAr())
                .descriptionEn(request.getDescriptionEn())
                .descriptionAr(request.getDescriptionAr())
                .category(request.getCategory())
                .parentAccount(parent)
                .active(true)
                .build();

        ChartOfAccount savedAccount = chartOfAccountRepository.save(chartOfAccount);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.CREATE,
                ChartOfAccount.class.getSimpleName(), savedAccount.getId(),
                "Created Chart of Account: " + savedAccount.getAccountNumber());

        log.info("Successfully created Chart of Account with ID: {}", savedAccount.getId());
        return mapToDto(savedAccount);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ChartOfAccountDto> getChartOfAccountById(UUID id) {
        return chartOfAccountRepository.findById(id).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ChartOfAccountDto> getChartOfAccountByNumber(String accountNumber) {
        return chartOfAccountRepository.findByAccountNumber(accountNumber).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ChartOfAccountDto> getAllActiveChartOfAccounts(String accountNumber, String nameEn, String nameAr, AccountingCategory category, Pageable pageable) {
        log.info("Fetching active Chart of Accounts with search criteria - AccNo: '{}', NameEn: '{}', NameAr: '{}', Category: '{}', Page: {}, Size: {}, Sort: {}",
                 accountNumber, nameEn, nameAr, category, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        Specification<ChartOfAccount> spec = ChartOfAccountRepository.createSpecification(accountNumber, nameEn, nameAr, category, true); // Pass category
        Page<ChartOfAccount> activeAccountsPage = chartOfAccountRepository.findAll(spec, pageable);
        return activeAccountsPage.map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ChartOfAccountDto> getAllChartOfAccounts(String accountNumber, String nameEn, String nameAr, AccountingCategory category, Pageable pageable) {
         log.info("Fetching all Chart of Accounts with search criteria - AccNo: '{}', NameEn: '{}', NameAr: '{}', Category: '{}', Page: {}, Size: {}, Sort: {}",
                 accountNumber, nameEn, nameAr, category, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        Specification<ChartOfAccount> spec = ChartOfAccountRepository.createSpecification(accountNumber, nameEn, nameAr, category, null); // Pass category, active=null
        return chartOfAccountRepository.findAll(spec, pageable).map(this::mapToDto);
    }

    @Override
    @Transactional
    public ChartOfAccountDto updateChartOfAccount(UUID id, UpdateChartOfAccountRequest request) {
        log.info("Attempting to update Chart of Account with ID: {}", id);
        ChartOfAccount chartOfAccount = findChartOfAccountOrThrow(id);

        ChartOfAccount parent = null;
        if (request.getParentAccountId() != null) {
            if (request.getParentAccountId().equals(id)) {
                throw new IllegalArgumentException("Account cannot be its own parent.");
            }
            parent = findChartOfAccountOrThrow(request.getParentAccountId());
        }

        chartOfAccount.setNameEn(request.getNameEn());
        chartOfAccount.setNameAr(request.getNameAr());
        chartOfAccount.setDescriptionEn(request.getDescriptionEn());
        chartOfAccount.setDescriptionAr(request.getDescriptionAr());
        chartOfAccount.setCategory(request.getCategory());
        chartOfAccount.setActive(request.getActive());
        chartOfAccount.setParentAccount(parent);

        ChartOfAccount updatedAccount = chartOfAccountRepository.save(chartOfAccount);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.UPDATE,
                ChartOfAccount.class.getSimpleName(), updatedAccount.getId(),
                "Updated Chart of Account: " + updatedAccount.getAccountNumber());

        log.info("Successfully updated Chart of Account with ID: {}", updatedAccount.getId());
        return mapToDto(updatedAccount);
    }

    @Override
    @Transactional
    public void deleteChartOfAccount(UUID id) {
        log.warn("Attempting to delete Chart of Account with ID: {}", id);
        ChartOfAccount chartOfAccount = findChartOfAccountOrThrow(id);

        chartOfAccountRepository.delete(chartOfAccount);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.DELETE,
                ChartOfAccount.class.getSimpleName(), id,
                "Deleted Chart of Account: " + chartOfAccount.getAccountNumber());
        log.warn("Successfully deleted Chart of Account with ID: {}", id);
    }

    private ChartOfAccount findChartOfAccountOrThrow(UUID id) {
        return chartOfAccountRepository.findById(id)
                .orElseThrow(() -> new ChartOfAccountNotFoundException(id));
    }

    private ChartOfAccountDto mapToDto(ChartOfAccount account) {
        if (account == null) return null;
        SimpleChartOfAccountDto parentDto = null;
        if (account.getParentAccount() != null) {
            parentDto = SimpleChartOfAccountDto.builder()
                    .id(account.getParentAccount().getId())
                    .accountNumber(account.getParentAccount().getAccountNumber())
                    .nameEn(account.getParentAccount().getNameEn()) // Update parent DTO mapping
                    .nameAr(account.getParentAccount().getNameAr())   // Update parent DTO mapping
                    .build();
        }
        return ChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn())
                .nameAr(account.getNameAr())
                .descriptionEn(account.getDescriptionEn())
                .descriptionAr(account.getDescriptionAr())
                .category(account.getCategory())
                .active(account.isActive())
                .parentAccount(parentDto)
                .createdDate(account.getCreatedDate())
                .lastModifiedDate(account.getLastModifiedDate())
                .build();
    }

    private UUID getCurrentUserId() {
        log.warn("getCurrentUserId() is using a placeholder UUID. Implement security context retrieval.");
        return UUID.fromString("********-0000-0000-0000-************");
    }
}
