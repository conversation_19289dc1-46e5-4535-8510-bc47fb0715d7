package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.math.BigDecimal;

@ResponseStatus(HttpStatus.BAD_REQUEST) // 400 Bad Request
public class JournalEntryUnbalancedException extends RuntimeException {

    public JournalEntryUnbalancedException(BigDecimal debits, BigDecimal credits) {
        super(String.format("Journal Entry is unbalanced. Debits (%s) do not equal Credits (%s).", debits, credits));
    }
}
