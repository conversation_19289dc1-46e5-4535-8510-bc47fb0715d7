package org.maalischool.ms.accounting.repository.specification;

import jakarta.persistence.criteria.Predicate;
import org.maalischool.ms.accounting.model.AccountingAuditLog;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.util.StringUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Utility class for creating JPA Specifications for AccountingAuditLog entities.
 */
public class AccountingAuditLogSpecification {

    private AccountingAuditLogSpecification() {
        // Private constructor
    }

    /**
     * Creates a Specification based on optional filter criteria.
     *
     * @param userId     Optional user ID.
     * @param entityType Optional entity type (case-sensitive).
     * @param entityId   Optional entity ID.
     * @param startTime  Optional start timestamp (inclusive).
     * @param endTime    Optional end timestamp (inclusive).
     * @return A Specification<AccountingAuditLog>.
     */
    public static Specification<AccountingAuditLog> filterBy(
            UUID userId, String entityType, UUID entityId, Instant startTime, Instant endTime) {

        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (userId != null) {
                predicates.add(criteriaBuilder.equal(root.get("userId"), userId));
            }
            if (StringUtils.hasText(entityType)) {
                predicates.add(criteriaBuilder.equal(root.get("entityType"), entityType));
            }
            if (entityId != null) {
                predicates.add(criteriaBuilder.equal(root.get("entityId"), entityId));
            }
            if (startTime != null) {
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("actionTimestamp"), startTime));
            }
            if (endTime != null) {
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("actionTimestamp"), endTime));
            }

            // Default sort by timestamp descending if no sort is specified in Pageable
            if (query.getOrderList().isEmpty()) {
                query.orderBy(criteriaBuilder.desc(root.get("actionTimestamp")));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
