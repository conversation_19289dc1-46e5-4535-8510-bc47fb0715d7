package org.maalischool.ms.student.dto;

import java.time.LocalDate;
import java.util.UUID;

import org.maalischool.ms.student.model.Gender;
import org.maalischool.ms.student.model.GuardianType; // Import GuardianType
import org.maalischool.ms.student.model.IdType; // Import IdType

import jakarta.validation.constraints.PastOrPresent;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// Allow partial updates
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateStudentProfileRequest {

    // Admission number is auto-generated and not updatable

    @PastOrPresent(message = "{validation.student.dateOfBirth.pastOrPresent}")
    private LocalDate dateOfBirth; // Optional

    private Gender gender; // Optional

    @Size(max = 255, message = "{validation.student.address.size}")
    private String address; // Optional

    @Size(max = 50, message = "{validation.student.nationalId.size}") // New field validation
    private String nationalId; // Optional

    private IdType idType; // Optional

    private GuardianType guardianType; // Optional

    // Optional: ID of the nationality (country) for this student
    private UUID nationalityId; // Optional

    // guardianId change handled via link/unlink endpoints
}
