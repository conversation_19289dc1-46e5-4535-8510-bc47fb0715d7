package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.DiscountType;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data // Ensures getters/setters/etc.
@Builder
public class DiscountDto {
    private UUID id;
    private String code;
    private String descriptionEn;
    private String descriptionAr;
    private DiscountType type;
    private BigDecimal value;
    private LocalDate validFrom;
    private LocalDate validUntil;
    private UUID applicableFeeId;
    private UUID applicableStudentId;
    private boolean active;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
