package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.FixedAsset;
import org.maalischool.ms.accounting.model.enums.AssetStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface FixedAssetRepository extends JpaRepository<FixedAsset, UUID> {

    Optional<FixedAsset> findByAssetCode(String assetCode);

    List<FixedAsset> findByStatusOrderByNameEnAsc(AssetStatus status); // Order by English name

    List<FixedAsset> findByAssetAccountId(UUID assetAccountId);

    List<FixedAsset> findByLocationContainingIgnoreCaseOrderByNameEnAsc(String location); // Order by English name
}
