package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.accounting.dto.InventoryAdjustmentRequest;
import org.maalischool.ms.accounting.dto.InventoryTransactionDto;
import org.maalischool.ms.accounting.dto.SimpleItemDto;
import org.maalischool.ms.accounting.exception.InsufficientStockException;
import org.maalischool.ms.accounting.exception.ItemNotFoundException;
import org.maalischool.ms.accounting.model.InventoryTransaction;
import org.maalischool.ms.accounting.model.Item;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.InventoryTransactionType;
import org.maalischool.ms.accounting.repository.InventoryTransactionRepository;
import org.maalischool.ms.accounting.repository.ItemRepository;
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.InventoryService;
import org.maalischool.ms.accounting.util.AccountingConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Service
@RequiredArgsConstructor
public class InventoryServiceImpl implements InventoryService {

    private static final Logger log = LoggerFactory.getLogger(InventoryServiceImpl.class);

    private final ItemRepository itemRepository;
    private final InventoryTransactionRepository inventoryTransactionRepository;
    private final AccountingAuditLogService auditLogService;

    @Value("${inventory.allow-negative-stock:false}") // Configuration property
    private boolean allowNegativeStock;

    @Override
    @Transactional
    public InventoryTransactionDto adjustInventory(InventoryAdjustmentRequest request) {
        log.info("Adjusting inventory for Item ID: {}, Type: {}, Quantity: {}, Date: {}",
                 request.getItemId(), request.getTransactionType(), request.getQuantity(), request.getTransactionDate());

        if (request.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Adjustment quantity must be positive.");
        }

        Item item = itemRepository.findById(request.getItemId())
                .orElseThrow(() -> new ItemNotFoundException(request.getItemId()));

        BigDecimal changeQuantity = request.getQuantity();
        BigDecimal oldQuantity = item.getQuantityOnHand();
        BigDecimal newQuantity;

        // Determine if the transaction increases or decreases stock based on the *actual* enum types
        boolean increasesStock = request.getTransactionType() == InventoryTransactionType.PURCHASE_RECEIPT ||
                                 request.getTransactionType() == InventoryTransactionType.TRANSFER_IN ||
                                 request.getTransactionType() == InventoryTransactionType.ADJUSTMENT_POSITIVE ||
                                 request.getTransactionType() == InventoryTransactionType.OPENING_BALANCE; // Added OPENING_BALANCE

        boolean decreasesStock = request.getTransactionType() == InventoryTransactionType.USAGE ||
                                 request.getTransactionType() == InventoryTransactionType.SALE ||
                                 request.getTransactionType() == InventoryTransactionType.TRANSFER_OUT ||
                                 request.getTransactionType() == InventoryTransactionType.ADJUSTMENT_NEGATIVE ||
                                 request.getTransactionType() == InventoryTransactionType.RETURN_TO_SUPPLIER; // Added RETURN_TO_SUPPLIER

        if (increasesStock) {
            newQuantity = oldQuantity.add(changeQuantity);
        } else if (decreasesStock) {
            newQuantity = oldQuantity.subtract(changeQuantity);
            if (newQuantity.compareTo(BigDecimal.ZERO) < 0 && !allowNegativeStock) {
                log.error("Insufficient stock for Item ID: {}. Current: {}, Requested Decrease: {}",
                          item.getId(), oldQuantity, changeQuantity);
                // Use itemCode from Item entity, not sku
                throw new InsufficientStockException(item.getId(), item.getItemCode(), oldQuantity, changeQuantity);
            }
        } else {
             // This case should ideally not happen if the request DTO validates the enum type
             log.error("Unsupported or unhandled inventory transaction type: {}", request.getTransactionType());
             throw new IllegalArgumentException("Unsupported or unhandled inventory transaction type: " + request.getTransactionType());
        }

        // Update Item's Quantity on Hand
        item.setQuantityOnHand(newQuantity);
        Item updatedItem = itemRepository.save(item); // Save the updated item stock level

        // Create Inventory Transaction Record
        InventoryTransaction transaction = InventoryTransaction.builder()
                .item(updatedItem)
                .transactionDate(request.getTransactionDate())
                .transactionType(request.getTransactionType())
                .quantityChange(changeQuantity) // Use correct builder method (matches entity field)
                .quantityAfterTransaction(newQuantity) // Use correct builder method (matches entity field)
                .unitCost(request.getUnitCost()) // Cost at the time of transaction
                .notes(request.getNotes())
                .relatedDocumentRef(request.getReferenceId()) // Use correct builder method (matches entity field)
                .build();

        InventoryTransaction savedTransaction = inventoryTransactionRepository.save(transaction);
        log.info("Successfully recorded Inventory Transaction ID: {} for Item ID: {}. New QOH: {}",
                 savedTransaction.getId(), item.getId(), newQuantity);

        // Audit Log
        auditLogService.logAction(
                getCurrentUserId(),
                AccountingActionType.ADJUST_INVENTORY, // Or more specific based on type
                AccountingConstants.ENTITY_INVENTORY_TRANSACTION,
                savedTransaction.getId(),
                String.format("Inventory Adj: Item %s (Code: %s), Type: %s, Qty: %s, New QOH: %s. Ref: %s", // Use English name and itemCode
                        item.getNameEn(), item.getItemCode(), request.getTransactionType(),
                        (increasesStock ? "+" : "-") + changeQuantity, // Use boolean flag to determine sign
                        newQuantity, request.getReferenceId())
        );

        return mapToDto(savedTransaction);
    }

    @Override
    @Transactional(readOnly = true)
    public BigDecimal getQuantityOnHand(UUID itemId) {
        log.debug("Fetching quantity on hand for Item ID: {}", itemId);
        Item item = itemRepository.findById(itemId)
                .orElseThrow(() -> new ItemNotFoundException(itemId));
        return item.getQuantityOnHand();
    }

    @Override
    @Transactional(readOnly = true)
    public Page<InventoryTransactionDto> getTransactionHistoryForItem(UUID itemId, LocalDate startDate, LocalDate endDate, Pageable pageable) {
        log.debug("Fetching transaction history for Item ID: {} between {} and {}", itemId, startDate, endDate);
        // Ensure item exists
        if (!itemRepository.existsById(itemId)) {
            throw new ItemNotFoundException(itemId);
        }
        return inventoryTransactionRepository.findByItemIdAndTransactionDateBetweenOrderByTransactionDateDesc(
                itemId, startDate, endDate, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<InventoryTransactionDto> getAllTransactionHistory(LocalDate startDate, LocalDate endDate, Pageable pageable) {
        log.debug("Fetching all transaction history between {} and {}", startDate, endDate);
        return inventoryTransactionRepository.findByTransactionDateBetweenOrderByTransactionDateDesc(
                startDate, endDate, pageable)
                .map(this::mapToDto);
    }

    // --- Helper Methods ---

    private InventoryTransactionDto mapToDto(InventoryTransaction transaction) {
        if (transaction == null) return null;
        return InventoryTransactionDto.builder()
                .id(transaction.getId())
                .item(mapItemToSimpleDto(transaction.getItem()))
                .transactionDate(transaction.getTransactionDate())
                .transactionType(transaction.getTransactionType())
                .quantityChange(transaction.getQuantityChange()) // Use correct getter
                .quantityAfterTransaction(transaction.getQuantityAfterTransaction()) // Use correct getter
                .unitCost(transaction.getUnitCost())
                .notes(transaction.getNotes())
                .referenceId(transaction.getRelatedDocumentRef()) // Use correct getter
                .createdDate(transaction.getCreatedDate())
                .build();
    }

    private SimpleItemDto mapItemToSimpleDto(Item item) {
        if (item == null) return null;
        return SimpleItemDto.builder()
                .id(item.getId())
                .nameEn(item.getNameEn()) // Use English name
                .nameAr(item.getNameAr())   // Use Arabic name
                .itemCode(item.getItemCode()) // Use correct getter (itemCode instead of sku)
                .unitOfMeasure(item.getUnitOfMeasure()) // Add unit of measure
                .build();
    }

    private UUID getCurrentUserId() {
        // TODO: Implement using Spring Security Context Holder
        log.warn("getCurrentUserId() is a placeholder and needs implementation using Spring Security.");
        // Returning a placeholder UUID for now
        return UUID.fromString("00000000-0000-0000-0000-000000000000");
    }
}
