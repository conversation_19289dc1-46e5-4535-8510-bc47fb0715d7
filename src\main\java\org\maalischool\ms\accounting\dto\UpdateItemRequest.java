package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.ItemType;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@Builder
public class UpdateItemRequest {
    // Item code is usually not updatable
    @NotBlank(message = "English item name cannot be blank")
    @Size(max = 255, message = "English item name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic item name cannot be blank")
    @Size(max = 255, message = "Arabic item name must be less than 255 characters")
    private String nameAr;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    @NotNull(message = "Item type cannot be null")
    private ItemType type;

    @NotBlank(message = "Unit of measure cannot be blank")
    @Size(max = 50, message = "Unit of measure must be less than 50 characters")
    private String unitOfMeasure;

    @NotNull(message = "Cost price cannot be null")
    @DecimalMin(value = "0.0", message = "Cost price must be non-negative")
    private BigDecimal costPrice;

    @NotNull(message = "Selling price cannot be null")
    @DecimalMin(value = "0.0", message = "Selling price must be non-negative")
    private BigDecimal sellingPrice;

    // Account links might change if CoA is restructured, but carefully
    private UUID inventoryAccountId;
    private UUID cogsAccountId;
    private UUID salesAccountId;

    @PositiveOrZero(message = "Reorder level must be zero or positive")
    private Integer reorderLevel;

    @Size(max = 255, message = "Supplier info must be less than 255 characters")
    private String supplierInfo;

    @NotNull(message = "Active status cannot be null")
    private Boolean active;
}
