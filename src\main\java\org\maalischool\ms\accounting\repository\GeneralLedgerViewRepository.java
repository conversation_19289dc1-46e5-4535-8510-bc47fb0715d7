package org.maalischool.ms.accounting.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import org.maalischool.ms.accounting.model.GeneralLedgerView;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface GeneralLedgerViewRepository extends JpaRepository<GeneralLedgerView, UUID> {

    @Query("SELECT glv FROM GeneralLedgerView glv " +
           "WHERE glv.entryDate BETWEEN :startDate AND :endDate " +
           "AND (:isPosted IS NULL OR (:isPosted = TRUE AND glv.postedDate IS NOT NULL) OR (:isPosted = FALSE AND glv.postedDate IS NULL)) " +
           "AND (:accountIds IS NULL OR glv.chartOfAccountId IN :accountIds) " +
           "ORDER BY glv.chartOfAccountId ASC, glv.entryDate ASC, glv.id ASC")
    List<GeneralLedgerView> findGeneralLedgerEntries(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("accountIds") List<UUID> accountIds,
            @Param("isPosted") Boolean isPosted);

    @Query("SELECT COALESCE(SUM(CASE WHEN glv.type = 'DEBIT' THEN glv.amount ELSE -glv.amount END), 0) " +
           "FROM GeneralLedgerView glv " +
           "WHERE glv.chartOfAccountId = :accountId " +
           "AND glv.entryDate < :startDate " +
           "AND glv.postedDate IS NOT NULL")
    BigDecimal calculateOpeningBalance(
            @Param("accountId") UUID accountId,
            @Param("startDate") LocalDate startDate);
            
    @Query("SELECT DISTINCT glv.chartOfAccountId FROM GeneralLedgerView glv " +
           "WHERE glv.entryDate BETWEEN :startDate AND :endDate " +
           "AND (:accountIds IS NULL OR glv.chartOfAccountId IN :accountIds) " +
           "AND (:isPosted IS NULL OR (:isPosted = TRUE AND glv.postedDate IS NOT NULL) OR (:isPosted = FALSE AND glv.postedDate IS NULL))")
    List<UUID> findDistinctAccountIds(
            @Param("startDate") LocalDate startDate,
            @Param("endDate") LocalDate endDate,
            @Param("accountIds") List<UUID> accountIds,
            @Param("isPosted") Boolean isPosted);
}
