package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "acc_budgets", uniqueConstraints = {
        // Ensure only one budget entry per account/category per year/period
        @UniqueConstraint(columnNames = {"academic_year", "period", "chart_of_account_id"}),
        @UniqueConstraint(columnNames = {"academic_year", "period", "expense_category_id"})
        // Note: A single constraint covering all 4 might be too restrictive if you allow
        // budgeting EITHER by account OR by category for the same period.
        // These two constraints allow one entry per account and one entry per category per period.
        // Adjust if the business rule is different (e.g., only one budget line per period, either account or category).
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class Budget {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "academic_year", nullable = false, length = 10) // e.g., "2024-2025"
    private String academicYear;

    @Column(nullable = false, length = 20) // e.g., "ANNUAL", "Q1", "JANUARY"
    private String period;

    // Budget can be for a specific account OR an expense category
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chart_of_account_id") // Nullable if budgeting by category
    private ChartOfAccount chartOfAccount;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "expense_category_id") // Nullable if budgeting by account
    private ExpenseCategory expenseCategory;

    @Column(name = "budgeted_amount", nullable = false, precision = 19, scale = 4)
    private BigDecimal budgetedAmount;

    @Column(length = 500)
    private String notes;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;

    // Validation: Ensure either chartOfAccount or expenseCategory is set, but not both? (Handled in service layer)
}
