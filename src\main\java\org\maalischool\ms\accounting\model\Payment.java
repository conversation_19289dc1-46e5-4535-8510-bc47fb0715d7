package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "acc_payments", indexes = {
        @Index(name = "idx_payment_receipt", columnList = "receipt_id"),
        @Index(name = "idx_payment_studentfee", columnList = "student_fee_id")
        // Removed index on payment_method_id as it's now on Receipt
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id", exclude = {"receipt", "studentFee"}) // Exclude relationships
@EntityListeners(AuditingEntityListener.class)
public class Payment { // Represents the allocation of a received amount

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "receipt_id", nullable = false)
    private Receipt receipt; // The overall receipt this payment belongs to

    // The specific fee being paid by this allocation.
    // Can be null if payment is on account / unallocated initially.
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "student_fee_id")
    private StudentFee studentFee;

    @Column(name = "payment_date", nullable = false)
    private LocalDate paymentDate; // Usually same as receipt date, but could differ

    @Column(nullable = false, precision = 19, scale = 4)
    private BigDecimal amount; // The portion of the receipt allocated here

    @Column(length = 255)
    private String reference; // e.g., Cheque number, transaction ID

    @Column(length = 500)
    private String notes;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID createdBy; // Link to User ID
}
