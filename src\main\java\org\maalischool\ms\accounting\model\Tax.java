package org.maalischool.ms.accounting.model;

import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import lombok.*;
import org.hibernate.annotations.GenericGenerator;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import java.util.UUID; // Use standard java.util.UUID

import java.math.BigDecimal;

@Entity
@Table(name = "acc_taxes")
@Getter
@Setter
@Builder // Added Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = "chartOfAccount") // Exclude ilişkili alanları
@EqualsAndHashCode(of = "id", exclude = "chartOfAccount") // Exclude ilişkili alanları
public class Tax {

    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(
            name = "UUID",
            strategy = "org.hibernate.id.UUIDGenerator" // Consider GenerationType.UUID if preferred
    )
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR) // Consistent UUID handling
    private UUID id; // Correct type declaration

    @Column(name = "name_ar", nullable = false, length = 255) // Added length
    private String nameEn;

    @Column(name = "description_ar", length = 1000) // Added length
    private String descriptionAr;

    @Column(name = "description_en", length = 1000) // Added length
    private String descriptionEn;

    @Column(nullable = false, precision = 5, scale = 2) // Example: 15.00%
    private BigDecimal percent;

    @ManyToOne(fetch = FetchType.LAZY, optional = false) // Tax must belong to an account
    @JoinColumn(name = "chart_of_account_id", nullable = false)
    private ChartOfAccount chartOfAccount;

    // TODO: Consider adding auditing fields (createdDate, lastModifiedDate) if needed
    // @CreatedDate
    // @Column(name = "created_date", nullable = false, updatable = false)
    // private Instant createdDate;
    //
    // @LastModifiedDate
    // @Column(name = "last_modified_date", nullable = false)
    // private Instant lastModifiedDate;
    // Add @EntityListeners(AuditingEntityListener.class) to the class if auditing is added
}
