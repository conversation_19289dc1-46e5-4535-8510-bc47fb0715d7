package org.maalischool.ms.accounting.service.impl;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

import org.maalischool.ms.accounting.dto.CreateJournalEntryLineRequest;
import org.maalischool.ms.accounting.dto.CreateJournalEntryRequest;
import org.maalischool.ms.accounting.dto.JournalEntryDto;
import org.maalischool.ms.accounting.dto.JournalEntryLineDto;
import org.maalischool.ms.accounting.dto.SimpleChartOfAccountDto;
import org.maalischool.ms.accounting.dto.UpdateJournalEntryRequest;
import org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException;
import org.maalischool.ms.accounting.exception.JournalEntryAlreadyPostedException;
import org.maalischool.ms.accounting.exception.JournalEntryNotFoundException;
import org.maalischool.ms.accounting.exception.JournalEntryUnbalancedException;
// Import models
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.JournalEntry;
import org.maalischool.ms.accounting.model.JournalEntryLine;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.maalischool.ms.accounting.model.enums.TransactionType;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.JournalEntryRepository;
import org.maalischool.ms.accounting.repository.specification.JournalEntrySpecification; // Import Specification
import org.maalischool.ms.accounting.service.AccountingAuditLogService;
import org.maalischool.ms.accounting.service.JournalEntryService;
import org.maalischool.ms.student.dto.SimpleStudentDto;
import org.maalischool.ms.student.model.Student;
import org.maalischool.ms.student.repository.StudentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification; // Import Specification
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class JournalEntryServiceImpl implements JournalEntryService {

    private static final Logger log = LoggerFactory.getLogger(JournalEntryServiceImpl.class);
    private final JournalEntryRepository journalEntryRepository;
    private final ChartOfAccountRepository chartOfAccountRepository;
    private final StudentRepository studentRepository;
    private final AccountingAuditLogService auditLogService;

    @Override
    @Transactional
    public JournalEntryDto createJournalEntry(CreateJournalEntryRequest request) {
        log.info("Attempting to create Journal Entry for date: {}", request.getEntryDate());

        String journalEntryNumber = generateJournalEntryNumber(request.getEntryDate().getYear());
        log.info("Attempting to create Journal Entry for date: {}", request.getEntryDate());

        JournalEntry journalEntry = JournalEntry.builder()
                .entryDate(request.getEntryDate())
                .description(request.getDescription())
                .referenceNumber(request.getReferenceNumber())
                .journalEntryNumber(journalEntryNumber) // Set the generated number
                .build();

        BigDecimal totalDebits = BigDecimal.ZERO;
        BigDecimal totalCredits = BigDecimal.ZERO;

        for (CreateJournalEntryLineRequest lineRequest : request.getLines()) {
            ChartOfAccount account = chartOfAccountRepository.findById(lineRequest.getChartOfAccountId())
                    .orElseThrow(() -> new ChartOfAccountNotFoundException(lineRequest.getChartOfAccountId()));

            // Check if student ID is provided and fetch the student if it is
            Student student = null;
            if (lineRequest.getStudentId() != null) {
                student = studentRepository.findById(lineRequest.getStudentId())
                        .orElse(null); // Optional: throw exception if student not found
            }

            JournalEntryLine line = JournalEntryLine.builder()
                    .chartOfAccount(account)
                    .type(lineRequest.getType())
                    .amount(lineRequest.getAmount())
                    .description(lineRequest.getDescription())
                    .student(student)
                    .build();
            journalEntry.addLine(line);

            if (lineRequest.getType() == TransactionType.DEBIT) {
                totalDebits = totalDebits.add(lineRequest.getAmount());
            } else {
                totalCredits = totalCredits.add(lineRequest.getAmount());
            }
        }

        if (totalDebits.compareTo(totalCredits) != 0) {
            log.error("Journal Entry is unbalanced. Debits: {}, Credits: {}", totalDebits, totalCredits);
            throw new JournalEntryUnbalancedException(totalDebits, totalCredits);
        }

        JournalEntry savedEntry = journalEntryRepository.save(journalEntry);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.CREATE,
                JournalEntry.class.getSimpleName(), savedEntry.getId(),
                "Created Journal Entry ID: " + savedEntry.getId());

        log.info("Successfully created Journal Entry with ID: {}", savedEntry.getId());
        return mapToDto(savedEntry);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<JournalEntryDto> getJournalEntryById(UUID id) {
        return journalEntryRepository.findById(id).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<JournalEntryDto> findJournalEntries(LocalDate startDate, LocalDate endDate, String referenceNumber,
            Boolean isPosted, Pageable pageable) {
        log.debug(
                "Finding journal entries with criteria: startDate={}, endDate={}, referenceNumber='{}', isPosted={}, pageable={}",
                startDate, endDate, referenceNumber, isPosted, pageable);

        // Build the specification dynamically based on provided criteria
        Specification<JournalEntry> spec = JournalEntrySpecification.filterBy(startDate, endDate, referenceNumber,
                isPosted);

        // Execute the query using the specification and pageable
        Page<JournalEntry> journalEntryPage = journalEntryRepository.findAll(spec, pageable);

        log.debug("Found {} journal entries matching criteria.", journalEntryPage.getTotalElements());
        return journalEntryPage.map(this::mapToDto);
    }

    @Override
    @Transactional
    public JournalEntryDto updateJournalEntry(UUID id, UpdateJournalEntryRequest request) {
        log.info("Attempting to update Journal Entry with ID: {}", id);
        JournalEntry journalEntry = findJournalEntryOrThrow(id);

        if (journalEntry.getPostedDate() != null) {
            throw new JournalEntryAlreadyPostedException(id);
        }

        journalEntry.setEntryDate(request.getEntryDate());
        journalEntry.setDescription(request.getDescription());
        journalEntry.setReferenceNumber(request.getReferenceNumber());

        JournalEntry updatedEntry = journalEntryRepository.save(journalEntry);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.UPDATE,
                JournalEntry.class.getSimpleName(), updatedEntry.getId(),
                "Updated Journal Entry ID: " + updatedEntry.getId());

        log.info("Successfully updated Journal Entry with ID: {}", updatedEntry.getId());
        return mapToDto(updatedEntry);
    }

    @Override
    @Transactional
    public JournalEntryDto postJournalEntry(UUID id) {
        log.info("Attempting to post Journal Entry with ID: {}", id);
        JournalEntry journalEntry = findJournalEntryOrThrow(id);

        if (journalEntry.getPostedDate() != null) {
            throw new JournalEntryAlreadyPostedException(id);
        }

        validateBalance(journalEntry);

        journalEntry.setPostedDate(Instant.now());
        JournalEntry postedEntry = journalEntryRepository.save(journalEntry);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.POST_JOURNAL_ENTRY,
                JournalEntry.class.getSimpleName(), postedEntry.getId(),
                "Posted Journal Entry ID: " + postedEntry.getId());

        log.info("Successfully posted Journal Entry with ID: {}", postedEntry.getId());
        return mapToDto(postedEntry);
    }

    @Override
    @Transactional
    public void deleteJournalEntry(UUID id) {
        log.warn("Attempting to delete Journal Entry with ID: {}", id);
        JournalEntry journalEntry = findJournalEntryOrThrow(id);

        if (journalEntry.getPostedDate() != null) {
            throw new JournalEntryAlreadyPostedException(id, "Cannot delete a posted journal entry.");
        }

        journalEntryRepository.delete(journalEntry);

        UUID currentUserId = getCurrentUserId();
        auditLogService.logAction(currentUserId, AccountingActionType.DELETE,
                JournalEntry.class.getSimpleName(), id,
                "Deleted Journal Entry ID: " + id);
        log.warn("Successfully deleted Journal Entry with ID: {}", id);
    }

    private JournalEntry findJournalEntryOrThrow(UUID id) {
        return journalEntryRepository.findById(id)
                .orElseThrow(() -> new JournalEntryNotFoundException(id));
    }

    private void validateBalance(JournalEntry journalEntry) {
        BigDecimal totalDebits = BigDecimal.ZERO;
        BigDecimal totalCredits = BigDecimal.ZERO;
        for (JournalEntryLine line : journalEntry.getLines()) {
            if (line.getType() == TransactionType.DEBIT) {
                totalDebits = totalDebits.add(line.getAmount());
            } else {
                totalCredits = totalCredits.add(line.getAmount());
            }
        }
        if (totalDebits.compareTo(totalCredits) != 0) {
            throw new JournalEntryUnbalancedException(totalDebits, totalCredits);
        }
    }

    private JournalEntryDto mapToDto(JournalEntry entry) {
        if (entry == null)
            return null;
        List<JournalEntryLineDto> lineDtos = entry.getLines().stream()
                .map(line -> JournalEntryLineDto.builder()
                        .id(line.getId())
                        .chartOfAccount(mapToSimpleCoADto(line.getChartOfAccount()))
                        .type(line.getType())
                        .amount(line.getAmount())
                        .description(line.getDescription())
                        .student(mapStudentToSimpleDto(line.getStudent()))
                        .build())
                .collect(Collectors.toList());

        return JournalEntryDto.builder()
                .id(entry.getId())
                .entryDate(entry.getEntryDate())
                .description(entry.getDescription())
                .referenceNumber(entry.getReferenceNumber())
                .journalEntryNumber(entry.getJournalEntryNumber()) // Include in DTO
                .postedDate(entry.getPostedDate())
                .lines(lineDtos)
                .createdDate(entry.getCreatedDate())
                .lastModifiedDate(entry.getLastModifiedDate())
                .build();
    }

    private String generateJournalEntryNumber(int year) {
        String yearPrefix = String.valueOf(year);
        Optional<JournalEntry> lastEntry = journalEntryRepository.findTopByJournalEntryNumberStartingWithOrderByJournalEntryNumberDesc(yearPrefix);

        int nextSequence = 1;
        if (lastEntry.isPresent()) {
            String lastNumber = lastEntry.get().getJournalEntryNumber();
            // Assuming format YYYYNNNN, e.g., 20230001
            if (lastNumber != null && lastNumber.length() == 8 && lastNumber.startsWith(yearPrefix)) {
                try {
                    nextSequence = Integer.parseInt(lastNumber.substring(4)) + 1;
                } catch (NumberFormatException e) {
                    log.warn("Could not parse sequence from journal entry number: {}. Defaulting to 1.", lastNumber);
                    // Potentially throw an error or handle more gracefully if strict parsing is required
                }
            }
        }
        return String.format("%s%04d", yearPrefix, nextSequence);
    }

    private SimpleChartOfAccountDto mapToSimpleCoADto(ChartOfAccount account) {
        if (account == null)
            return null;
        return SimpleChartOfAccountDto.builder()
                .id(account.getId())
                .accountNumber(account.getAccountNumber())
                .nameEn(account.getNameEn()) // Use English name
                .nameAr(account.getNameAr()) // Use Arabic name
                .build();
    }

    private SimpleStudentDto mapStudentToSimpleDto(Student student) {
        if (student == null)
            return null;

        String firstName = null;
        String lastName = null;
        if (student.getUserAccount() != null) {
            firstName = student.getUserAccount().getFirstName();
            lastName = student.getUserAccount().getLastName();
        }

        return SimpleStudentDto.builder()
                .id(student.getId())
                .firstName(firstName)
                .lastName(lastName)
                .admissionNumber(student.getAdmissionNumber())
                .dateOfBirth(student.getDateOfBirth())
                .gender(student.getGender())
                .build();
    }

    private UUID getCurrentUserId() {
        log.warn("getCurrentUserId() is using a placeholder UUID. Implement security context retrieval.");
        return UUID.fromString("********-0000-0000-0000-************");
    }
}
