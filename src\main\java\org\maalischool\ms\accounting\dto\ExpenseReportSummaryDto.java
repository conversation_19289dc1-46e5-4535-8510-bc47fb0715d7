package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * DTO for expense report summary information per category.
 */
@Data
@Builder
public class ExpenseReportSummaryDto {
    private UUID categoryId;
    private String categoryNameEn;
    private String categoryNameAr;
    private BigDecimal totalAmount;
    private BigDecimal totalTaxAmount;
    private BigDecimal totalAmountWithTax;
    private int expenseCount;
    private BigDecimal percentageOfTotal;
}
