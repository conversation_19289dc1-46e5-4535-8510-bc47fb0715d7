package org.maalischool.ms.accounting.service.impl;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.AIReportMessage;
import org.maalischool.ms.accounting.dto.AIReportRequest;
import org.maalischool.ms.accounting.dto.AIReportResponse;
import org.maalischool.ms.accounting.dto.BalanceSheetDto;
import org.maalischool.ms.accounting.dto.ExpenseReportDto;
import org.maalischool.ms.accounting.dto.GeneralLedgerEntryDto;
import org.maalischool.ms.accounting.dto.IncomeStatementDto;
import org.maalischool.ms.accounting.dto.TrialBalanceDto;
import org.maalischool.ms.accounting.mapper.AIReportMapper;
import org.maalischool.ms.accounting.model.AIReport;
import org.maalischool.ms.accounting.model.enums.ReportStatus;
import org.maalischool.ms.accounting.repository.AIReportRepository;
import org.maalischool.ms.accounting.service.AIReportMessageProducer;
import org.maalischool.ms.accounting.service.AIReportService;
import org.maalischool.ms.accounting.service.BalanceSheetService;
import org.maalischool.ms.accounting.service.ChatGPTService;
import org.maalischool.ms.accounting.service.ExpenseReportService;
import org.maalischool.ms.accounting.service.GeneralLedgerService;
import org.maalischool.ms.accounting.service.IncomeStatementService;
import org.maalischool.ms.accounting.service.TrialBalanceService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of AIReportService for managing AI-generated reports
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIReportServiceImpl implements AIReportService {

    private final AIReportRepository aiReportRepository;
    private final ChatGPTService chatGPTService;
    private final ObjectMapper objectMapper;
    private final AIReportMapper aiReportMapper;
    private final AIReportMessageProducer messageProducer;

    // Services for fetching accounting data
    private final GeneralLedgerService generalLedgerService;
    private final IncomeStatementService incomeStatementService;
    private final TrialBalanceService trialBalanceService;
    private final BalanceSheetService balanceSheetService;
    private final ExpenseReportService expenseReportService;

    @Override
    @Transactional
    public AIReportResponse getReport(AIReportRequest request) {
        // Convert filters to JSON string
        String filtersJson;
        try {
            filtersJson = (request.getFilters() != null)
                ? objectMapper.writeValueAsString(request.getFilters())
                : "{}";
        } catch (JsonProcessingException e) {
            log.error("Error serializing filters to JSON", e);
            filtersJson = "{}";
        }

        // Check if a report with the same parameters already exists
        Optional<AIReport> existingReport = aiReportRepository.findByReportTypeAndStartDateAndEndDateAndFilters(
                request.getReportType(),
                request.getStartDate(),
                request.getEndDate(),
                filtersJson
        );

        if (existingReport.isPresent()) {
            // Return the existing report
            AIReport report = existingReport.get();

            // If the report is in PENDING or PROCESSING status, return it as is
            if (report.getStatus() == ReportStatus.PENDING || report.getStatus() == ReportStatus.PROCESSING) {
                return createResponseFromEntity(report, false);
            }

            // If the report is COMPLETED, return it as cached
            if (report.getStatus() == ReportStatus.COMPLETED) {
                return createResponseFromEntity(report, true);
            }

            // If the report FAILED, create a new one
        }

        // Create a new report with PENDING status
        AIReport newReport = AIReport.builder()
                .reportType(request.getReportType())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .filters(filtersJson)
                .status(ReportStatus.PENDING)
                .build();

        // Save the report to get an ID
        AIReport savedReport = aiReportRepository.save(newReport);

        // Create and send message to RabbitMQ
        AIReportMessage message = AIReportMessage.builder()
                .reportId(savedReport.getId())
                .reportType(request.getReportType())
                .startDate(request.getStartDate())
                .endDate(request.getEndDate())
                .filters(request.getFilters())
                .audience("non-accounting staff")
                .build();

        // Send to queue for async processing
        messageProducer.sendReportMessage(message);

        log.info("AI report request queued for processing: id={}, type={}",
                savedReport.getId(), request.getReportType());

        return createResponseFromEntity(savedReport, false);
    }

    /**
     * Fetch the appropriate accounting data based on the report type, dates, and filters
     *
     * @param reportType The type of report to generate
     * @param startDate The start date of the report period
     * @param endDate The end date of the report period
     * @param filters Additional filters for the report (can be null)
     * @return A map containing the data for the report
     */
    public Map<String, Object> fetchReportData(String reportType, LocalDate startDate, LocalDate endDate, Map<String, Object> filters) {
        Map<String, Object> data = new HashMap<>();

        switch (reportType) {
            case "GENERAL_LEDGER":
                Pageable pageable = PageRequest.of(0, 1000); // Limit to 1000 entries
                List<UUID> accountIds = null; // Could be extracted from filters if needed
                Page<GeneralLedgerEntryDto> ledger = generalLedgerService.getGeneralLedger(
                        startDate,
                        endDate,
                        accountIds,
                        true, // Only posted entries
                        pageable
                );
                data.put("entries", ledger.getContent());
                data.put("totalPages", ledger.getTotalPages());
                data.put("totalElements", ledger.getTotalElements());
                break;

            case "INCOME_STATEMENT":
                IncomeStatementDto incomeStatement = incomeStatementService.generateIncomeStatement(
                        startDate,
                        endDate
                );
                data.put("incomeStatement", incomeStatement);
                break;

            case "TRIAL_BALANCE":
                TrialBalanceDto trialBalance = trialBalanceService.generateTrialBalance(
                        startDate,
                        endDate
                );
                data.put("trialBalance", trialBalance);
                break;

            case "BALANCE_SHEET":
                BalanceSheetDto balanceSheet = balanceSheetService.generateBalanceSheet(
                        startDate,
                        endDate
                );
                data.put("balanceSheet", balanceSheet);
                break;

            case "EXPENSES":
                ExpenseReportDto expenseReport = expenseReportService.generateExpenseReport(
                        startDate,
                        endDate
                );
                data.put("expenseReport", expenseReport);
                break;

            default:
                log.warn("Unknown report type: {}", reportType);
                data.put("error", "Unknown report type: " + reportType);
        }

        return data;
    }

    /**
     * Fetch the appropriate accounting data based on the report request
     *
     * @param request The report request
     * @return A map containing the data for the report
     */
    private Map<String, Object> fetchReportData(AIReportRequest request) {
        return fetchReportData(
            request.getReportType(),
            request.getStartDate(),
            request.getEndDate(),
            request.getFilters()
        );
    }

    /**
     * Create a response DTO from an entity
     */
    private AIReportResponse createResponseFromEntity(AIReport report, boolean fromCache) {
        return aiReportMapper.toDto(report, fromCache);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AIReportResponse> getReportsByType(String reportType, Pageable pageable) {
        log.debug("Fetching AI reports by type: {}, page: {}, size: {}",
                reportType, pageable.getPageNumber(), pageable.getPageSize());

        Page<AIReport> reportPage;

        if (reportType != null && !reportType.isBlank()) {
            // Filter by report type
            reportPage = aiReportRepository.findByReportTypeOrderByCreatedDateDesc(reportType, pageable);
        } else {
            // Get all reports
            reportPage = aiReportRepository.findAllByOrderByCreatedDateDesc(pageable);
        }

        // Map each entity to DTO (all reports from repository are considered cached)
        return reportPage.map(report -> createResponseFromEntity(report, true));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AIReportResponse> getReportById(UUID id) {
        log.debug("Fetching AI report by ID: {}", id);

        return aiReportRepository.findById(id)
                .map(report -> {
                    // If the report is COMPLETED, consider it as cached
                    boolean fromCache = report.getStatus() == ReportStatus.COMPLETED;
                    return createResponseFromEntity(report, fromCache);
                });
    }
}
