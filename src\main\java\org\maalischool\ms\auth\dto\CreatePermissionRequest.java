package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreatePermissionRequest {

    @NotBlank(message = "{validation.permissionName.notBlank}")
    @Size(min = 3, max = 100, message = "{validation.permissionName.size}")
    private String name; // e.g., READ_STUDENT_DATA

    @Size(max = 255, message = "Description cannot exceed 255 characters")
    private String description;
}
