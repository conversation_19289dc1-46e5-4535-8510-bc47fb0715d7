package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.schoolmanagement.dto.SimpleAcademicYearDto;

import java.math.BigDecimal;
import java.time.LocalDate; // Add import for LocalDate
import java.util.UUID;

@Data
@Builder
public class SimpleFeeDto {
    private UUID id;
    private String nameEn;
    private String nameAr;
    private BigDecimal amount;
    private SimpleAcademicYearDto academicYear;
    private LocalDate dueDate; // Add dueDate field
}
