package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegisterRequest {

    @NotBlank(message = "{validation.firstName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.firstName.size}")
    private String firstName;

    @NotBlank(message = "{validation.lastName.notBlank}")
    @Size(min = 2, max = 50, message = "{validation.lastName.size}")
    private String lastName;

    @NotBlank(message = "{validation.email.notBlank}")
    @Email(message = "{validation.email.invalid}")
    @Size(max = 100, message = "{validation.email.size}")
    private String email;

    @NotBlank(message = "{validation.password.notBlank}")
    @Size(min = 8, max = 100, message = "{validation.password.size}")
    private String password;

    @Size(max = 20, message = "{validation.phoneNumber.size}")
    private String phoneNumber; // Optional field

    // Optional: Specify a role name during registration
    // If not provided, DEFAULT_USER_ROLE will be assigned.
    @Size(max = 50, message = "{validation.roleName.size}")
    private String roleName;
}
