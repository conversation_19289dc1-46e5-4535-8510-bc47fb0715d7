package org.maalischool.ms.accounting.repository;

import jakarta.persistence.criteria.Predicate;
import org.maalischool.ms.accounting.model.PaymentMethod;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor; // Import JpaSpecificationExecutor
import org.springframework.stereotype.Repository;
import org.springframework.util.StringUtils; // Import StringUtils

import java.util.ArrayList; // Import ArrayList
import java.util.List; // Import List
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PaymentMethodRepository extends JpaRepository<PaymentMethod, UUID>, JpaSpecificationExecutor<PaymentMethod> { // Extend JpaSpecificationExecutor

    // findByActiveTrue method is removed, filtering will be done via Specification

    Optional<PaymentMethod> findByNameEnIgnoreCaseAndActiveTrue(String nameEn); // Find by English name (keep for specific active check if needed elsewhere)

    /**
     * Finds a payment method by its name, ignoring case.
     *
     * @param nameEn The English name of the payment method.
     * @return An Optional containing the PaymentMethod if found.
     */
    Optional<PaymentMethod> findByNameEnIgnoreCase(String nameEn); // Find by English name (keep for specific checks if needed elsewhere)

    /**
     * Helper static method to create a Specification based on search criteria for Payment Methods.
     *
     * @param search Optional search term for nameEn OR nameAr (contains, case-insensitive)
     * @param active Optional active status filter (exact match)
     * @return A Specification for PaymentMethod
     */
    static Specification<PaymentMethod> createSpecification(String search, Boolean active) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            if (StringUtils.hasText(search)) {
                String searchTermLower = "%" + search.toLowerCase() + "%";
                Predicate nameEnPredicate = criteriaBuilder.like(criteriaBuilder.lower(root.get("nameEn")), searchTermLower);
                Predicate nameArPredicate = criteriaBuilder.like(criteriaBuilder.lower(root.get("nameAr")), searchTermLower);
                predicates.add(criteriaBuilder.or(nameEnPredicate, nameArPredicate));
            }

            if (active != null) {
                predicates.add(criteriaBuilder.equal(root.get("active"), active));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
