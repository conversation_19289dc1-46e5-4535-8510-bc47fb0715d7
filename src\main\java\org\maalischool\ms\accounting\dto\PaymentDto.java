package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class PaymentDto {
    private UUID id;
    private UUID receiptId; // Link back to the receipt
    private SimpleStudentFeeDto studentFee; // Use Simple DTO
    private LocalDate paymentDate; // Usually same as receipt date, but kept for clarity
    private BigDecimal amount; // The amount paid against this specific fee via this payment record
    private Instant createdDate;
    // Add lastModifiedDate if Payment entity has it
    // private Instant lastModifiedDate;
}
