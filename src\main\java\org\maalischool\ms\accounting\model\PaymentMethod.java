package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.PaymentMethodType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "acc_payment_methods")
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class PaymentMethod {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private PaymentMethodType type;

    @Column(nullable = false, length = 100)
    private String nameEn; // e.g., "Main Operating Account", "Petty Cash",
    // "Visa Card **** 1234"
    @Column(nullable = false, length = 100)
    private String nameAr; // e.g., "Main Operating Account", "Petty Cash",
    // "Visa Card **** 1234"

    @Column(length = 500)
    private String descriptionEn; // Renamed from details
    @Column(length = 500)
    private String descriptionAr; // Renamed from details

    // Optional: Link to a ChartOfAccount (e.g., the bank account or cash account)
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chart_of_account_id")
    private ChartOfAccount chartOfAccount;

    @Column(name = "is_active", nullable = false)
    private boolean active = true;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
