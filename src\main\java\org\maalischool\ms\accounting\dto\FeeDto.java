package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.schoolmanagement.dto.SimpleAcademicYearDto;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class FeeDto {
    private UUID id;
    private String nameEn;
    private String nameAr;
    private String descriptionEn;
    private String descriptionAr;
    private BigDecimal amount;
    private SimpleAcademicYearDto academicYear;
    private LocalDate dueDate;
    private SimpleFeeCategoryDto feeCategory; // Use Simple DTO here
    private UUID applicableGradeId;
    private UUID applicableStageId;
    private UUID applicableBranchId;
    private boolean active;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
