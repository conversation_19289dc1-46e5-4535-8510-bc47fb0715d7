package org.maalischool.ms.auth.service.impl;

import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.RegisterRequest;
import org.maalischool.ms.auth.dto.ResetPasswordRequest;
import org.maalischool.ms.auth.dto.UserDto; // Import UserDto
import org.maalischool.ms.auth.exception.*; // Import RoleNotFoundException
import org.maalischool.ms.auth.model.Role;
import org.maalischool.ms.auth.model.User;
import org.maalischool.ms.auth.repository.UserRepository;
import org.maalischool.ms.auth.service.RoleService;
import org.maalischool.ms.auth.service.UserService;
import org.maalischool.ms.shared.email.EmailService; // Import EmailService
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page; // Import Page
import org.springframework.data.domain.Pageable; // Import Pageable
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils; // Import StringUtils

import java.time.LocalDateTime;
import java.util.List; // Import List
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors; // Import Collectors

@Service
@RequiredArgsConstructor // Creates constructor with final fields
public class UserServiceImpl implements UserService {

    private static final Logger log = LoggerFactory.getLogger(UserServiceImpl.class);

    private final UserRepository userRepository;
    private final RoleService roleService; // Use RoleService to find/create roles
    private final PasswordEncoder passwordEncoder;
    private final EmailService emailService; // Inject EmailService

    // Define default role name (consider making this configurable)
    private static final String DEFAULT_USER_ROLE = "ROLE_USER";
    private static final String ACTIVATION_TOKEN_TYPE = "activation";
    private static final String RESET_TOKEN_TYPE = "reset";


    @Override
    @Transactional
    public User registerUser(RegisterRequest registerRequest) {
        if (userRepository.existsByEmail(registerRequest.getEmail())) {
            throw new EmailAlreadyExistsException(registerRequest.getEmail()); // Throw specific exception
        }

        User user = User.builder()
                .firstName(registerRequest.getFirstName())
                .lastName(registerRequest.getLastName())
                .email(registerRequest.getEmail())
                .password(passwordEncoder.encode(registerRequest.getPassword()))
                .phoneNumber(registerRequest.getPhoneNumber())
                .enabled(false) // Require activation
                .accountLocked(false)
                .accountExpired(false)
                .credentialsExpired(false)
                .build();

        // Assign role: Use provided roleName if present and valid, otherwise default
        Role assignedRole;
        String requestedRoleName = registerRequest.getRoleName();

        if (StringUtils.hasText(requestedRoleName)) {
            // If a role name is provided, try to find it
            assignedRole = roleService.findByName(requestedRoleName)
                    .orElseThrow(() -> new RoleNotFoundException(requestedRoleName)); // Throw if requested role doesn't exist
            log.info("Assigning requested role '{}' to new user {}", requestedRoleName, user.getEmail());
        } else {
            // If no role name is provided, assign the default role
            assignedRole = roleService.findOrCreateRole(DEFAULT_USER_ROLE, "Default user role");
            log.info("Assigning default role '{}' to new user {}", DEFAULT_USER_ROLE, user.getEmail());
        }
        user.setRoles(Set.of(assignedRole));


        // Generate activation token
        String token = generateToken(); // Implement token generation logic
        user.setActivationToken(token);
        user.setActivationTokenExpiry(LocalDateTime.now().plusHours(24)); // Example expiry

        User savedUser = userRepository.save(user);
        log.info("User registered successfully with email: {}", savedUser.getEmail());

        // Send activation email
        try {
            emailService.sendActivationEmail(savedUser.getEmail(), savedUser.getFirstName(), token);
        } catch (Exception e) {
            // Log error but don't fail registration if email sending fails
            log.error("Failed to send activation email to {}: {}", savedUser.getEmail(), e.getMessage());
        }

        return savedUser;
    }

    @Override
    @Transactional
    public Optional<User> activateUser(String token) {
        User user = userRepository.findByActivationToken(token)
                .orElseThrow(() -> new InvalidTokenException(ACTIVATION_TOKEN_TYPE)); // Throw specific exception

        if (user.getActivationTokenExpiry() != null && user.getActivationTokenExpiry().isBefore(LocalDateTime.now())) {
            log.warn("Activation attempt failed: Token '{}' for user {} has expired", token, user.getEmail());
            // Optionally regenerate token and resend email here
            // For now, just throw exception
             throw new ExpiredTokenException(ACTIVATION_TOKEN_TYPE); // Throw specific exception
        }

        user.setEnabled(true);
        user.setActivationToken(null); // Clear token after activation
        user.setActivationTokenExpiry(null);
        userRepository.save(user);
        log.info("User account activated successfully for email: {}", user.getEmail());

        return Optional.of(user); // Return optional for consistency, though exception is thrown on failure
    }

    @Override
    @Transactional(readOnly = true) // Add readOnly for find operations
    public Optional<User> findByEmail(String email) {
        return userRepository.findByEmail(email);
    }

    @Override
    @Transactional(readOnly = true) // Add readOnly for find operations
    public Optional<User> findById(UUID id) {
        return userRepository.findById(id);
    }

    @Override
    @Transactional
    public void initiatePasswordReset(String email) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        if (userOptional.isEmpty()) {
            log.warn("Password reset initiation requested for non-existent email {}", email);
            // Do not throw UserNotFoundException here to prevent email enumeration
            // Simply log and return silently. The response in controller confirms this.
            return;
        }

        User user = userOptional.get();
        String token = generateToken(); // Implement token generation logic
        user.setResetToken(token);
        user.setResetTokenExpiry(LocalDateTime.now().plusHours(1)); // Shorter expiry for reset

        userRepository.save(user);
        log.info("Password reset token generated for {}: {}", user.getEmail(), token);

        // Send password reset email
         try {
            emailService.sendPasswordResetEmail(user.getEmail(), user.getFirstName(), token);
        } catch (Exception e) {
            // Log error but don't fail the operation if email sending fails
            log.error("Failed to send password reset email to {}: {}", user.getEmail(), e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean completePasswordReset(ResetPasswordRequest resetPasswordRequest) {
        User user = userRepository.findByResetToken(resetPasswordRequest.getToken())
                 .orElseThrow(() -> new InvalidTokenException(RESET_TOKEN_TYPE)); // Throw specific exception

        if (user.getResetTokenExpiry() != null && user.getResetTokenExpiry().isBefore(LocalDateTime.now())) {
            log.warn("Password reset completion failed: Token '{}' for user {} has expired", resetPasswordRequest.getToken(), user.getEmail());
            user.setResetToken(null); // Clear expired token
            user.setResetTokenExpiry(null);
            userRepository.save(user); // Save the cleared token state
            throw new ExpiredTokenException(RESET_TOKEN_TYPE); // Throw specific exception
        }

        // Reset password
        user.setPassword(passwordEncoder.encode(resetPasswordRequest.getNewPassword()));
        user.setResetToken(null); // Clear token after successful reset
        user.setResetTokenExpiry(null);
        user.setCredentialsExpired(false); // Mark credentials as non-expired

        userRepository.save(user);
        log.info("Password reset successfully for user: {}", user.getEmail());
        return true; // Return true on success
    }

    @Override
    @Transactional(readOnly = true) // Add readOnly for check operations
    public boolean existsByEmail(String email) {
        return userRepository.existsByEmail(email);
    }

    @Override
    @Transactional
    public User assignRoleToUser(UUID userId, String roleName) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
        Role role = roleService.findByName(roleName)
                .orElseThrow(() -> new RoleNotFoundException(roleName));

        // Add role using the helper method in User entity if it exists, or manage the set directly
        user.getRoles().add(role);
        // user.addRole(role); // If using helper method

        User updatedUser = userRepository.save(user);
        log.info("Assigned role '{}' to user '{}'", roleName, user.getEmail());
        return updatedUser;
    }

    @Override
    @Transactional
    public User removeRoleFromUser(UUID userId, String roleName) {
         User user = userRepository.findById(userId)
                .orElseThrow(() -> new UserNotFoundException(userId));
        Role role = roleService.findByName(roleName)
                .orElseThrow(() -> new RoleNotFoundException(roleName)); // Role must exist to be removed

        if (user.getRoles().contains(role)) {
            user.getRoles().remove(role);
            // user.removeRole(role); // If using helper method
            User updatedUser = userRepository.save(user);
            log.info("Removed role '{}' from user '{}'", roleName, user.getEmail());
            return updatedUser;
        } else {
            log.warn("Attempted to remove role '{}' which was not assigned to user '{}'", roleName, user.getEmail());
            // Optionally throw an exception here, or just return the user as is
            // throw new RoleNotFoundException("User does not have role: " + roleName);
            return user; // Return user unchanged
        }
    }

    @Override
    @Transactional(readOnly = true) // Add readOnly for find operations
    public Page<UserDto> findAllUsers(String roleName, Pageable pageable) { // Updated signature
        Page<User> userPage;
        if (StringUtils.hasText(roleName)) {
            // Filter by role name if provided
            log.debug("Finding users with role: {}", roleName);
            userPage = userRepository.findByRoles_Name(roleName, pageable);
        } else {
            // Otherwise, find all users
            log.debug("Finding all users (no role filter)");
            userPage = userRepository.findAll(pageable);
        }
        return userPage.map(this::mapToUserDto); // Map Page<User> to Page<UserDto>
    }


    // --- Helper Methods ---

    /**
     * Generates a simple random token (e.g., UUID).
     * Replace with a more secure random string generator if needed.
     *
     * @return A unique token string.
     */
    private String generateToken() {
        return UUID.randomUUID().toString();
    }

    /**
     * Maps a User entity to a UserDto.
     *
     * @param user The User entity.
     * @return The corresponding UserDto.
     */
    private UserDto mapToUserDto(User user) {
        List<String> roleNames = user.getRoles().stream()
                                     .map(Role::getName)
                                     .collect(Collectors.toList());
        return UserDto.builder()
                .id(user.getId())
                .firstName(user.getFirstName())
                .lastName(user.getLastName())
                .email(user.getEmail())
                .phoneNumber(user.getPhoneNumber())
                .enabled(user.isEnabled())
                .accountLocked(!user.isAccountNonLocked()) // Invert logic for DTO
                .accountExpired(!user.isAccountNonExpired()) // Invert logic for DTO
                .credentialsExpired(!user.isCredentialsNonExpired()) // Invert logic for DTO
                .roles(roleNames)
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt())
                .build();
    }
}
