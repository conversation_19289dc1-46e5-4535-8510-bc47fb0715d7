package org.maalischool.ms.accounting.repository;

import org.maalischool.ms.accounting.model.Item;
import org.maalischool.ms.accounting.model.enums.ItemType;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface ItemRepository extends JpaRepository<Item, UUID> {

    /**
     * Finds an item by its unique item code (case-insensitive).
     *
     * @param itemCode The item code.
     * @return An Optional containing the Item if found.
     */
    Optional<Item> findByItemCodeIgnoreCase(String itemCode); // Renamed from findBySkuIgnoreCase

    List<Item> findByTypeAndActiveTrueOrderByNameEnAsc(ItemType type); // Order by English name

    List<Item> findByActiveTrueOrderByNameEnAsc(); // Order by English name

    // Find items where reorder level is set (service layer would calculate stock and compare)
    @Query("SELECT i FROM Item i WHERE i.active = true AND i.reorderLevel IS NOT NULL ORDER BY i.nameEn") // Order by English name
    List<Item> findItemsWithReorderLevel();

    List<Item> findByNameEnContainingIgnoreCaseAndActiveTrueOrderByNameEnAsc(String nameEn); // Search and order by English name
}
