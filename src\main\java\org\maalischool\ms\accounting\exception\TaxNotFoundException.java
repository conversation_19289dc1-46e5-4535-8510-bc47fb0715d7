package org.maalischool.ms.accounting.exception;

import java.util.UUID;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class TaxNotFoundException extends RuntimeException {

    public TaxNotFoundException(UUID id) {
        super("Tax not found with id: " + id);
    }

    public TaxNotFoundException(String message) {
        super(message);
    }
}