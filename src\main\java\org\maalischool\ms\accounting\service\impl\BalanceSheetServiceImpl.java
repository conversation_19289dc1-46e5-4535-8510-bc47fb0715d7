package org.maalischool.ms.accounting.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.maalischool.ms.accounting.dto.BalanceSheetAccountDto;
import org.maalischool.ms.accounting.dto.BalanceSheetDto;
import org.maalischool.ms.accounting.dto.BalanceSheetSectionDto;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.enums.AccountingCategory;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.JournalEntryLineRepository;
import org.maalischool.ms.accounting.service.BalanceSheetService;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class BalanceSheetServiceImpl implements BalanceSheetService {

        private final ChartOfAccountRepository chartOfAccountRepository;
        private final JournalEntryLineRepository journalEntryLineRepository;

        @Override
        public BalanceSheetDto generateBalanceSheet(LocalDate fromDate, LocalDate toDate) {
                // Fetch active Asset, Liability, and Equity accounts
                List<AccountingCategory> balanceSheetCategories = Arrays.asList(
                                AccountingCategory.ASSET,
                                AccountingCategory.LIABILITY,
                                AccountingCategory.EQUITY);

                Specification<ChartOfAccount> spec = ChartOfAccountRepository.createSpecification(
                                null, null, null, null, true); // Filter by active = true

                List<ChartOfAccount> accounts = chartOfAccountRepository.findAll(spec).stream()
                                .filter(account -> balanceSheetCategories.contains(account.getCategory()))
                                .collect(Collectors.toList());

                // Calculate balance for each account within the date range and create DTOs
                List<BalanceSheetAccountDto> accountDtos = accounts.stream()
                                .map(account -> {
                                        // Calculate total debits and credits for the period
                                        BigDecimal totalDebits = journalEntryLineRepository
                                                        .calculateTotalDebitsBetweenDates(account.getId(), fromDate,
                                                                        toDate);
                                        BigDecimal totalCredits = journalEntryLineRepository
                                                        .calculateTotalCreditsBetweenDates(account.getId(), fromDate,
                                                                        toDate);

                                        // Calculate the ending balance as of toDate
                                        BigDecimal endingBalance = journalEntryLineRepository
                                                        .calculateAccountBalanceAsOfDate(account.getId(), toDate);

                                        // For Liabilities and Equity, the natural balance is credit, so we might need
                                        // to negate the debit-based sum from calculateAccountBalanceAsOfDate
                                        // The calculateAccountBalanceAsOfDate sums DEBIT - CREDIT.
                                        // For Assets, this is correct.
                                        // For Liabilities/Equity, a positive result from the query means total debits >
                                        // total credits, which is a debit balance.
                                        // A credit balance (natural for L/E) would be a negative result from the query.
                                        // So, for L/E, we need to negate the result to show the natural credit balance
                                        // as positive.
                                        if (account.getCategory() == AccountingCategory.LIABILITY
                                                        || account.getCategory() == AccountingCategory.EQUITY) {
                                                endingBalance = endingBalance.negate();
                                        }

                                        return BalanceSheetAccountDto.builder()
                                                        .accountId(account.getId())
                                                        .accountNumber(account.getAccountNumber())
                                                        .accountNameEn(account.getNameEn())
                                                        .accountNameAr(account.getNameAr())
                                                        .debit(totalDebits)
                                                        .credit(totalCredits)
                                                        .balance(endingBalance)
                                                        .build();
                                })
                                .collect(Collectors.toList());

                // Group accounts by category and create section DTOs
                BalanceSheetSectionDto assetsSection = createSectionDto(accountDtos, AccountingCategory.ASSET,
                                accounts);
                BalanceSheetSectionDto liabilitiesSection = createSectionDto(accountDtos, AccountingCategory.LIABILITY,
                                accounts);
                BalanceSheetSectionDto equitySection = createSectionDto(accountDtos, AccountingCategory.EQUITY,
                                accounts);

                // Build the final BalanceSheetDto
                return BalanceSheetDto.builder()
                                .reportDate(toDate) // Still using toDate as the report date, as it's the end of the
                                                    // period
                                .assets(assetsSection)
                                .liabilities(liabilitiesSection)
                                .equity(equitySection)
                                .build();
        }

        // Refactored helper method to create a section DTO from a list of account DTOs
        // and original accounts
        private BalanceSheetSectionDto createSectionDto(List<BalanceSheetAccountDto> accountDtos,
                        AccountingCategory category, List<ChartOfAccount> originalAccounts) {
                List<BalanceSheetAccountDto> sectionAccounts = accountDtos.stream()
                                .filter(dto -> originalAccounts.stream()
                                                .anyMatch(acc -> acc.getId().equals(dto.getAccountId())
                                                                && acc.getCategory() == category))
                                .collect(Collectors.toList());

                BigDecimal totalDebit = sectionAccounts.stream()
                                .map(BalanceSheetAccountDto::getDebit)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalCredit = sectionAccounts.stream()
                                .map(BalanceSheetAccountDto::getCredit)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                BigDecimal totalBalance = sectionAccounts.stream()
                                .map(BalanceSheetAccountDto::getBalance)
                                .reduce(BigDecimal.ZERO, BigDecimal::add);

                return BalanceSheetSectionDto.builder()
                                .category(category)
                                .accounts(sectionAccounts)
                                .totalDebit(totalDebit)
                                .totalCredit(totalCredit)
                                .totalBalance(totalBalance)
                                .build();
        }
}
