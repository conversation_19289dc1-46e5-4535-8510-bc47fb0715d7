package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * DTO for expense report containing detailed expense information and summary statistics.
 */
@Data
@Builder
public class ExpenseReportDto {
    private LocalDate startDate;
    private LocalDate endDate;
    private List<ExpenseDto> expenses;
    private List<ExpenseReportSummaryDto> categorySummaries;
    private BigDecimal totalAmount;
    private BigDecimal totalTaxAmount;
    private BigDecimal totalAmountWithTax;
    private int totalExpenseCount;
}
