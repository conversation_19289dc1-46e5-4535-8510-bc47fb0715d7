package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.student.dto.SimpleStudentDto; // Assuming this exists

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@Data
@Builder
public class ReceiptDto {
    private UUID id;
    private String receiptNumber;
    private SimpleStudentDto student; // Use Simple DTO
    private LocalDate receiptDate;
    private SimplePaymentMethodDto paymentMethod; // Use Simple DTO
    private BigDecimal totalAmountReceived;
    private String referenceNumber;
    private String notes;
    private List<PaymentDto> payments; // List of allocations made under this receipt
    private boolean cancelled; // Add cancelled flag
    private String cancellationReason;
    private LocalDate cancellationDate;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
