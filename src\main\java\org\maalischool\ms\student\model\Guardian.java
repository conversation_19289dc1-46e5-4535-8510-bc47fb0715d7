package org.maalischool.ms.student.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.maalischool.ms.auth.model.User; // Import User
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Entity
@Table(name = "guardian", uniqueConstraints = { // Renamed table
        @UniqueConstraint(columnNames = { "id_type", "national_id" }) // Assuming ID type + number is unique
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = { "userAccount", "students" })
@EntityListeners(AuditingEntityListener.class)
public class Guardian { // Renamed class

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    // Link to the User account for login credentials etc.
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", referencedColumnName = "id", unique = true)
    private User userAccount;

    // Basic guardian info
    @NotBlank(message = "{validation.guardian.occupation.notBlank}") // Updated key
    @Size(max = 100, message = "{validation.guardian.occupation.size}") // Updated key
    @Column(length = 100)
    private String occupation;

    @Size(max = 255, message = "{validation.guardian.address.size}") // Updated key
    private String address;

    @NotBlank(message = "{validation.guardian.nationalId.notBlank}") // New field validation
    @Size(max = 50, message = "{validation.guardian.nationalId.size}") // New field validation
    @Column(name = "national_id", nullable = false, length = 50)
    private String nationalId;

    @NotNull(message = "{validation.guardian.idType.notNull}") // New field validation
    @Enumerated(EnumType.STRING)
    @Column(name = "id_type", nullable = false, length = 20)
    private IdType idType;

    // Nationality relationship
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "nationality_id")
    private Country nationality;

    // One Guardian can have multiple Students
    // Update mappedBy to reflect the field name in the Student entity
    @OneToMany(mappedBy = "guardian", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY)
    @Builder.Default
    private List<Student> students = new ArrayList<>();

    // --- Auditing Fields ---
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;
}
