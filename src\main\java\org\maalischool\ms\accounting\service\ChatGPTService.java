package org.maalischool.ms.accounting.service;

import java.util.Map;

/**
 * Service interface for ChatGPT integration
 */
public interface ChatGPTService {
    
    /**
     * Generate a human-readable report from accounting data using ChatGPT
     * 
     * @param reportType The type of report to generate
     * @param reportData The accounting data to use for the report
     * @param audience The target audience for the report (e.g., "non-accounting staff")
     * @return The generated report content
     */
    String generateReport(String reportType, Map<String, Object> reportData, String audience);
}
