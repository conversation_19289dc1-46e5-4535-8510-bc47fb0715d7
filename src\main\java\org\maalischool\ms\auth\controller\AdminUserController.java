package org.maalischool.ms.auth.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.maalischool.ms.auth.dto.UserDto;
import org.maalischool.ms.auth.service.UserService;
import org.maalischool.ms.exception.ErrorResponse;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam; // Import RequestParam
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/api/v1/admin/users") // Admin endpoint prefix for users
@RequiredArgsConstructor
@Tag(name = "User Management (Admin)", description = "Endpoints for managing users (Requires ADMIN role)")
@SecurityRequirement(name = "Bearer Authentication") // Indicate endpoints require Bearer token
@PreAuthorize("hasRole('ADMIN')") // Secure all methods in this controller
public class AdminUserController {

    private final UserService userService;
    // No MessageSource needed here currently as no custom messages are returned

    @Operation(summary = "Get all users (paginated)", description = "Retrieves a paginated list of all registered users. Can be filtered by role name.")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "List of users retrieved successfully"),
            @ApiResponse(responseCode = "403", description = "Forbidden - User does not have ADMIN role",
                         content = @Content(mediaType = "application/json", schema = @Schema(implementation = ErrorResponse.class)))
    })
    @GetMapping
    public ResponseEntity<Page<UserDto>> getAllUsers(
            // Add optional RequestParam for filtering by roleName
            @Parameter(description = "Optional role name to filter users by (e.g., ROLE_USER, ROLE_ADMIN)")
            @RequestParam(required = false) String roleName,
            // Use @ParameterObject to improve Swagger UI for Pageable
            @ParameterObject Pageable pageable
    ) {
        // Pass roleName to the service method
        Page<UserDto> userPage = userService.findAllUsers(roleName, pageable);
        return ResponseEntity.ok(userPage);
    }

    // TODO: Add other user management endpoints here (e.g., GET /users/{id}, PUT /users/{id}, DELETE /users/{id}, POST /users/{id}/lock etc.)
    // Remember to secure them appropriately and use MessageSource for any custom response messages.
}
