package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND)
public class DiscountNotFoundException extends RuntimeException {

    public DiscountNotFoundException(UUID id) {
        super("Discount not found with ID: " + id);
    }

    public DiscountNotFoundException(String code) {
        super("Discount not found with code: " + code);
    }

     public DiscountNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
