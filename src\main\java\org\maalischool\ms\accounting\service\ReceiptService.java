package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateReceiptRequest;
import org.maalischool.ms.accounting.dto.ReceiptDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.UUID;

/**
 * Service interface for managing Receipts (incoming payments).
 */
public interface ReceiptService {

    /**
     * Creates a new receipt record and handles payment allocations.
     *
     * @param request the request object containing receipt and allocation details.
     * @return the created receipt DTO.
     * @throws org.maalischool.ms.student.exception.StudentNotFoundException if the student ID is invalid.
     * @throws org.maalischool.ms.accounting.exception.PaymentMethodNotFoundException if the payment method ID is invalid.
     * @throws org.maalischool.ms.accounting.exception.StudentFeeNotFoundException if any allocated student fee ID is invalid.
     * @throws org.maalischool.ms.accounting.exception.InsufficientAmountException if the total allocated amount exceeds the receipt amount.
     * @throws org.maalischool.ms.accounting.exception.OverpaymentException if an allocation exceeds the amount due for a student fee.
     */
    ReceiptDto createReceipt(CreateReceiptRequest request);

    /**
     * Retrieves a receipt by its ID.
     *
     * @param id the UUID of the receipt.
     * @return the receipt DTO including allocation details.
     * @throws org.maalischool.ms.accounting.exception.ReceiptNotFoundException if the receipt is not found.
     */
    ReceiptDto getReceiptById(UUID id);

    /**
     * Retrieves a receipt by its unique receipt number.
     *
     * @param receiptNumber the unique receipt number.
     * @return the receipt DTO including allocation details.
     * @throws org.maalischool.ms.accounting.exception.ReceiptNotFoundException if the receipt is not found.
     */
    ReceiptDto getReceiptByNumber(String receiptNumber);

    /**
     * Retrieves all receipts with pagination.
     *
     * @param pageable pagination information.
     * @return a page of receipt DTOs.
     */
    Page<ReceiptDto> getAllReceipts(Pageable pageable);

    /**
     * Finds receipts within a specific date range.
     *
     * @param startDate the start date (inclusive).
     * @param endDate   the end date (inclusive).
     * @param pageable  pagination information.
     * @return a page of receipt DTOs within the date range.
     */
    Page<ReceiptDto> findReceiptsByDateRange(LocalDate startDate, LocalDate endDate, Pageable pageable);

    /**
     * Finds receipts associated with a specific student.
     *
     * @param studentId the UUID of the student.
     * @param pageable  pagination information.
     * @return a page of receipt DTOs for the student.
     */
    Page<ReceiptDto> findReceiptsByStudent(UUID studentId, Pageable pageable);

    /**
     * Finds receipts using a specific payment method.
     *
     * @param paymentMethodId the UUID of the payment method.
     * @param pageable        pagination information.
     * @return a page of receipt DTOs using the payment method.
     */
    Page<ReceiptDto> findReceiptsByPaymentMethod(UUID paymentMethodId, Pageable pageable);

    // Note: Updating receipts is complex due to allocations affecting student fee balances.
    // Usually, receipts are not updated. If incorrect, it might be cancelled/reversed
    // and a new correct receipt issued. Deletion is also generally discouraged once created.

    /**
     * Cancels a receipt. This might involve reversing payment allocations.
     * Business logic for cancellation needs careful definition (e.g., creating reversal entries).
     *
     * @param id     the UUID of the receipt to cancel.
     * @param reason the reason for cancellation.
     * @throws org.maalischool.ms.accounting.exception.ReceiptNotFoundException if the receipt is not found.
     * @throws IllegalStateException if the receipt is already cancelled or cannot be cancelled.
     */
    void cancelReceipt(UUID id, String reason); // Implementation requires careful thought on reversal logic

}
