package org.maalischool.ms.student.repository;

import org.maalischool.ms.student.model.Country;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface CountryRepository extends JpaRepository<Country, UUID>, JpaSpecificationExecutor<Country> {
    
    Optional<Country> findByCode(String code);
    Optional<Country> findByCodeIgnoreCase(String code);
    Optional<Country> findByNameEnIgnoreCase(String nameEn);
    Optional<Country> findByNameArIgnoreCase(String nameAr);
    boolean existsByCode(String code);

    /**
     * Finds Countries by searching across nameAr, nameEn, and code fields.
     * If searchTerm is null or empty, returns all countries.
     *
     * @param searchTerm The term to search for across multiple fields
     * @param pageable Pagination and sorting information
     * @return A Page of matching Country entities
     */
    @Query("SELECT c FROM Country c WHERE " +
           "(:searchTerm IS NULL OR :searchTerm = '' OR " +
           "LOWER(c.nameAr) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.nameEn) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(c.code) LIKE LOWER(CONCAT('%', :searchTerm, '%')))")
    Page<Country> findByKeyword(@Param("searchTerm") String searchTerm, Pageable pageable);
}
