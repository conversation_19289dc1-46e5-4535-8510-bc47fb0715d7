package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.FeeStatus;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

import org.maalischool.ms.accounting.model.enums.FeeStatus; // Ensure FeeStatus is imported

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class SimpleStudentFeeDto {
    private UUID id;
    private UUID studentId; // Keep studentId for simplicity in this DTO
    private SimpleFeeDto fee; // Nested Fee details
    // Remove academicYear, dueDate as they are now inside SimpleFeeDto
    private BigDecimal amountDue; // Amount due for this specific student fee assignment
    private FeeStatus status;
}
