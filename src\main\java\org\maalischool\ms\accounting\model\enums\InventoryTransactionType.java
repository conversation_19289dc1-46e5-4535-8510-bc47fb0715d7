package org.maalischool.ms.accounting.model.enums;

public enum InventoryTransactionType {
    PURCHASE_RECEIPT,   // Goods received from supplier
    USAGE,              // Item consumed internally
    SALE,               // Item sold (if applicable)
    TRANSFER_IN,        // Item transferred from another location/dept
    TRANSFER_OUT,       // Item transferred to another location/dept
    ADJUSTMENT_POSITIVE,// Stock count adjustment (increase)
    ADJUSTMENT_NEGATIVE,// Stock count adjustment (decrease)
    RETURN_TO_SUPPLIER, // Goods returned to supplier
    OPENING_BALANCE     // Initial stock entry
}
