package org.maalischool.ms.accounting.service;

import java.util.Optional;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.AIReportRequest;
import org.maalischool.ms.accounting.dto.AIReportResponse;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service interface for managing AI-generated reports
 */
public interface AIReportService {

    /**
     * Generate or retrieve an AI report based on the request parameters
     *
     * @param request The report request containing type, date range, and filters
     * @return The AI-generated report response
     */
    AIReportResponse getReport(AIReportRequest request);

    /**
     * Get a paginated list of AI reports, optionally filtered by report type
     *
     * @param reportType The type of report to filter by (can be null to get all reports)
     * @param pageable Pagination information
     * @return A page of AI report responses
     */
    Page<AIReportResponse> getReportsByType(String reportType, Pageable pageable);

    /**
     * Get a single AI report by its ID
     *
     * @param id The UUID of the report to retrieve
     * @return An Optional containing the report if found
     */
    Optional<AIReportResponse> getReportById(UUID id);
}
