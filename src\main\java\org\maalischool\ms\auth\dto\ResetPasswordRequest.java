package org.maalischool.ms.auth.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResetPasswordRequest {

    @NotBlank(message = "{validation.token.notBlank}")
    private String token;

    @NotBlank(message = "{validation.password.notBlank}")
    @Size(min = 8, max = 100, message = "{validation.password.size}")
    private String newPassword;
}
