package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateFixedAssetRequest;
import org.maalischool.ms.accounting.dto.FixedAssetDto;
import org.maalischool.ms.accounting.dto.UpdateFixedAssetRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing Fixed Assets.
 */
public interface FixedAssetService {

    /**
     * Creates a new fixed asset record.
     *
     * @param request the request object containing fixed asset details.
     * @return the created fixed asset DTO.
     * @throws org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException if related accounts are not found.
     */
    FixedAssetDto createFixedAsset(CreateFixedAssetRequest request);

    /**
     * Retrieves a fixed asset by its ID.
     *
     * @param id the UUID of the fixed asset.
     * @return the fixed asset DTO.
     * @throws org.maalischool.ms.accounting.exception.FixedAssetNotFoundException if the asset is not found.
     */
    FixedAssetDto getFixedAssetById(UUID id);

    /**
     * Retrieves all fixed assets with pagination.
     *
     * @param pageable pagination information.
     * @return a page of fixed asset DTOs.
     */
    Page<FixedAssetDto> getAllFixedAssets(Pageable pageable);

    /**
     * Updates an existing fixed asset record.
     *
     * @param id      the UUID of the fixed asset to update.
     * @param request the request object containing updated fixed asset details.
     * @return the updated fixed asset DTO.
     * @throws org.maalischool.ms.accounting.exception.FixedAssetNotFoundException if the asset is not found.
     * @throws org.maalischool.ms.accounting.exception.ChartOfAccountNotFoundException if related accounts in the request are not found.
     */
    FixedAssetDto updateFixedAsset(UUID id, UpdateFixedAssetRequest request);

    /**
     * Deletes a fixed asset record by its ID (Disposal).
     * This might involve creating journal entries for disposal (gain/loss).
     * Consider if a soft delete or status change is more appropriate.
     *
     * @param id the UUID of the fixed asset to delete/dispose.
     * @throws org.maalischool.ms.accounting.exception.FixedAssetNotFoundException if the asset is not found.
     */
    void disposeFixedAsset(UUID id, /* Add disposal details like date, proceeds */ String disposalNotes); // Renamed for clarity

    // Potential future methods:
    // - Calculate and record depreciation
    // - Get assets by category/location
}
