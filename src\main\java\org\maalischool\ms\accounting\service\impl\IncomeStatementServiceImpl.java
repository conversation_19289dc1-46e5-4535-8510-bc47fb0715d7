package org.maalischool.ms.accounting.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.maalischool.ms.accounting.dto.AccountBalanceDto;
import org.maalischool.ms.accounting.dto.IncomeStatementDto;
import org.maalischool.ms.accounting.model.ChartOfAccount;
import org.maalischool.ms.accounting.model.JournalEntryLine;
import org.maalischool.ms.accounting.model.enums.AccountingCategory;
import org.maalischool.ms.accounting.model.enums.TransactionType;
import org.maalischool.ms.accounting.repository.ChartOfAccountRepository;
import org.maalischool.ms.accounting.repository.JournalEntryLineRepository;
import org.maalischool.ms.accounting.service.IncomeStatementService;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class IncomeStatementServiceImpl implements IncomeStatementService {

    private final JournalEntryLineRepository journalEntryLineRepository;
    private final ChartOfAccountRepository chartOfAccountRepository;

    @Override
    public IncomeStatementDto generateIncomeStatement(LocalDate startDate, LocalDate endDate) {
        // Fetch all relevant journal entry lines within the date range
        // Assuming JournalEntryLineRepository has a method to find by JournalEntry entryDate between
        List<JournalEntryLine> lines = journalEntryLineRepository.findByJournalEntry_EntryDateBetween(startDate, endDate);

        // Filter for Income and Expense accounts and group by ChartOfAccount
        Map<ChartOfAccount, List<JournalEntryLine>> groupedLines = lines.stream()
                .filter(line -> {
                    AccountingCategory accountType = line.getChartOfAccount().getCategory();
                    return accountType == AccountingCategory.REVENUE || accountType == AccountingCategory.EXPENSE;
                })
                .collect(Collectors.groupingBy(JournalEntryLine::getChartOfAccount));

        List<AccountBalanceDto> incomeAccounts = new ArrayList<>();
        List<AccountBalanceDto> expenseAccounts = new ArrayList<>();
        BigDecimal totalRevenue = BigDecimal.ZERO;
        BigDecimal totalExpenses = BigDecimal.ZERO;

        // Calculate balances for each account
        for (Map.Entry<ChartOfAccount, List<JournalEntryLine>> entry : groupedLines.entrySet()) {
            ChartOfAccount account = entry.getKey();
            List<JournalEntryLine> accountLines = entry.getValue();

            BigDecimal debitTotal = accountLines.stream()
                    .filter(line -> line.getType() == TransactionType.DEBIT)
                    .map(JournalEntryLine::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal creditTotal = accountLines.stream()
                    .filter(line -> line.getType() == TransactionType.CREDIT)
                    .map(JournalEntryLine::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            BigDecimal balance;
            if (account.getCategory() == AccountingCategory.REVENUE) {
                // Revenue accounts have a normal credit balance
                balance = creditTotal.subtract(debitTotal);
                incomeAccounts.add(new AccountBalanceDto(account.getId(), account.getAccountNumber(), account.getNameEn(), balance));
                totalRevenue = totalRevenue.add(balance);
            } else { // AccountType.EXPENSE
                // Expense accounts have a normal debit balance
                balance = debitTotal.subtract(creditTotal);
                expenseAccounts.add(new AccountBalanceDto(account.getId(), account.getAccountNumber(), account.getNameEn(), balance));
                totalExpenses = totalExpenses.add(balance);
            }
        }

        BigDecimal netIncome = totalRevenue.subtract(totalExpenses);

        return new IncomeStatementDto(startDate, endDate, incomeAccounts, expenseAccounts, totalRevenue, totalExpenses, netIncome);
    }
}