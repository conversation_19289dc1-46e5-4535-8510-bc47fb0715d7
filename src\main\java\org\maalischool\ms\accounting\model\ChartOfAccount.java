package org.maalischool.ms.accounting.model;

import java.time.Instant;
import java.util.UUID;

import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.AccountingCategory;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EntityListeners;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "acc_chart_of_accounts", uniqueConstraints = {
        @UniqueConstraint(columnNames = "account_number")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id") // Base equality on ID
@EntityListeners(AuditingEntityListener.class)
public class ChartOfAccount {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR) // Ensure UUID is stored as VARCHAR
    private UUID id;

    public UUID getId() {
        return id;
    }

    @Column(name = "account_number", nullable = false, unique = true, length = 50)
    private String accountNumber;

    public String getAccountNumber() {
        return accountNumber;
    }

    @Column(name = "name_en", nullable = false, length = 255)
    private String nameEn;

    public String getNameEn() {
        return nameEn;
    }

    @Column(name = "name_ar", nullable = false, length = 255)
    private String nameAr;

    public String getNameAr() {
        return nameAr;
    }

    @Column(name = "description_en", length = 1000)
    private String descriptionEn;

    public String getDescriptionEn() {
        return descriptionEn;
    }

    @Column(name = "description_ar", length = 1000)
    private String descriptionAr;

    public String getDescriptionAr() {
        return descriptionAr;
    }

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 50)
    private AccountingCategory category;

    public AccountingCategory getCategory() {
        return category;
    }

    @Column(name = "is_active", nullable = false)
    private boolean active = true;

    public boolean isActive() {
        return active;
    }

    // Optional: For hierarchical charts of accounts
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_account_id")
    private ChartOfAccount parentAccount;

    public ChartOfAccount getParentAccount() {
        return parentAccount;
    }

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    public Instant getCreatedDate() {
        return createdDate;
    }

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;

    public Instant getLastModifiedDate() {
        return lastModifiedDate;
    }

    // Consider adding createdBy, lastModifiedBy if user auditing is set up
}
