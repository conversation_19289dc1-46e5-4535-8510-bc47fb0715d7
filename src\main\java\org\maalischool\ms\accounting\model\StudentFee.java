package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.maalischool.ms.student.model.Student; // Import Student from student module
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.UUID;

@Entity
@Table(name = "acc_student_fees", indexes = {
        @Index(name = "idx_studentfee_student", columnList = "student_id"),
        @Index(name = "idx_studentfee_fee", columnList = "fee_id"),
        @Index(name = "idx_studentfee_status", columnList = "status")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class StudentFee {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "student_id", nullable = false)
    private Student student; // Reference Student entity from student module

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "fee_id", nullable = false)
    private Fee fee;

    @Column(name = "assigned_date", nullable = false)
    private LocalDate assignedDate;

    @Column(name = "due_date", nullable = false)
    private LocalDate dueDate;

    @Column(name = "original_amount", nullable = false, precision = 19, scale = 4)
    private BigDecimal originalAmount; // The amount from the Fee definition

    @Column(name = "discount_amount", nullable = false, precision = 19, scale = 4)
    @Builder.Default
    private BigDecimal discountAmount = BigDecimal.ZERO; // Total discount applied

    @Column(name = "amount_due", nullable = false, precision = 19, scale = 4)
    private BigDecimal amountDue; // originalAmount - discountAmount

    @Column(name = "amount_paid", nullable = false, precision = 19, scale = 4)
    @Builder.Default
    private BigDecimal amountPaid = BigDecimal.ZERO;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 20)
    private FeeStatus status;

    @Column(name = "notes_en", length = 500)
    private String notesEn;

    @Column(name = "notes_ar", length = 500)
    private String notesAr;

    // Link to payments made against this fee
    @OneToMany(mappedBy = "studentFee", cascade = CascadeType.PERSIST, fetch = FetchType.LAZY) // Don't cascade ALL, payments are separate
    @Builder.Default
    private List<Payment> payments = new ArrayList<>();

    // Link to applied discounts
    @OneToMany(mappedBy = "studentFee", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<StudentFeeDiscount> appliedDiscounts = new HashSet<>();

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;

    @PrePersist
    @PreUpdate
    private void calculateAmountDueAndStatus() {
        if (this.originalAmount != null && this.discountAmount != null) {
            this.amountDue = this.originalAmount.subtract(this.discountAmount);
        } else if (this.originalAmount != null) {
            this.amountDue = this.originalAmount;
        } else {
            this.amountDue = BigDecimal.ZERO;
        }

        // Basic status update logic (can be more complex)
        if (this.status != FeeStatus.WAIVED && this.status != FeeStatus.CANCELLED) {
            if (amountPaid.compareTo(BigDecimal.ZERO) <= 0) {
                this.status = FeeStatus.UNPAID;
            } else if (amountPaid.compareTo(amountDue) >= 0) {
                this.status = FeeStatus.PAID;
            } else {
                this.status = FeeStatus.PARTIALLY_PAID;
            }
            // Add OVERDUE logic based on dueDate if needed elsewhere
        }
    }
}
