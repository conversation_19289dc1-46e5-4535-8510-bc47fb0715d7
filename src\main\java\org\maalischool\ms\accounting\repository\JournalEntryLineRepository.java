package org.maalischool.ms.accounting.repository;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

import org.maalischool.ms.accounting.model.JournalEntryLine;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface JournalEntryLineRepository extends JpaRepository<JournalEntryLine, UUID> {

       @Query("SELECT jel FROM JournalEntryLine jel JOIN jel.journalEntry je " +
              "WHERE je.entryDate BETWEEN :startDate AND :endDate " +
              "AND je.postedDate IS NOT NULL")
       List<JournalEntryLine> findByJournalEntry_EntryDateBetween(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

       List<JournalEntryLine> findByJournalEntryId(UUID journalEntryId);

       List<JournalEntryLine> findByChartOfAccountId(UUID chartOfAccountId);

       @Query("SELECT jel FROM JournalEntryLine jel JOIN jel.journalEntry je " +
                     "WHERE jel.chartOfAccount.id = :accountId " +
                     "AND je.entryDate BETWEEN :startDate AND :endDate " +
                     "AND je.postedDate IS NOT NULL " +
                     "ORDER BY je.entryDate ASC, jel.id ASC")
       List<JournalEntryLine> findByChartOfAccountIdAndEntryDateBetween(
                     @Param("accountId") UUID accountId,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       @Query("SELECT COALESCE(SUM(CASE WHEN jel.type = 'DEBIT' THEN jel.amount ELSE -jel.amount END), 0) " +
                     "FROM JournalEntryLine jel JOIN jel.journalEntry je " +
                     "WHERE jel.chartOfAccount.id = :accountId " +
                     "AND je.entryDate <= :date " +
                     "AND je.postedDate IS NOT NULL")
       BigDecimal calculateAccountBalanceAsOfDate(
                     @Param("accountId") UUID accountId,
                     @Param("date") LocalDate date);

       @Query("SELECT COALESCE(SUM(jel.amount), 0) " +
                     "FROM JournalEntryLine jel JOIN jel.journalEntry je " +
                     "WHERE jel.chartOfAccount.id = :accountId " +
                     "AND jel.type = 'DEBIT' " +
                     "AND je.entryDate BETWEEN :startDate AND :endDate " +
                     "AND je.postedDate IS NOT NULL")
       BigDecimal calculateTotalDebitsBetweenDates(
                     @Param("accountId") UUID accountId,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       @Query("SELECT COALESCE(SUM(jel.amount), 0) " +
                     "FROM JournalEntryLine jel JOIN jel.journalEntry je " +
                     "WHERE jel.chartOfAccount.id = :accountId " +
                     "AND jel.type = 'CREDIT' " +
                     "AND je.entryDate BETWEEN :startDate AND :endDate " +
                     "AND je.postedDate IS NOT NULL")
       BigDecimal calculateTotalCreditsBetweenDates(
                     @Param("accountId") UUID accountId,
                     @Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate);

       @Query("SELECT jel FROM JournalEntryLine jel JOIN FETCH jel.journalEntry je JOIN FETCH jel.chartOfAccount coa " +
                     "WHERE je.entryDate BETWEEN :startDate AND :endDate " +
                     "AND (:isPosted IS NULL OR (:isPosted = TRUE AND je.postedDate IS NOT NULL) OR (:isPosted = FALSE AND je.postedDate IS NULL)) " +
                     "AND (:accountIds IS NULL OR coa.id IN :accountIds) " +
                     "ORDER BY coa.id ASC, je.entryDate ASC, jel.id ASC")
      List<JournalEntryLine> findGeneralLedgerLines(@Param("startDate") LocalDate startDate,
                     @Param("endDate") LocalDate endDate,
                     @Param("accountIds") List<UUID> accountIds,
                     @Param("isPosted") Boolean isPosted);
}
