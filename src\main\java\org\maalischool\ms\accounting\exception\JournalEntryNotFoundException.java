package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.util.UUID;

@ResponseStatus(HttpStatus.NOT_FOUND) // 404 Not Found
public class JournalEntryNotFoundException extends RuntimeException {

    public JournalEntryNotFoundException(UUID journalEntryId) {
        super("Journal Entry not found with ID: " + journalEntryId);
    }
}
