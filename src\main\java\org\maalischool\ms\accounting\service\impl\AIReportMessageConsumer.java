package org.maalischool.ms.accounting.service.impl;

import java.util.Map;
import java.util.Optional;

import org.maalischool.ms.accounting.dto.AIReportMessage;
import org.maalischool.ms.accounting.model.AIReport;
import org.maalischool.ms.accounting.model.enums.ReportStatus;
import org.maalischool.ms.accounting.repository.AIReportRepository;
import org.maalischool.ms.accounting.service.ChatGPTService;
import org.maalischool.ms.config.RabbitMQConfig;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Service for consuming AI report messages from RabbitMQ and processing them
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIReportMessageConsumer {

    private final AIReportRepository aiReportRepository;
    private final ChatGPTService chatGPTService;
    private final ObjectMapper objectMapper;
    private final AIReportServiceImpl aiReportService;

    /**
     * Process AI report messages from the queue
     * 
     * @param message The message containing report details
     */
    @RabbitListener(queues = RabbitMQConfig.AI_REPORT_QUEUE)
    @Transactional
    public void processReportMessage(AIReportMessage message) {
        log.info("Received AI report message: reportId={}, type={}", 
                message.getReportId(), message.getReportType());
        
        try {
            // Find the report by ID
            Optional<AIReport> reportOpt = aiReportRepository.findById(message.getReportId());
            
            if (reportOpt.isEmpty()) {
                log.error("Report not found with ID: {}", message.getReportId());
                return;
            }
            
            AIReport report = reportOpt.get();
            
            // Update status to PROCESSING
            report.setStatus(ReportStatus.PROCESSING);
            aiReportRepository.save(report);
            
            try {
                // Generate the report content
                Map<String, Object> reportData = aiReportService.fetchReportData(
                    message.getReportType(),
                    message.getStartDate(),
                    message.getEndDate(),
                    message.getFilters()
                );
                
                String content = chatGPTService.generateReport(
                    message.getReportType(),
                    reportData,
                    message.getAudience()
                );
                
                // Update the report with the generated content
                report.setContent(content);
                report.setStatus(ReportStatus.COMPLETED);
                aiReportRepository.save(report);
                
                log.info("AI report generated successfully: reportId={}", message.getReportId());
            } catch (Exception e) {
                // Update status to FAILED
                report.setStatus(ReportStatus.FAILED);
                aiReportRepository.save(report);
                
                log.error("Error generating AI report: reportId={}, error={}", 
                        message.getReportId(), e.getMessage(), e);
            }
        } catch (Exception e) {
            log.error("Error processing AI report message: reportId={}, error={}", 
                    message.getReportId(), e.getMessage(), e);
        }
    }
}
