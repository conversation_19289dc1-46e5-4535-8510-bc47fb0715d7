package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.AccountingCategory;

import java.util.UUID;

@Data // Ensures getters/setters/etc.
@Builder
public class CreateChartOfAccountRequest {
    @NotBlank(message = "Account number cannot be blank")
    @Size(max = 50, message = "Account number must be less than 50 characters")
    private String accountNumber;

    @NotBlank(message = "English account name cannot be blank")
    @Size(max = 255, message = "English account name must be less than 255 characters")
    private String nameEn;

    @NotBlank(message = "Arabic account name cannot be blank")
    @Size(max = 255, message = "Arabic account name must be less than 255 characters")
    private String nameAr;

    @Size(max = 1000, message = "English description must be less than 1000 characters")
    private String descriptionEn;

    @Size(max = 1000, message = "Arabic description must be less than 1000 characters")
    private String descriptionAr;

    @NotNull(message = "Accounting category cannot be null")
    private AccountingCategory category;

    private UUID parentAccountId;
}
