package org.maalischool.ms.student.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.*;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.LocalDateTime;
import java.util.UUID;

@Entity
@Table(name = "country", uniqueConstraints = {
    @UniqueConstraint(columnNames = "code")
})
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(exclude = {"createdAt", "updatedAt"})
@EntityListeners(AuditingEntityListener.class)
public class Country {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    private UUID id;

    @NotBlank(message = "{validation.country.nameAr.notBlank}")
    @Size(max = 100, message = "{validation.country.nameAr.size}")
    @Column(name = "name_ar", nullable = false, length = 100)
    private String nameAr;

    @NotBlank(message = "{validation.country.nameEn.notBlank}")
    @Size(max = 100, message = "{validation.country.nameEn.size}")
    @Column(name = "name_en", nullable = false, length = 100)
    private String nameEn;

    @NotBlank(message = "{validation.country.code.notBlank}")
    @Size(min = 2, max = 3, message = "{validation.country.code.size}")
    @Column(nullable = false, unique = true, length = 3)
    private String code;

    // --- Auditing Fields ---
    @CreatedDate
    @Column(nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @LastModifiedDate
    @Column(insertable = false)
    private LocalDateTime updatedAt;
}
