package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateDiscountRequest;
import org.maalischool.ms.accounting.dto.DiscountDto;
import org.maalischool.ms.accounting.dto.UpdateDiscountRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing Discounts.
 */
public interface DiscountService {

    /**
     * Creates a new discount.
     *
     * @param request the request object containing discount details.
     * @return the created discount DTO.
     */
    DiscountDto createDiscount(CreateDiscountRequest request);

    /**
     * Retrieves a discount by its ID.
     *
     * @param id the UUID of the discount.
     * @return the discount DTO.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException if the discount is not found.
     */
    DiscountDto getDiscountById(UUID id);

    /**
     * Retrieves all discounts with pagination.
     *
     * @param pageable pagination information.
     * @return a page of discount DTOs.
     */
    Page<DiscountDto> getAllDiscounts(Pageable pageable);

     /**
     * Retrieves all active discounts.
     *
     * @return a list of active discount DTOs.
     */
    List<DiscountDto> getAllActiveDiscounts();

    /**
     * Updates an existing discount.
     *
     * @param id      the UUID of the discount to update.
     * @param request the request object containing updated discount details.
     * @return the updated discount DTO.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException if the discount is not found.
     */
    DiscountDto updateDiscount(UUID id, UpdateDiscountRequest request);

    /**
     * Deletes a discount by its ID.
     * Consider implications if the discount is assigned.
     *
     * @param id the UUID of the discount to delete.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException if the discount is not found.
     */
    void deleteDiscount(UUID id);

    /**
     * Activates a discount.
     *
     * @param id the UUID of the discount to activate.
     * @return the activated discount DTO.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException if the discount is not found.
     */
    DiscountDto activateDiscount(UUID id);

    /**
     * Deactivates a discount.
     *
     * @param id the UUID of the discount to deactivate.
     * @return the deactivated discount DTO.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException if the discount is not found.
     */
    DiscountDto deactivateDiscount(UUID id);
}
