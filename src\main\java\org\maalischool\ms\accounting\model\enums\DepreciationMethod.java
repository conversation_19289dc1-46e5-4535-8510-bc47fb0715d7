package org.maalischool.ms.accounting.model.enums;

/**
 * Represents the method used to calculate depreciation for a fixed asset.
 */
public enum DepreciationMethod {
    STRAIGHT_LINE,          // (Cost - Salvage Value) / Useful Life
    DOUBLE_DECLINING_BALANCE, // Accelerated method
    SUM_OF_YEARS_DIGITS,    // Accelerated method
    UNITS_OF_PRODUCTION     // Based on usage/output
    // Add other methods if needed
}
