package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

import java.math.BigDecimal;
import java.util.UUID;

@ResponseStatus(HttpStatus.BAD_REQUEST) // Overpayment is usually a client error
public class OverpaymentException extends RuntimeException {

    public OverpaymentException(UUID studentFeeId, BigDecimal amountDue, BigDecimal paymentAmount) {
        super(String.format("Overpayment attempt on StudentFee ID %s. Amount currently due: %s, Payment amount: %s",
                studentFeeId, amountDue, paymentAmount));
    }

    public OverpaymentException(String message) {
        super(message);
    }

    public OverpaymentException(String message, Throwable cause) {
        super(message, cause);
    }
}
