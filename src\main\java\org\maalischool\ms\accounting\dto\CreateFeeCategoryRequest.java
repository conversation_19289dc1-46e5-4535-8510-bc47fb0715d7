package org.maalischool.ms.accounting.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateFeeCategoryRequest {
    @NotBlank(message = "English fee category name cannot be blank")
    @Size(max = 100, message = "English fee category name must be less than 100 characters")
    private String nameEn;

    @NotBlank(message = "Arabic fee category name cannot be blank")
    @Size(max = 100, message = "Arabic fee category name must be less than 100 characters")
    private String nameAr;

    @Size(max = 500, message = "English description must be less than 500 characters")
    private String descriptionEn;

    @Size(max = 500, message = "Arabic description must be less than 500 characters")
    private String descriptionAr;
}
