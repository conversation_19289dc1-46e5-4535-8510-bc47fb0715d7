package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.UUID;

import org.maalischool.ms.accounting.model.enums.TransactionType;

import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CreateJournalEntryLineRequest {
    @NotNull(message = "Chart of Account ID cannot be null")
    private UUID chartOfAccountId;

    @NotNull(message = "Transaction type cannot be null")
    private TransactionType type;

    @NotNull(message = "Amount cannot be null")
    @DecimalMin(value = "0.0", inclusive = false, message = "Amount must be positive")
    private BigDecimal amount;

    @Size(max = 500, message = "Description must be less than 500 characters")
    private String description;

    // Optional student ID for partner reference
    private UUID studentId;
}
