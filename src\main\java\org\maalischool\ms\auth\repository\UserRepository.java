package org.maalischool.ms.auth.repository;

import org.maalischool.ms.auth.model.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.UUID;

@Repository
public interface UserRepository extends JpaRepository<User, UUID> {

    Optional<User> findByEmail(String email);

    boolean existsByEmail(String email);

    Optional<User> findByActivationToken(String activationToken);

    Optional<User> findByResetToken(String resetToken);

    /**
     * Finds users belonging to a specific role by the role's name, with pagination.
     * Spring Data JPA derives the query from the method name.
     * It looks for users where the 'name' property within the 'roles' collection matches.
     *
     * @param roleName The name of the role to filter by.
     * @param pageable Pagination and sorting information.
     * @return A Page of users matching the role name.
     */
    Page<User> findByRoles_Name(String roleName, Pageable pageable);
}
