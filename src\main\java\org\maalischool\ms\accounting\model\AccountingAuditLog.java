package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "acc_audit_logs", indexes = {
        @Index(name = "idx_audit_timestamp", columnList = "action_timestamp"),
        @Index(name = "idx_audit_user", columnList = "user_id"),
        @Index(name = "idx_audit_entity", columnList = "entity_type, entity_id")
})
@Getter
@Setter // Setters might not be needed if immutable after creation
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EntityListeners(AuditingEntityListener.class) // To auto-populate actionTimestamp
public class AccountingAuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @CreatedDate // Use @CreatedDate for the timestamp of the log record itself
    @Column(name = "action_timestamp", nullable = false, updatable = false)
    private Instant actionTimestamp;

    @Column(name = "user_id", nullable = false, updatable = false) // Store User UUID directly
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID userId;

    @Enumerated(EnumType.STRING)
    @Column(name = "action_type", nullable = false, updatable = false, length = 50)
    private AccountingActionType actionType;

    @Column(name = "entity_type", updatable = false, length = 100) // e.g., "JournalEntry", "Receipt"
    private String entityType;

    @Column(name = "entity_id", updatable = false) // UUID of the affected entity
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID entityId;

    @Lob // Use Lob for potentially large details string (like JSON diff)
    @JdbcTypeCode(SqlTypes.LONGVARCHAR) // Map to TEXT or similar in DB
    @Column(name = "details", updatable = false)
    private String details; // Can store a summary message or JSON representation of changes

    // Optional: Store IP address, session ID, etc. if needed
    // @Column(name = "ip_address", length = 50)
    // private String ipAddress;
}
