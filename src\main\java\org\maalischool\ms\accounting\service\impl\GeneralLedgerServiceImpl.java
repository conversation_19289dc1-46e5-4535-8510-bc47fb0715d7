package org.maalischool.ms.accounting.service.impl;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import org.maalischool.ms.accounting.dto.GeneralLedgerEntryDto;
import org.maalischool.ms.accounting.mapper.GeneralLedgerMapper;
import org.maalischool.ms.accounting.model.GeneralLedgerView;
import org.maalischool.ms.accounting.repository.GeneralLedgerViewRepository;
import org.maalischool.ms.accounting.service.GeneralLedgerService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.RequiredArgsConstructor;

@Service
@RequiredArgsConstructor
public class GeneralLedgerServiceImpl implements GeneralLedgerService {

    private final GeneralLedgerViewRepository generalLedgerViewRepository;
    private final GeneralLedgerMapper generalLedgerMapper;

    @Override
    @Transactional(readOnly = true)
    public Page<GeneralLedgerEntryDto> getGeneralLedger(LocalDate startDate, LocalDate endDate, List<UUID> accountIds,
            Boolean isPosted, Pageable pageable) {

        // Get all accounts that have transactions in the period
        List<UUID> activeAccountIds = generalLedgerViewRepository.findDistinctAccountIds(startDate, endDate, accountIds, isPosted);

        // Calculate opening balances for each account
        Map<UUID, BigDecimal> openingBalances = new HashMap<>();
        for (UUID accountId : activeAccountIds) {
            BigDecimal openingBalance = generalLedgerViewRepository.calculateOpeningBalance(accountId, startDate);
            openingBalances.put(accountId, openingBalance);
        }

        // Fetch all relevant entries from the view
        List<GeneralLedgerView> ledgerEntries = generalLedgerViewRepository.findGeneralLedgerEntries(startDate, endDate,
                accountIds, isPosted);

        // Convert to DTOs and set cumulative balances
        List<GeneralLedgerEntryDto> dtoList = new ArrayList<>();
        UUID currentAccountId = null;
        BigDecimal cumulativeBalance = null;

        for (GeneralLedgerView entry : ledgerEntries) {
            // If we're starting a new account, get its opening balance
            if (currentAccountId == null || !currentAccountId.equals(entry.getChartOfAccountId())) {
                currentAccountId = entry.getChartOfAccountId();
                cumulativeBalance = openingBalances.getOrDefault(currentAccountId, BigDecimal.ZERO);
            }

            // Map the view to DTO
            GeneralLedgerEntryDto dto = generalLedgerMapper.toDto(entry);

            // Set the cumulative balance (opening balance at the start of the period)
            dto.setCumulativeBalance(cumulativeBalance);

            dtoList.add(dto);
        }

        // Manual pagination
        int start = (int) pageable.getOffset();
        int end = Math.min((start + pageable.getPageSize()), dtoList.size());

        // Handle empty list case
        if (dtoList.isEmpty()) {
            return new PageImpl<>(new ArrayList<>(), pageable, 0);
        }

        List<GeneralLedgerEntryDto> pagedList = dtoList.subList(start, end);

        return new PageImpl<>(pagedList, pageable, dtoList.size());
    }
}
