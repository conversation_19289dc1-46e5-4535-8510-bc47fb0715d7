package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.student.model.Student; // Import Student
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "acc_discounts", uniqueConstraints = {
        @UniqueConstraint(columnNames = "code")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class Discount {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(nullable = false, unique = true, length = 50)
    private String code;

    @Column(nullable = false, length = 255)
    private String descriptionEn;
    @Column(nullable = false, length = 255)
    private String descriptionAr;

    @Enumerated(EnumType.STRING) // Add the type field
    @Column(nullable = false, length = 30)
    private org.maalischool.ms.accounting.model.enums.DiscountType type; // Assuming enum exists

    @Column(precision = 5, scale = 2) // e.g., 10.50%
    private BigDecimal percentage; // Nullable if fixed amount

    @Column(name = "fixed_amount", precision = 19, scale = 4)
    private BigDecimal fixedAmount; // Nullable if percentage

    @Column(name = "valid_from")
    private LocalDate validFrom;

    @Column(name = "valid_until")
    private LocalDate validUntil;

    // Applicability Rules
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicable_fee_id") // Can apply to a specific Fee type
    private Fee applicableFee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "applicable_student_id") // Can apply to a specific Student
    private Student applicableStudent;

    // Could add applicability for Grade, Category, etc.

    @Column(name = "is_active", nullable = false)
    private boolean active = true;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
