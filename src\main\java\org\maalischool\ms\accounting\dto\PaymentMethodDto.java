package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.PaymentMethodType;

import java.time.Instant;
import java.util.UUID;

@Data
@Builder
public class PaymentMethodDto {
    private UUID id;
    private String nameEn;
    private String nameAr;
    private PaymentMethodType type;
    private String descriptionEn; // Renamed from details to match model
    private String descriptionAr;
    private boolean active;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
