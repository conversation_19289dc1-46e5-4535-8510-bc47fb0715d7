package org.maalischool.ms.accounting.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.maalischool.ms.accounting.dto.ExpenseDto;
import org.maalischool.ms.accounting.dto.ExpenseReportDto;
import org.maalischool.ms.accounting.dto.ExpenseReportSummaryDto;
import org.maalischool.ms.accounting.model.Expense;
import org.maalischool.ms.accounting.repository.ExpenseRepository;
import org.maalischool.ms.accounting.service.ExpenseReportService;
import org.maalischool.ms.accounting.service.ExpenseService;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * Implementation of ExpenseReportService for generating expense reports
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class ExpenseReportServiceImpl implements ExpenseReportService {

    private final ExpenseRepository expenseRepository;
    private final ExpenseService expenseService;

    @Override
    public ExpenseReportDto generateExpenseReport(LocalDate startDate, LocalDate endDate) {
        log.info("Generating expense report for period: {} to {}", startDate, endDate);
        
        // Fetch all expenses within the date range
        List<Expense> expenses = expenseRepository.findByExpenseDateBetweenOrderByExpenseDateDesc(startDate, endDate);

        if (expenses.isEmpty()) {
            return ExpenseReportDto.builder()
                    .startDate(startDate)
                    .endDate(endDate)
                    .expenses(new ArrayList<>())
                    .categorySummaries(new ArrayList<>())
                    .totalAmount(BigDecimal.ZERO)
                    .totalTaxAmount(BigDecimal.ZERO)
                    .totalAmountWithTax(BigDecimal.ZERO)
                    .totalExpenseCount(0)
                    .build();
        }

        // Convert expenses to DTOs
        List<ExpenseDto> expenseDtos = new ArrayList<>();
        for (Expense expense : expenses) {
            expenseDtos.add(expenseService.getExpenseById(expense.getId()));
        }

        // Calculate totals and category summaries
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal totalTaxAmount = BigDecimal.ZERO;
        BigDecimal totalAmountWithTax = BigDecimal.ZERO;
        
        // Group expenses by category
        Map<UUID, ExpenseReportSummaryDto.ExpenseReportSummaryDtoBuilder> categorySummaryBuilders = new HashMap<>();
        
        for (ExpenseDto expense : expenseDtos) {
            // Add to totals
            totalAmount = totalAmount.add(expense.getAmountBeforeTax());
            totalTaxAmount = totalTaxAmount.add(expense.getTaxAmount());
            totalAmountWithTax = totalAmountWithTax.add(expense.getTotalAmount());
            
            // Get or create category summary builder
            UUID categoryId = expense.getCategory().getId();
            ExpenseReportSummaryDto.ExpenseReportSummaryDtoBuilder summaryBuilder = categorySummaryBuilders.computeIfAbsent(
                    categoryId,
                    id -> ExpenseReportSummaryDto.builder()
                            .categoryId(id)
                            .categoryNameEn(expense.getCategory().getNameEn())
                            .categoryNameAr(expense.getCategory().getNameAr())
                            .totalAmount(BigDecimal.ZERO)
                            .totalTaxAmount(BigDecimal.ZERO)
                            .totalAmountWithTax(BigDecimal.ZERO)
                            .expenseCount(0)
            );
            
            // Update category summary
            summaryBuilder.totalAmount(summaryBuilder.build().getTotalAmount().add(expense.getAmountBeforeTax()));
            summaryBuilder.totalTaxAmount(summaryBuilder.build().getTotalTaxAmount().add(expense.getTaxAmount()));
            summaryBuilder.totalAmountWithTax(summaryBuilder.build().getTotalAmountWithTax().add(expense.getTotalAmount()));
            summaryBuilder.expenseCount(summaryBuilder.build().getExpenseCount() + 1);
        }
        
        // Calculate percentages and build final summaries
        List<ExpenseReportSummaryDto> categorySummaries = new ArrayList<>();
        for (ExpenseReportSummaryDto.ExpenseReportSummaryDtoBuilder builder : categorySummaryBuilders.values()) {
            BigDecimal percentage = BigDecimal.ZERO;
            if (totalAmountWithTax.compareTo(BigDecimal.ZERO) > 0) {
                percentage = builder.build().getTotalAmountWithTax()
                        .multiply(new BigDecimal("100"))
                        .divide(totalAmountWithTax, 2, RoundingMode.HALF_UP);
            }
            builder.percentageOfTotal(percentage);
            categorySummaries.add(builder.build());
        }
        
        // Sort category summaries by total amount (descending)
        categorySummaries.sort((a, b) -> b.getTotalAmountWithTax().compareTo(a.getTotalAmountWithTax()));
        
        // Build and return the final report
        return ExpenseReportDto.builder()
                .startDate(startDate)
                .endDate(endDate)
                .expenses(expenseDtos)
                .categorySummaries(categorySummaries)
                .totalAmount(totalAmount)
                .totalTaxAmount(totalTaxAmount)
                .totalAmountWithTax(totalAmountWithTax)
                .totalExpenseCount(expenses.size())
                .build();
    }
}
