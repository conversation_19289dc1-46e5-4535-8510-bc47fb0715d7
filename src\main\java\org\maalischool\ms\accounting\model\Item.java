package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.ItemType;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Entity
@Table(name = "acc_items", uniqueConstraints = {
        @UniqueConstraint(columnNames = "item_code")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class Item { // Represents school supplies, inventory items

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "item_code", nullable = false, unique = true, length = 50)
    private String itemCode; // Renamed from sku

    @Column(nullable = false, length = 255)
    private String nameEn;
    @Column(nullable = false, length = 255)
    private String nameAr;

    @Column(length = 1000)
    private String descriptionEn;
    @Column(length = 1000)
    private String descriptionAr;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 30)
    private ItemType type;

    @Column(name = "unit_of_measure", nullable = false, length = 20) // e.g., "pcs", "box", "ream"
    private String unitOfMeasure;

    @Column(name = "quantity_on_hand", nullable = false, precision = 19, scale = 4)
    @Builder.Default
    private BigDecimal quantityOnHand = BigDecimal.ZERO; // Added quantity field

    // Optional: Link to preferred supplier, default cost, reorder level etc.
    // currentStockLevel should ideally be calculated from InventoryTransactions, not stored directly here (Note: Field added above to match service logic)
    // to avoid data inconsistency. It can be added as a @Transient field populated by the service if needed.
    // @Transient
    // private Integer currentStockLevel;

    @Column(name = "reorder_level")
    private Integer reorderLevel;

    @Column(name = "last_cost", precision = 19, scale = 4)
    private BigDecimal lastCost; // Last purchase cost

    // Optional: Link to ChartOfAccount for inventory asset / expense
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "inventory_account_id")
    private ChartOfAccount inventoryAccount; // If tracked as asset

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "expense_account_id")
    private ChartOfAccount expenseAccount; // If expensed on purchase/use

    @Column(name = "is_active", nullable = false)
    private boolean active = true;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @LastModifiedDate
    @Column(name = "last_modified_date", nullable = false)
    private Instant lastModifiedDate;
}
