package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.AssignFeeRequest;
import org.maalischool.ms.accounting.dto.StudentFeeDto;
import org.maalischool.ms.accounting.dto.UpdateStudentFeeRequest;
import org.maalischool.ms.accounting.model.enums.FeeStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

/**
 * Service interface for managing fees assigned to students.
 */
public interface StudentFeeService {

    /**
     * Assigns a specific fee to a student.
     *
     * @param request Details of the fee assignment.
     * @return The created StudentFee DTO.
     * @throws org.maalischool.ms.student.exception.StudentNotFoundException if the
     *                                                                       student
     *                                                                       is not
     *                                                                       found.
     * @throws org.maalischool.ms.accounting.exception.FeeNotFoundException  if the
     *                                                                       fee is
     *                                                                       not
     *                                                                       found.
     */
    StudentFeeDto assignFeeToStudent(AssignFeeRequest request);

    /**
     * Assigns multiple applicable fees to a student based on their context (grade,
     * stage, branch, year).
     * Skips fees already assigned to the student for the given academic year.
     *
     * @param studentId      The UUID of the student.
     * @param academicYearId The academic year ID for which to assign fees.
     * @return A list of newly created StudentFee DTOs.
     * @throws org.maalischool.ms.student.exception.StudentNotFoundException if the
     *                                                                       student
     *                                                                       is not
     *                                                                       found.
     */
    List<StudentFeeDto> assignApplicableFeesToStudent(UUID studentId, UUID academicYearId);

    /**
     * Retrieves a specific student fee assignment by its ID.
     *
     * @param studentFeeId The UUID of the student fee record.
     * @return The StudentFee DTO.
     * @throws org.maalischool.ms.accounting.exception.StudentFeeNotFoundException if
     *                                                                             the
     *                                                                             record
     *                                                                             is
     *                                                                             not
     *                                                                             found.
     */
    StudentFeeDto getStudentFeeById(UUID studentFeeId);

    /**
     * Retrieves all fees assigned to a specific student for a given academic year.
     *
     * @param studentId      The UUID of the student.
     * @param academicYearId The academic year ID.
     * @param pageable       Pagination information.
     * @return A page of StudentFee DTOs.
     */
    Page<StudentFeeDto> getFeesForStudent(UUID studentId, UUID academicYearId, Pageable pageable);

    /**
     * Retrieves all student fee assignments with a specific status for a given
     * academic year.
     *
     * @param academicYearId The academic year ID.
     * @param status         The fee status to filter by.
     * @param pageable       Pagination information.
     * @return A page of StudentFee DTOs.
     */
    Page<StudentFeeDto> getStudentFeesByStatus(UUID academicYearId, FeeStatus status, Pageable pageable);

    /**
     * Retrieves all student fee assignments for a specific fee ID.
     *
     * @param feeId    The UUID of the fee.
     * @param pageable Pagination information.
     * @return A page of StudentFee DTOs.
     */
    Page<StudentFeeDto> getStudentFeesByFeeId(UUID feeId, Pageable pageable);

    /**
     * Updates details of a student fee assignment (e.g., applied discount, notes).
     * Recalculates amount due based on changes.
     *
     * @param studentFeeId The UUID of the student fee record to update.
     * @param request      The request object containing updated details.
     * @return The updated StudentFee DTO.
     * @throws org.maalischool.ms.accounting.exception.StudentFeeNotFoundException if
     *                                                                             the
     *                                                                             record
     *                                                                             is
     *                                                                             not
     *                                                                             found.
     * @throws org.maalischool.ms.accounting.exception.DiscountNotFoundException   if
     *                                                                             a
     *                                                                             discount
     *                                                                             ID
     *                                                                             in
     *                                                                             the
     *                                                                             request
     *                                                                             is
     *                                                                             invalid.
     */
    StudentFeeDto updateStudentFee(UUID studentFeeId, UpdateStudentFeeRequest request);

    /**
     * Deletes a student fee assignment. Use with caution, consider implications on
     * payment records.
     * Typically, fees should be marked as waived or cancelled rather than deleted
     * if payments exist.
     *
     * @param studentFeeId The UUID of the student fee record to delete.
     * @throws org.maalischool.ms.accounting.exception.StudentFeeNotFoundException if
     *                                                                             the
     *                                                                             record
     *                                                                             is
     *                                                                             not
     *                                                                             found.
     */
    void deleteStudentFeeAssignment(UUID studentFeeId);

    /**
     * Finds student fee assignments based on various filter criteria.
     *
     * @param studentId    Optional filter by student UUID.
     * @param feeId        Optional filter by fee definition UUID.
     * @param status       Optional filter by fee status.
     * @param dueDateStart Optional filter for due date start (inclusive).
     * @param dueDateEnd   Optional filter for due date end (inclusive).
     * @param pageable     Pagination information.
     * @return A page of matching StudentFee DTOs.
     */
    // Use simple names, ensure imports are correct in implementing classes
    Page<StudentFeeDto> findStudentFees(
            UUID studentId,
            UUID feeId,
            FeeStatus status,
            LocalDate dueDateStart,
            LocalDate dueDateEnd,
            Pageable pageable);

    // Potential future methods:
    // - Apply payment to a specific student fee
    // - Waive a student fee
    // - Bulk assign fees to a class/section
}
