package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.dto.SimpleTaxDto; // Add import

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
public class ExpenseDto {
    private UUID id;
    private LocalDate expenseDate;
    private BigDecimal amountBeforeTax; // Renamed from amount
    private BigDecimal taxAmount;       // Added tax amount
    private BigDecimal totalAmount;     // Added total amount (calculated in service)
    private String descriptionEn;
    private String descriptionAr;
    private String vendor;
    private String referenceNumber;
    private SimpleExpenseCategoryDto category; // Use Simple DTO
    private SimpleTaxDto tax; // Added simple tax DTO
    private SimpleChartOfAccountDto paymentAccount; // Use Simple DTO
    private Instant createdDate;
    private Instant lastModifiedDate;
}
