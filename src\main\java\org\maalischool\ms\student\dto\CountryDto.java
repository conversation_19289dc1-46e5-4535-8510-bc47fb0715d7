package org.maalischool.ms.student.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * Response DTO for Country.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountryDto {
    private UUID id;
    private String nameAr;
    private String nameEn;
    private String code;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
