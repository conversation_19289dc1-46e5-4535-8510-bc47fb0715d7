package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.AccountingAuditLogDto; // Import DTO
import org.maalischool.ms.accounting.model.enums.AccountingActionType;
import org.springframework.data.domain.Page; // Import Page
import org.springframework.data.domain.Pageable; // Import Pageable

import java.time.Instant; // Import Instant
import java.util.List; // Import List
import java.util.UUID;

/**
 * Service responsible for logging accounting-related actions.
 */
public interface AccountingAuditLogService {

    /**
     * Logs an accounting action.
     *
     * @param userId      The ID of the user performing the action.
     * @param actionType  The type of action performed.
     * @param entityType  The simple class name of the entity affected (e.g., "JournalEntry"). Can be null if not entity-specific.
     * @param entityId    The ID of the entity affected. Can be null if not entity-specific.
     * @param details     A description or JSON representation of the action/changes.
     */
    void logAction(UUID userId, AccountingActionType actionType, String entityType, UUID entityId, String details);

    /**
     * Finds audit logs based on optional filter criteria.
     *
     * @param userId     Optional user ID to filter by.
     * @param entityType Optional entity type to filter by.
     * @param entityId   Optional entity ID to filter by.
     * @param startTime  Optional start timestamp (inclusive).
     * @param endTime    Optional end timestamp (inclusive).
     * @param pageable   Pagination information.
     * @return A page of AccountingAuditLogDto.
     */
    Page<AccountingAuditLogDto> findAuditLogs(UUID userId, String entityType, UUID entityId, Instant startTime, Instant endTime, Pageable pageable);

    /**
     * Finds all audit logs for a specific entity type and ID, ordered by timestamp descending.
     *
     * @param entityType The entity type to filter by.
     * @param entityId   The entity ID to filter by.
     * @return A list of AccountingAuditLogDto.
     */
    List<AccountingAuditLogDto> findAllByEntityTypeAndEntityId(String entityType, UUID entityId);
}
