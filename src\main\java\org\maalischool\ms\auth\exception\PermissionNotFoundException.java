package org.maalischool.ms.auth.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.NOT_FOUND) // 404 Not Found
public class PermissionNotFoundException extends RuntimeException {
    public PermissionNotFoundException(String name) {
        super("Permission not found with name: " + name);
    }
     public PermissionNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
