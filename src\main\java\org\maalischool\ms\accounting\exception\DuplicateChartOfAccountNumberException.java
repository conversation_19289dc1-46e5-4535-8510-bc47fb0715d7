package org.maalischool.ms.accounting.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

@ResponseStatus(HttpStatus.CONFLICT) // 409 Conflict
public class DuplicateChartOfAccountNumberException extends RuntimeException {

    public DuplicateChartOfAccountNumberException(String accountNumber) {
        super("Chart of Account number already exists: " + accountNumber);
    }
}
