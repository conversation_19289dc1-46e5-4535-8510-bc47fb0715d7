package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import org.maalischool.ms.accounting.model.enums.ItemType;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Data
@Builder
public class ItemDto {
    private UUID id;
    private String itemCode; // Already correct
    private String nameEn;
    private String nameAr;
    private String descriptionEn;
    private String descriptionAr;
    private ItemType type;
    private String unitOfMeasure;
    private BigDecimal costPrice;
    private BigDecimal sellingPrice;
    private SimpleChartOfAccountDto inventoryAccount;
    private SimpleChartOfAccountDto cogsAccount;
    private SimpleChartOfAccountDto salesAccount;
    private Integer reorderLevel;
    private String supplierInfo;
    private boolean active;
    // Current stock could be added by service
    // private Integer currentStock;
    private Instant createdDate;
    private Instant lastModifiedDate;
}
