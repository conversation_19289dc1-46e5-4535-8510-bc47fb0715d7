package org.maalischool.ms.accounting.model;

import jakarta.persistence.*;
import lombok.*;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.InventoryTransactionType;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Entity
@Table(name = "acc_inventory_transactions", indexes = {
        @Index(name = "idx_invtxn_item", columnList = "item_id"),
        @Index(name = "idx_invtxn_date", columnList = "transaction_date")
})
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = "id")
@EntityListeners(AuditingEntityListener.class)
public class InventoryTransaction {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @JoinColumn(name = "item_id", nullable = false)
    private Item item;

    @Column(name = "transaction_date", nullable = false)
    private LocalDate transactionDate;

    @Enumerated(EnumType.STRING)
    @Column(name = "transaction_type", nullable = false, length = 30)
    private InventoryTransactionType transactionType;

    @Column(name = "quantity_change", nullable = false, precision = 19, scale = 4) // Renamed and changed type
    private BigDecimal quantityChange; // Always positive, type indicates direction

    @Column(name = "quantity_after_transaction", nullable = false, precision = 19, scale = 4) // Added field
    private BigDecimal quantityAfterTransaction; // Stock level after this transaction

    @Column(name = "unit_cost", precision = 19, scale = 4) // Cost at the time of transaction
    private BigDecimal unitCost;

    @Column(name = "related_document_ref", length = 100) // e.g., PO number, Issue Slip ID, Adjustment ID
    private String relatedDocumentRef;

    @Column(length = 500)
    private String notes;

    @CreatedDate
    @Column(name = "created_date", nullable = false, updatable = false)
    private Instant createdDate;

    @CreatedBy
    @Column(name = "created_by", updatable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID createdBy;
}
