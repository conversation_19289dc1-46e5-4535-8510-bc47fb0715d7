package org.maalischool.ms.accounting.repository;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

import org.maalischool.ms.accounting.model.AIReport;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface AIReportRepository extends JpaRepository<AIReport, UUID> {

    /**
     * Find a report by its type, date range, and filters
     *
     * @param reportType The type of report
     * @param startDate The start date of the report period
     * @param endDate The end date of the report period
     * @param filters JSON string containing additional filters
     * @return An Optional containing the AIReport if found
     */
    Optional<AIReport> findByReportTypeAndStartDateAndEndDateAndFilters(
            String reportType,
            LocalDate startDate,
            LocalDate endDate,
            String filters);

    /**
     * Find all reports by type with pagination
     *
     * @param reportType The type of report to filter by (optional)
     * @param pageable Pagination information
     * @return A Page of AIReport entities
     */
    Page<AIReport> findByReportTypeOrderByCreatedDateDesc(String reportType, Pageable pageable);

    /**
     * Find all reports with pagination
     *
     * @param pageable Pagination information
     * @return A Page of AIReport entities
     */
    Page<AIReport> findAllByOrderByCreatedDateDesc(Pageable pageable);
}
