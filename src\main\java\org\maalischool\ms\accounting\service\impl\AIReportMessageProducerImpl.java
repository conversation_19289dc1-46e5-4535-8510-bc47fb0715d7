package org.maalischool.ms.accounting.service.impl;

import org.maalischool.ms.accounting.dto.AIReportMessage;
import org.maalischool.ms.accounting.service.AIReportMessageProducer;
import org.maalischool.ms.config.RabbitMQConfig;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Implementation of AIReportMessageProducer for sending AI report messages to RabbitMQ
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AIReportMessageProducerImpl implements AIReportMessageProducer {

    private final RabbitTemplate rabbitTemplate;

    @Override
    public void sendReportMessage(AIReportMessage message) {
        log.info("Sending AI report message to queue: reportId={}, type={}", 
                message.getReportId(), message.getReportType());
        
        rabbitTemplate.convertAndSend(
            RabbitMQConfig.EXCHANGE_NAME,
            RabbitMQConfig.AI_REPORT_ROUTING_KEY,
            message
        );
        
        log.debug("AI report message sent successfully: reportId={}", message.getReportId());
    }
}
