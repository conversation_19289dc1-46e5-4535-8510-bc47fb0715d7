package org.maalischool.ms.accounting.model;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

import org.hibernate.annotations.Immutable;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;
import org.maalischool.ms.accounting.model.enums.TransactionType;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Id;
import jakarta.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * Entity representing the general_ledger_view database view.
 * This is a read-only entity mapped to a database view.
 */
@Entity
@Immutable // This is a view, not a table
@Table(name = "general_ledger_view")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString(exclude = { "postedDate" })
public class GeneralLedgerView {

    @Id
    @Column(updatable = false, nullable = false)
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID id;

    @Column(name = "entry_date")
    private LocalDate entryDate;

    @Column(name = "journal_entry_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID journalEntryId;

    @Column(name = "journal_description")
    private String journalDescription;

    @Column(name = "reference_number")
    private String referenceNumber;

    @Column(name = "posted_date")
    private Instant postedDate;

    @Column(name = "chart_of_account_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID chartOfAccountId;

    @Column(name = "account_number")
    private String accountNumber;

    @Column(name = "account_name_en")
    private String accountNameEn;

    @Column(name = "account_name_ar")
    private String accountNameAr;

    @Enumerated(EnumType.STRING)
    @Column(nullable = false, length = 10) // DEBIT or CREDIT
    private TransactionType type;

    @Column(nullable = false, precision = 19, scale = 4)
    private BigDecimal amount;

    @Column(name = "line_description")
    private String lineDescription;

    @Column(name = "student_id")
    @JdbcTypeCode(SqlTypes.VARCHAR)
    private UUID studentId;

    @Column(name = "student_admission_number")
    private String studentAdmissionNumber;

    @Column(name = "student_first_name")
    private String studentFirstName;

    @Column(name = "student_last_name")
    private String studentLastName;

    @Column(name = "debit_amount", precision = 19, scale = 4)
    private BigDecimal debitAmount;

    @Column(name = "credit_amount", precision = 19, scale = 4)
    private BigDecimal creditAmount;

    @Column(name = "running_balance", precision = 19, scale = 4)
    private BigDecimal runningBalance;
}
