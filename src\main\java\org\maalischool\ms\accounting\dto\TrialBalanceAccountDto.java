package org.maalischool.ms.accounting.dto;

import java.math.BigDecimal;
import java.util.Objects;

public class TrialBalanceAccountDto {

    private String accountCode;
    private String accountName;
    private BigDecimal debitBalance;
    private BigDecimal creditBalance;

    public TrialBalanceAccountDto() {
    }

    public TrialBalanceAccountDto(String accountCode, String accountName, BigDecimal debitBalance, BigDecimal creditBalance) {
        this.accountCode = accountCode;
        this.accountName = accountName;
        this.debitBalance = debitBalance;
        this.creditBalance = creditBalance;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public String getAccountName() {
        return accountName;
    }

    public void setAccountName(String accountName) {
        this.accountName = accountName;
    }

    public BigDecimal getDebitBalance() {
        return debitBalance;
    }

    public void setDebitBalance(BigDecimal debitBalance) {
        this.debitBalance = debitBalance;
    }

    public BigDecimal getCreditBalance() {
        return creditBalance;
    }

    public void setCreditBalance(BigDecimal creditBalance) {
        this.creditBalance = creditBalance;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        TrialBalanceAccountDto that = (TrialBalanceAccountDto) o;
        return Objects.equals(accountCode, that.accountCode) && Objects.equals(accountName, that.accountName) && Objects.equals(debitBalance, that.debitBalance) && Objects.equals(creditBalance, that.creditBalance);
    }

    @Override
    public int hashCode() {
        return Objects.hash(accountCode, accountName, debitBalance, creditBalance);
    }

    @Override
    public String toString() {
        return "TrialBalanceAccountDto{" +
               "accountCode='" + accountCode + '\'' +
               ", accountName='" + accountName + '\'' +
               ", debitBalance=" + debitBalance +
               ", creditBalance=" + creditBalance +
               '}';
    }
}
