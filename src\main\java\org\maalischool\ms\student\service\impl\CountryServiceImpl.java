package org.maalischool.ms.student.service.impl;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.maalischool.ms.student.dto.CountryDto;
import org.maalischool.ms.student.dto.SimpleCountryDto;
import org.maalischool.ms.student.model.Country;
import org.maalischool.ms.student.repository.CountryRepository;
import org.maalischool.ms.student.service.CountryService;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class CountryServiceImpl implements CountryService {

    private final CountryRepository countryRepository;

    @Override
    @Transactional(readOnly = true)
    public Optional<CountryDto> getCountryById(UUID countryId) {
        log.debug("Fetching country by ID: {}", countryId);
        return countryRepository.findById(countryId).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CountryDto> getCountryByCode(String code) {
        log.debug("Fetching country by code: {}", code);
        return countryRepository.findByCodeIgnoreCase(code).map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<CountryDto> getAllCountries() {
        log.debug("Fetching all countries");
        return countryRepository.findAll().stream()
                .map(this::mapToDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CountryDto> searchCountries(String keyword, Pageable pageable) {
        log.debug("Searching countries with keyword: '{}', page: {}, size: {}, sort: {}",
                keyword, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        return countryRepository.findByKeyword(keyword, pageable)
                .map(this::mapToDto);
    }

    @Override
    @Transactional(readOnly = true)
    public boolean existsByCode(String code) {
        log.debug("Checking if country exists by code: {}", code);
        return countryRepository.existsByCode(code);
    }

    /**
     * Maps Country entity to CountryDto.
     */
    private CountryDto mapToDto(Country country) {
        return CountryDto.builder()
                .id(country.getId())
                .nameAr(country.getNameAr())
                .nameEn(country.getNameEn())
                .code(country.getCode())
                .createdAt(country.getCreatedAt())
                .updatedAt(country.getUpdatedAt())
                .build();
    }

    /**
     * Maps Country entity to SimpleCountryDto.
     */
    public SimpleCountryDto mapToSimpleDto(Country country) {
        if (country == null) {
            return null;
        }
        return SimpleCountryDto.builder()
                .id(country.getId())
                .nameAr(country.getNameAr())
                .nameEn(country.getNameEn())
                .code(country.getCode())
                .build();
    }
}
