package org.maalischool.ms.accounting.service;

import org.maalischool.ms.accounting.dto.CreateJournalEntryRequest;
import org.maalischool.ms.accounting.dto.JournalEntryDto;
import org.maalischool.ms.accounting.dto.UpdateJournalEntryRequest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.util.Optional;
import java.util.UUID;

public interface JournalEntryService {

    JournalEntryDto createJournalEntry(CreateJournalEntryRequest request);

    Optional<JournalEntryDto> getJournalEntryById(UUID id);

    Page<JournalEntryDto> findJournalEntries(LocalDate startDate, LocalDate endDate, String referenceNumber, Boolean isPosted, Pageable pageable);

    JournalEntryDto updateJournalEntry(UUID id, UpdateJournalEntryRequest request);

    JournalEntryDto postJournalEntry(UUID id);

    void deleteJournalEntry(UUID id); // Only if not posted? Business rule needed.
}
