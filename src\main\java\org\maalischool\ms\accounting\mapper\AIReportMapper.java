package org.maalischool.ms.accounting.mapper;

import java.util.HashMap;
import java.util.Map;

import org.maalischool.ms.accounting.dto.AIReportResponse;
import org.maalischool.ms.accounting.model.AIReport;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * Mapper for converting between AIReport entity and DTOs
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class AIReportMapper {

    private final ObjectMapper objectMapper;

    /**
     * Convert an AIReport entity to an AIReportResponse DTO
     *
     * @param report The AIReport entity
     * @param fromCache Whether the report was retrieved from cache
     * @return The AIReportResponse DTO
     */
    public AIReportResponse toDto(AIReport report, boolean fromCache) {
        // Deserialize filters JSON
        Map<String, Object> filters;
        try {
            filters = (report.getFilters() != null && !report.getFilters().isEmpty())
                ? objectMapper.readValue(report.getFilters(), Map.class)
                : new HashMap<>();
        } catch (JsonProcessingException e) {
            log.error("Error deserializing filters JSON", e);
            filters = new HashMap<>();
        }

        return AIReportResponse.builder()
                .id(report.getId())
                .reportType(report.getReportType())
                .startDate(report.getStartDate())
                .endDate(report.getEndDate())
                .filters(filters)
                .content(report.getContent())
                .status(report.getStatus())
                .fromCache(fromCache)
                .createdDate(report.getCreatedDate())
                .lastModifiedDate(report.getLastModifiedDate())
                .build();
    }
}
