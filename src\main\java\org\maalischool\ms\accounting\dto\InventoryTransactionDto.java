package org.maalischool.ms.accounting.dto;

import lombok.Builder;
import lombok.Data;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.maalischool.ms.accounting.model.enums.InventoryTransactionType;

import java.math.BigDecimal; // Import BigDecimal
import java.time.Instant;
import java.time.LocalDate;
import java.util.UUID;

@Data
@Builder
@NoArgsConstructor // Add constructors for flexibility
@AllArgsConstructor
public class InventoryTransactionDto {
    private UUID id;
    private SimpleItemDto item; // Use Simple DTO
    private LocalDate transactionDate;
    private InventoryTransactionType transactionType; // Renamed from 'type' for clarity
    private BigDecimal quantityChange; // Changed type to BigDecimal, renamed from 'quantity'
    private BigDecimal quantityAfterTransaction; // Added field
    private BigDecimal unitCost; // Added field (was missing but present in service mapping)
    private String notes; // Added field (was missing but present in service mapping)
    private String referenceId; // Renamed from referenceNumber/reason for consistency
    private Instant createdDate;
}
